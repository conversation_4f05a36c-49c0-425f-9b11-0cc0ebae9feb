// 🔥 REAL CALL MANAGER HOOK - COMPLETE WEBRTC IMPLEMENTATION
// No mockups, no fake data - 100% real WebRTC calling functionality

import { useState, useEffect, useRef, useCallback } from 'react';
import { Audio } from 'expo-av';
// import * as Notifications from 'expo-notifications'; // Removed - push notifications disabled
import * as Haptics from 'expo-haptics';
import { MediaStream } from 'react-native-webrtc';
import { realCallService, RealCall, CallType } from '../services/realCallService';
import { getCurrentUser } from '../services/authService';
import { User } from '../types';

interface CallState {
  currentCall: RealCall | null;
  localStream: MediaStream | null;
  remoteStream: MediaStream | null;
  isConnected: boolean;
  isMuted: boolean;
  isVideoEnabled: boolean;
  isSpeakerOn: boolean;
  callDuration: number;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'connecting';
}

interface CallPermissions {
  camera: boolean;
  microphone: boolean;
  notifications: boolean;
}

export const useRealCallManager = () => {
  // ==================== REAL STATE MANAGEMENT ====================
  
  const [callState, setCallState] = useState<CallState>({
    currentCall: null,
    localStream: null,
    remoteStream: null,
    isConnected: false,
    isMuted: false,
    isVideoEnabled: false,
    isSpeakerOn: false,
    callDuration: 0,
    connectionQuality: 'connecting',
  });

  const [permissions, setPermissions] = useState<CallPermissions>({
    camera: false,
    microphone: false,
    notifications: false,
  });

  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Refs for cleanup
  const callListenerUnsubscribe = useRef<(() => void) | null>(null);
  const durationInterval = useRef<ReturnType<typeof setInterval> | null>(null);
  const qualityCheckInterval = useRef<ReturnType<typeof setInterval> | null>(null);

  // ==================== CLEANUP ====================

  const cleanup = useCallback(() => {
    console.log('🧹 Cleaning up call manager...');

    if (callListenerUnsubscribe.current) {
      callListenerUnsubscribe.current();
    }

    stopCallDurationTimer();
    stopQualityMonitoring();
  }, []);

  const setupCallListeners = useCallback(() => {
    try {
      console.log('🔥 Setting up real call listeners...');

      const unsubscribe = realCallService.addCallListener((call) => {
        if (call) {
          console.log('🔥 Call state updated:', call.status);

          setCallState(prev => ({
            ...prev,
            currentCall: call,
            isConnected: call.status === 'connected',
          }));

          // Handle call status changes
          handleCallStatusChange(call);
        } else {
          setCallState(prev => ({
            ...prev,
            currentCall: null,
            isConnected: false,
          }));

          stopCallDurationTimer();
          stopQualityMonitoring();
        }
      });

      callListenerUnsubscribe.current = unsubscribe;
    } catch (error) {
      console.error('❌ Error setting up call listeners:', error);
    }
  }, []);

  const initializeCallManager = useCallback(async () => {
    try {
      console.log('🔥 Initializing real call manager...');

      // Get current user
      const user = await getCurrentUser();
      if (!user) {
        console.warn('⚠️ No authenticated user found');
        return;
      }
      setCurrentUser(user);

      // Check permissions
      await checkAllPermissions();

      // Set up call listeners
      setupCallListeners();

      // Configure audio session
      await configureAudioSession();

      setIsInitialized(true);
      console.log('✅ Real call manager initialized');
    } catch (error) {
      console.error('❌ Error initializing call manager:', error);
    }
  }, [setupCallListeners]);

  const checkAllPermissions = async () => {
    try {
      console.log('🔥 Checking all call permissions...');

      // Check camera permission
      const cameraPermission = await realCallService.checkCameraPermission();
      
      // Check microphone permission
      const microphonePermission = await realCallService.checkMicrophonePermission();
      
      // Check notification permission (disabled as per user requirements)
      // const notificationPermission = await Notifications.getPermissionsAsync();

      setPermissions({
        camera: cameraPermission,
        microphone: microphonePermission,
        notifications: false, // Push notifications disabled
      });

      console.log('✅ Permissions checked:', {
        camera: cameraPermission,
        microphone: microphonePermission,
        notifications: false, // Push notifications disabled
      });
    } catch (error) {
      console.error('❌ Error checking permissions:', error);
    }
  };



  // ==================== REAL INITIALIZATION ====================

  useEffect(() => {
    initializeCallManager();
    return () => {
      cleanup();
    };
  }, [cleanup, initializeCallManager]);

  const configureAudioSession = async () => {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });
      console.log('✅ Audio session configured');
    } catch (error) {
      console.error('❌ Error configuring audio session:', error);
    }
  };

  // ==================== REAL CALL MANAGEMENT ====================

  const startCall = async (
    receiverId: string,
    receiverName: string,
    type: CallType,
    chatId?: string
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!currentUser) {
        return { success: false, error: 'User not authenticated' };
      }

      console.log('🔥 Starting real call...', { receiverId, type });

      // Check permissions
      if (!permissions.microphone || (type === 'video' && !permissions.camera)) {
        return { success: false, error: 'Permissions required' };
      }

      // Haptic feedback
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Start call through real service
      const result = await realCallService.startCall(
        currentUser.id,
        currentUser.displayName,
        receiverId,
        receiverName,
        type,
        chatId
      );

      if (result.success) {
        // Get local stream
        const localStream = realCallService.getLocalStream();
        setCallState(prev => ({
          ...prev,
          localStream,
          isVideoEnabled: type === 'video',
        }));

        // Start monitoring
        startCallDurationTimer();
        startQualityMonitoring();

        console.log('✅ Real call started successfully');
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('❌ Error starting call:', error);
      return { success: false, error: 'Failed to start call' };
    }
  };

  const answerCall = async (): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!callState.currentCall) {
        return { success: false, error: 'No incoming call' };
      }

      console.log('🔥 Answering real call:', callState.currentCall.id);

      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

      const result = await realCallService.answerCall(callState.currentCall.id);

      if (result.success) {
        // Get streams
        const localStream = realCallService.getLocalStream();
        const remoteStream = realCallService.getRemoteStream();

        setCallState(prev => ({
          ...prev,
          localStream,
          remoteStream,
          isVideoEnabled: prev.currentCall?.type === 'video',
        }));

        // Start monitoring
        startCallDurationTimer();
        startQualityMonitoring();

        console.log('✅ Call answered successfully');
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('❌ Error answering call:', error);
      return { success: false, error: 'Failed to answer call' };
    }
  };

  const endCall = async (): Promise<void> => {
    try {
      console.log('🔥 Ending real call...');

      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

      await realCallService.endCall('ended');

      // Reset state
      setCallState(prev => ({
        ...prev,
        currentCall: null,
        localStream: null,
        remoteStream: null,
        isConnected: false,
        callDuration: 0,
      }));

      // Stop monitoring
      stopCallDurationTimer();
      stopQualityMonitoring();

      console.log('✅ Call ended successfully');
    } catch (error) {
      console.error('❌ Error ending call:', error);
    }
  };

  const declineCall = async (): Promise<void> => {
    try {
      if (!callState.currentCall) return;

      console.log('🔥 Declining real call:', callState.currentCall.id);

      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

      await realCallService.endCall(callState.currentCall?.id || 'unknown');

      setCallState(prev => ({
        ...prev,
        currentCall: null,
      }));

      console.log('✅ Call declined successfully');
    } catch (error) {
      console.error('❌ Error declining call:', error);
    }
  };

  // ==================== REAL CALL CONTROLS ====================

  const toggleMute = async (): Promise<boolean> => {
    try {
      const isMuted = realCallService.toggleMute();
      setCallState(prev => ({ ...prev, isMuted }));
      
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      console.log('🔥 Mute toggled:', isMuted);
      
      return isMuted;
    } catch (error) {
      console.error('❌ Error toggling mute:', error);
      return callState.isMuted;
    }
  };

  const toggleVideo = async (): Promise<boolean> => {
    try {
      const isVideoOff = realCallService.toggleVideo();
      setCallState(prev => ({ ...prev, isVideoEnabled: !isVideoOff }));
      
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      console.log('🔥 Video toggled:', !isVideoOff);
      
      return !isVideoOff;
    } catch (error) {
      console.error('❌ Error toggling video:', error);
      return callState.isVideoEnabled;
    }
  };

  const switchCamera = async (): Promise<void> => {
    try {
      await realCallService.switchCamera();
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      console.log('🔥 Camera switched');
    } catch (error) {
      console.error('❌ Error switching camera:', error);
    }
  };

  const toggleSpeaker = async (): Promise<boolean> => {
    try {
      const isSpeakerOn = !callState.isSpeakerOn;
      
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: !isSpeakerOn,
      });

      setCallState(prev => ({ ...prev, isSpeakerOn }));
      
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      console.log('🔥 Speaker toggled:', isSpeakerOn);
      
      return isSpeakerOn;
    } catch (error) {
      console.error('❌ Error toggling speaker:', error);
      return callState.isSpeakerOn;
    }
  };

  // ==================== REAL MONITORING ====================

  const startCallDurationTimer = () => {
    stopCallDurationTimer();
    
    durationInterval.current = setInterval(() => {
      setCallState(prev => ({
        ...prev,
        callDuration: prev.callDuration + 1,
      }));
    }, 1000);
  };

  const stopCallDurationTimer = () => {
    if (durationInterval.current) {
      clearInterval(durationInterval.current);
      durationInterval.current = null;
    }
  };

  const startQualityMonitoring = () => {
    stopQualityMonitoring();
    
    qualityCheckInterval.current = setInterval(async () => {
      try {
        const stats = await realCallService.getCallStats();
        if (stats) {
          // Determine quality based on stats
          let quality: 'excellent' | 'good' | 'poor' | 'connecting' = 'excellent';
          
          if (stats.packetsLost > 5) {
            quality = 'poor';
          } else if (stats.latency > 200) {
            quality = 'good';
          }

          setCallState(prev => ({ ...prev, connectionQuality: quality }));
        }
      } catch (error) {
        console.error('❌ Error monitoring call quality:', error);
      }
    }, 3000);
  };

  const stopQualityMonitoring = () => {
    if (qualityCheckInterval.current) {
      clearInterval(qualityCheckInterval.current);
      qualityCheckInterval.current = null;
    }
  };

  const handleCallStatusChange = (call: RealCall) => {
    switch (call.status) {
      case 'connected':
        console.log('🔥 Call connected');
        // Get remote stream
        const remoteStream = realCallService.getRemoteStream();
        setCallState(prev => ({ ...prev, remoteStream }));
        break;
        
      case 'ended':
      case 'declined':
      case 'failed':
        console.log('🔥 Call ended:', call.status);
        // Reset streams
        setCallState(prev => ({
          ...prev,
          localStream: null,
          remoteStream: null,
        }));
        break;
    }
  };

  // ==================== RETURN INTERFACE ====================

  return {
    // State
    callState,
    permissions,
    currentUser,
    isInitialized,
    
    // Actions
    startCall,
    answerCall,
    endCall,
    declineCall,
    
    // Controls
    toggleMute,
    toggleVideo,
    switchCamera,
    toggleSpeaker,
    
    // Utilities
    checkAllPermissions,
    cleanup,
  };
};

export default useRealCallManager;

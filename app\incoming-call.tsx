// Incoming Call Screen Route
import React from 'react';
import { View } from 'react-native';
import { IncomingCallScreen } from '../src/components/IncomingCallScreen';
import { useRealCallManager } from '../src/hooks/useRealCallManager';
import { auth } from '../src/services/firebaseSimple';

export default function IncomingCallScreenPage() {
  const currentUser = auth?.currentUser;
  const { callState, answerCall, declineCall } = useRealCallManager();

  if (!callState.currentCall) {
    return <View style={{ flex: 1, backgroundColor: '#000' }} />;
  }

  return (
    <IncomingCallScreen
      callData={callState.currentCall as any}
      onAnswer={answerCall}
      onDecline={declineCall}
    />
  );
}

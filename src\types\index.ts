export interface User {
  id: string;
  username: string;
  displayName: string;
  name: string;
  phoneNumber: string;
  avatar: string;
  photoURL?: string;
  bio?: string;
  status?: string;
  followersCount: number;
  followingCount: number;
  likesCount: number;
  isVerified?: boolean;
  isOnline?: boolean;
}

export interface Message {
  id: string;
  text?: string;
  senderId: string;
  senderPhoneNumber?: string;
  senderName?: string;
  timestamp: any;
  type?: "text" | "image" | "file" | "video" | "audio" | "document";
  mediaUrl?: string;
  fileName?: string;
  fileSize?: number;
  // Enhanced message status for individual chats
  status?: "sent" | "delivered" | "seen_not_replied" | "seen_replied";
  statusTime?: any;
  // Pinning functionality
  isPinned?: boolean;
  pinnedAt?: any;
  pinnedBy?: string;
  // Reactions functionality
  reactions?: { [userId: string]: string };
  // Reply functionality
  replyTo?: {
    messageId: string;
    text: string;
    senderName: string;
    type?: string;
  };
  // Forward functionality
  isForwarded?: boolean;
  forwardedFrom?: string;
  originalSender?: string;
  // Edit functionality
  isEdited?: boolean;
  editedAt?: any;
  // Media support for individual chats
  media?: {
    type: "image" | "video";
    url: string;
    thumbnail?: string;
    caption?: string;
  };
  file?: {
    type: "document";
    name: string;
    size: string;
    url: string;
    caption?: string;
  };
}

export interface Chat {
  id: string;
  name?: string;
  isGroup: boolean;
  participants: string[];
  participantDetails?: User[];
  lastMessage?: string;
  lastMessageAt?: string; // Serialized as ISO string
  timestamp: string; // Serialized as ISO string
  avatar?: string;
  description?: string;
  createdBy?: string;
  unreadCount?: number; // Add unreadCount property
}

export interface AuthState {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface ChatState {
  selectedChatId: string | null;
  chats: Chat[];
  isLoading: boolean;
  error: string | null;
}

export interface RootState {
  user: AuthState;
  chat: ChatState;
  updates: UpdatesState;
}

// Update/Post related types for vertical media updates
export interface Update {
  id: string;
  user: User;
  mediaUrl: string;
  mediaType: "video" | "image";
  caption?: string; // Make caption optional to match other definitions
  musicTitle?: string;
  musicAuthor?: string;
  location?: string;
  createdAt: number;
  likeCount: number;
  commentCount: number;
  shareCount: number;
  viewCount: number;
  downloadCount?: number; // Optional download count
  isLiked: boolean;
  isFollowing: boolean;
  hashtags?: string[];
  mentions?: string[];
  comments?: Comment[];
  // Additional properties for compatibility
  timestamp?: any; // Firebase timestamp
  expiresAt?: any; // Firebase timestamp
  isVisible?: boolean;
  media?: {
    id: string;
    url: string;
    type: "image" | "video";
  }[];
}

export interface Comment {
  id: string;
  userId: string;
  user: User;
  text: string;
  likesCount: number;
  repliesCount: number;
  createdAt: number;
  isLiked: boolean;

  // Additional properties for compatibility
  timestamp: Date;
  isVisible: boolean;
  userAvatar?: string;
  username?: string;
  likes?: number;
}

export interface Like {
  id: string;
  updateId: string;
  userId: string;
  timestamp: any; // Firebase timestamp
}

export interface UpdatesState {
  updates: Update[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  lastVisible: any; // For pagination
}

export interface UpdateState {
  updates: Update[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  lastUpdateId: string | null;
}

export interface UpdateAction {
  type:
    | "FETCH_UPDATES_START"
    | "FETCH_UPDATES_SUCCESS"
    | "FETCH_UPDATES_ERROR"
    | "ADD_UPDATE"
    | "UPDATE_LIKE"
    | "UPDATE_COMMENT"
    | "UPDATE_SHARE"
    | "UPDATE_VIEW";
  payload?: any;
}

export interface UpdateContextType {
  state: UpdateState;
  fetchUpdates: () => Promise<void>;
  loadMoreUpdates: () => Promise<void>;
  addUpdate: (_update: Update) => void;
  likeUpdate: (_updateId: string) => void;
  commentUpdate: (_updateId: string, _comment: Comment) => void;
  shareUpdate: (_updateId: string) => void;
  viewUpdate: (_updateId: string) => void;
}

export type NavigationParamList = {
  Login: undefined;
  Register: undefined;
  MainTabs: undefined;
  ChatRoom: { chatId: string; chatName?: string };
  NewChat: undefined;
  CreateGroup: undefined;
  Profile: undefined;
  Settings: undefined;
};

// Additional types for missing interfaces
export interface SearchResult {
  id: string;
  type: "message" | "member" | "contact" | "user";
  title: string;
  subtitle: string;
  content: string;
  timestamp?: Date;
  avatar?: string;
  username?: string;
}

export interface GroupMember {
  id: string; // Add id property for compatibility
  userId: string;
  username?: string;
  name?: string; // Add name property for compatibility
  profilePic?: string;
  role: "admin" | "member";
  joinedAt: Date;
  lastSeen?: Date;
  isOnline?: boolean;
  permissions?: string[];
  isAdmin?: boolean; // Add isAdmin property for compatibility
  isBlocked?: boolean; // Add isBlocked property for compatibility
}

export interface GroupMemberPreferences {
  notifications: {
    messages: boolean;
    mentions: boolean;
    reactions: boolean;
  };
  privacy: {
    readReceipts: boolean;
    lastSeen: boolean;
    profilePhoto: boolean;
  };
  media: {
    autoDownload: boolean;
    quality: "low" | "medium" | "high";
  };
  // Additional properties for compatibility
  isMuted: boolean;
  isArchived: boolean;
  isLocked: boolean;
  hiddenMessages: string[];
  hiddenUpdates: string[];
}

export interface GroupChat {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  members: GroupMember[];
  admins: string[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  settings: {
    allowMemberInvites: boolean;
    allowMemberMessages: boolean;
    allowMediaSharing: boolean;
  };
}

export interface UpdatesScreenProps {
  navigation: any;
  route: any;
}

export type GroupRole = "admin" | "member";

// ==================== NAVIGATION TYPES ====================

export type RootStackParamList = {
  '(tabs)': undefined;
  'real-call': {
    callId: string;
    isOutgoing: boolean;
    contactId?: string;
    contactName?: string;
    callType?: 'voice' | 'video';
  };
  'incoming-call-real': {
    callId: string;
    callerId: string;
    callerName: string;
    callType: 'voice' | 'video';
  };
  'call-screen': {
    callId: string;
    isOutgoing: boolean;
    contactId?: string;
    contactName?: string;
    callType?: 'voice' | 'video';
  };
};

export type CallScreenParams = {
  callId: string;
  isOutgoing: boolean;
  contactId?: string;
  contactName?: string;
  callType?: 'voice' | 'video';
};

// 👥 COMPREHENSIVE GROUPS SCREEN
// Beautiful groups main screen with creation, management, and discovery
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Alert,
  SafeAreaView,
  Image,
  RefreshControl,
  TextInput,

  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { Group } from '../types/Group';
import { realGroupService, RealGroup } from '../services/realGroupService';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  inputBackground: 'rgba(255, 255, 255, 0.1)',
};

interface ComprehensiveGroupsScreenProps {
  onGroupPress?: (_groupId: string) => void;
  onCreateGroup?: () => void;
}

export const ComprehensiveGroupsScreen: React.FC<ComprehensiveGroupsScreenProps> = ({
  onGroupPress,
  onCreateGroup,
}) => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  
  // Responsive dimensions
  const _isTablet = SCREEN_WIDTH > 768;
  const _headerHeight = 60 + insets.top;

  // ==================== STATE MANAGEMENT ====================
  
  const [groups, setGroups] = useState<Group[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [activeTab, setActiveTab] = useState<'my_groups' | 'discover' | 'recent'>('my_groups');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Animation refs
  const searchAnim = useRef(new Animated.Value(0)).current;
  const fabAnim = useRef(new Animated.Value(1)).current;

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (currentUser?.id) {
      loadGroups();
    }
  }, [currentUser?.id, activeTab, loadGroups]);

  useEffect(() => {
    // Animate search bar
    Animated.timing(searchAnim, {
      toValue: showSearch ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [showSearch, searchAnim]);

  // ==================== DATA METHODS ====================

  const loadGroups = useCallback(async () => {
    setIsLoading(true);
    try {
      if (!currentUser?.id) {
        console.log('❌ No current user ID available');
        setGroups([]);
        return;
      }

      console.log('👥 Loading groups for user:', currentUser.id);

      // Load real groups from Firebase
      const realGroups = await realGroupService.getUserGroups(currentUser.id);

      // Convert RealGroup to Group format for UI compatibility
      const convertedGroups: Group[] = realGroups.map((realGroup: RealGroup) => ({
        id: realGroup.id,
        name: realGroup.name,
        description: realGroup.description || '',
        avatar: realGroup.avatar,
        type: realGroup.privacy as 'public' | 'private',
        isVerified: false, // Can be enhanced later
        ownerId: realGroup.createdBy,
        ownerName: realGroup.memberNames[realGroup.createdBy] || 'Unknown',
        createdAt: realGroup.createdAt,
        createdBy: realGroup.createdBy,
        members: realGroup.members,
        memberCount: realGroup.members.length,
        onlineCount: 0, // Can be calculated from online status
        admins: Object.keys(realGroup.memberRoles).filter(userId =>
          realGroup.memberRoles[userId] === 'admin' || realGroup.memberRoles[userId] === 'owner'
        ),
        settings: {
          onlyAdminsCanSendMessages: !realGroup.allowMemberMessages,
          onlyAdminsCanAddMembers: !realGroup.allowMemberInvites,
          allowMemberInvites: realGroup.allowMemberInvites,
          requireApprovalToJoin: realGroup.requireApproval,
          showMemberList: true,
          allowForwarding: true,
          allowScreenshots: true,
          muteNotifications: false,
          mentionNotifications: true,
          disappearingMessages: false,
          disappearingMessagesDuration: 24,
          readReceipts: true,
          typingIndicators: true,
          allowPhotos: true,
          allowVideos: true,
          allowDocuments: true,
          allowVoiceMessages: true,
          allowStickers: true,
          allowGifs: true,
          slowMode: false,
          slowModeDelay: 0,
          maxMembers: realGroup.maxMembers,
          autoDeleteMessages: false,
          autoDeleteDuration: 30,
        },
        lastActivity: realGroup.lastActivity,
        lastMessage: realGroup.lastMessage ? {
          id: realGroup.lastMessage.id,
          text: realGroup.lastMessage.content,
          senderId: realGroup.lastMessage.senderId,
          senderName: realGroup.lastMessage.senderName,
          timestamp: realGroup.lastMessage.timestamp,
          type: realGroup.lastMessage.type,
        } : undefined,
        stats: {
          totalMembers: realGroup.members.length,
          onlineMembers: 0, // Can be calculated from online status
          totalMessages: realGroup.messageCount,
          messagesThisWeek: 0, // Can be calculated
          mediaShared: 0, // Can be calculated
          mostActiveMembers: [],
          peakOnlineTime: { hour: 14, count: 0 },
          joinRate: 0,
          leaveRate: 0,
        },
        invites: [],
        pinnedMessages: [],
        mutedMembers: [],
        bannedMembers: [],
        currentUserRole: realGroup.memberRoles[currentUser.id] || 'member',
        currentUserPermissions: realGroup.memberRoles[currentUser.id] === 'owner' || realGroup.memberRoles[currentUser.id] === 'admin'
          ? ['send_messages', 'add_members', 'remove_members', 'edit_info']
          : ['send_messages'],
        isMuted: false,
        isPinned: false,
        unreadCount: 0, // Can be calculated from unread messages
        mentionCount: 0, // Can be calculated from mentions
        hasBot: false,
        tags: [],
        trustScore: 95,
        reportCount: 0,
        isReported: false,
        isFlagged: false,
      }));

      console.log('✅ Loaded groups:', convertedGroups.length);
      setGroups(convertedGroups);
    } catch (error) {
      console.error('❌ Error loading groups:', error);
      Alert.alert('Error', 'Failed to load groups');
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.id]);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadGroups();
    setIsRefreshing(false);
  }, [loadGroups]);

  const handleGroupPress = (groupId: string) => {
    onGroupPress?.(groupId);
    // Navigate to group chat
    router.push(`/group/${groupId}`);
  };

  const handleCreateGroup = () => {
    setShowCreateModal(true);
    onCreateGroup?.();
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // Implement search functionality
  };

  const getLastMessagePreview = (group: Group) => {
    if (!group.lastMessage) return 'No messages yet';
    
    const { text, type, senderName } = group.lastMessage;
    const preview = type === 'text' ? text : `${type.charAt(0).toUpperCase() + type.slice(1)}`;
    return `${senderName}: ${preview}`;
  };

  const getTimeAgo = (timestamp: Date) => {
    const now = Date.now();
    const diff = now - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days > 0) return `${days}d`;
    if (hours > 0) return `${hours}h`;
    if (minutes > 0) return `${minutes}m`;
    return 'now';
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Text style={styles.headerTitle}>Groups</Text>
            <Text style={styles.headerSubtitle}>
              {groups.length} {groups.length === 1 ? 'group' : 'groups'}
            </Text>
          </View>

          <View style={styles.headerRight}>
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={() => setShowSearch(!showSearch)}
            >
              <Ionicons name="search" size={24} color={COLORS.text} />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={handleCreateGroup}
            >
              <Ionicons name="add" size={24} color={COLORS.text} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <Animated.View 
          style={[
            styles.searchContainer,
            {
              height: searchAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 50],
              }),
              opacity: searchAnim,
            },
          ]}
        >
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color={COLORS.textMuted} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search groups..."
              placeholderTextColor={COLORS.textMuted}
              value={searchQuery}
              onChangeText={handleSearch}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color={COLORS.textMuted} />
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>

        {/* Tab Bar */}
        <View style={styles.tabBar}>
          {(['my_groups', 'discover', 'recent'] as const).map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tabItem, activeTab === tab && styles.activeTabItem]}
              onPress={() => setActiveTab(tab)}
            >
              <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                {tab === 'my_groups' ? 'My Groups' : 
                 tab === 'discover' ? 'Discover' : 'Recent'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </LinearGradient>
    </View>
  );

  const renderGroupItem = ({ item: group, index: _index }: { item: Group; index: number }) => (
    <TouchableOpacity 
      style={styles.groupItem}
      onPress={() => handleGroupPress(group.id)}
      activeOpacity={0.7}
    >
      <View style={styles.groupLeft}>
        <View style={styles.groupAvatarContainer}>
          <Image 
            source={{ uri: group.avatar || 'https://via.placeholder.com/50' }} 
            style={styles.groupAvatar} 
          />
          {group.isVerified && (
            <View style={styles.verifiedBadge}>
              <Ionicons name="checkmark" size={10} color={COLORS.text} />
            </View>
          )}
          {group.onlineCount > 0 && (
            <View style={styles.onlineBadge}>
              <Text style={styles.onlineText}>{group.onlineCount}</Text>
            </View>
          )}
        </View>

        <View style={styles.groupInfo}>
          <View style={styles.groupHeader}>
            <Text style={styles.groupName} numberOfLines={1}>
              {group.name}
            </Text>
            {group.isPinned && (
              <Ionicons name="pin" size={14} color={COLORS.primary} />
            )}
          </View>
          
          <Text style={styles.groupLastMessage} numberOfLines={1}>
            {getLastMessagePreview(group)}
          </Text>
          
          <View style={styles.groupMeta}>
            <Text style={styles.groupMembers}>
              {group.memberCount} members
            </Text>
            <Text style={styles.metaSeparator}>•</Text>
            <Text style={styles.groupTime}>
              {getTimeAgo(group.lastActivity)}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.groupRight}>
        {group.unreadCount > 0 && (
          <View style={styles.unreadBadge}>
            <Text style={styles.unreadText}>
              {group.unreadCount > 99 ? '99+' : group.unreadCount}
            </Text>
          </View>
        )}
        {group.mentionCount > 0 && (
          <View style={styles.mentionBadge}>
            <Text style={styles.mentionText}>@</Text>
          </View>
        )}
        {group.isMuted && (
          <Ionicons name="volume-mute" size={16} color={COLORS.textMuted} />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}

      {/* Groups List */}
      <FlatList
        data={groups}
        renderItem={renderGroupItem}
        keyExtractor={(item) => item.id}
        style={styles.groupsList}
        contentContainerStyle={styles.groupsListContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={COLORS.primary}
            colors={[COLORS.primary]}
          />
        }
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Ionicons name="people-outline" size={64} color={COLORS.textMuted} />
            <Text style={styles.emptyText}>No groups yet</Text>
            <Text style={styles.emptySubtext}>
              {activeTab === 'my_groups' 
                ? 'Create or join a group to get started!'
                : 'Discover new groups to join!'}
            </Text>
            <TouchableOpacity style={styles.emptyButton} onPress={handleCreateGroup}>
              <Text style={styles.emptyButtonText}>Create Group</Text>
            </TouchableOpacity>
          </View>
        )}
      />

      {/* Floating Action Button */}
      <Animated.View 
        style={[
          styles.fab,
          {
            transform: [{ scale: fabAnim }],
          },
        ]}
      >
        <TouchableOpacity style={styles.fabButton} onPress={handleCreateGroup}>
          <Ionicons name="add" size={24} color={COLORS.text} />
        </TouchableOpacity>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  headerGradient: {
    paddingBottom: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 12,
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 8,
    overflow: 'hidden',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.inputBackground,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    paddingVertical: 4,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 8,
    gap: 8,
  },
  tabItem: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  activeTabItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  tabText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  activeTabText: {
    color: COLORS.text,
    fontWeight: '700',
  },
  groupsList: {
    flex: 1,
  },
  groupsListContent: {
    paddingVertical: 8,
  },
  groupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.surface,
    marginHorizontal: 16,
    marginVertical: 2,
    borderRadius: 16,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  groupLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  groupAvatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  groupAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  verifiedBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: COLORS.success,
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: COLORS.surface,
  },
  onlineBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
    borderWidth: 1.5,
    borderColor: COLORS.surface,
  },
  onlineText: {
    fontSize: 10,
    color: COLORS.text,
    fontWeight: '700',
  },
  groupInfo: {
    flex: 1,
  },
  groupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 6,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    flex: 1,
  },
  groupLastMessage: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  groupMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  groupMembers: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  metaSeparator: {
    fontSize: 12,
    color: COLORS.textMuted,
    marginHorizontal: 6,
  },
  groupTime: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  groupRight: {
    alignItems: 'flex-end',
    gap: 4,
  },
  unreadBadge: {
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadText: {
    fontSize: 12,
    color: COLORS.background,
    fontWeight: '700',
  },
  mentionBadge: {
    backgroundColor: COLORS.error,
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mentionText: {
    fontSize: 10,
    color: COLORS.text,
    fontWeight: '700',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 16,
    color: COLORS.textMuted,
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 22,
  },
  emptyButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    paddingHorizontal: 24,
    paddingVertical: 12,
    marginTop: 24,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  emptyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.background,
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
  },
  fabButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 28,
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 10,
    borderWidth: 3,
    borderColor: COLORS.background,
  },
});

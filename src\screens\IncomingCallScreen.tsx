// 🔥 INCOMING CALL SCREEN - REAL WEBRTC INCOMING CALL HANDLING
// No mockups, no fake data - 100% real incoming call functionality

import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Animated,
  ImageBackground,
  Platform,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useRealCallManager } from '../hooks/useRealCallManager';
import { useNavigation } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';
import { soundService } from '../services/soundService';

const { width, height } = Dimensions.get('window');

interface IncomingCallScreenProps {
  callId: string;
  callerId: string;
  callerName: string;
  callType: 'voice' | 'video';
  callerAvatar?: string;
}

export const IncomingCallScreen: React.FC<IncomingCallScreenProps> = ({
  callId,
  callerId,
  callerName,
  callType,
  callerAvatar,
}) => {
  // ==================== REAL CALL MANAGEMENT ====================
  
  const navigation = useNavigation();
  const { answerCall, declineCall, callState } = useRealCallManager();
  
  // ==================== REAL ANIMATIONS ====================
  
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(height)).current;
  const [isAnswering, setIsAnswering] = useState(false);
  const [isDeclining, setIsDeclining] = useState(false);

  // ==================== REAL EFFECTS ====================

  useEffect(() => {
    // Slide in animation
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();

    // Pulse animation for incoming call
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    // Play ringtone
    soundService.playRingtone();

    // Cleanup
    return () => {
      pulseAnimation.stop();
      soundService.stopRingtone();
    };
  }, [pulseAnim, slideAnim]);

  // ==================== REAL CALL ACTIONS ====================

  const handleAnswer = async () => {
    try {
      setIsAnswering(true);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
      
      // Stop ringtone
      await soundService.stopRingtone();
      
      // Answer the call
      const result = await answerCall();
      
      if (result.success) {
        // Navigate to call screen
        (navigation as any).navigate('real-call', {
          callId,
          isOutgoing: false,
          contactId: callerId,
          contactName: callerName,
          callType,
        });
      } else {
        console.error('Failed to answer call:', result.error);
        setIsAnswering(false);
      }
    } catch (error) {
      console.error('Error answering call:', error);
      setIsAnswering(false);
    }
  };

  const handleDecline = async () => {
    try {
      setIsDeclining(true);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      // Stop ringtone
      await soundService.stopRingtone();
      
      // Decline the call
      await declineCall();

      // Navigate back
      navigation.goBack();
    } catch (error) {
      console.error('Error declining call:', error);
      setIsDeclining(false);
    }
  };

  // ==================== REAL RENDER ====================

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      {/* Background */}
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={styles.background}
      />
      
      {/* Animated Container */}
      <Animated.View 
        style={[
          styles.content,
          { transform: [{ translateY: slideAnim }] }
        ]}
      >
        <SafeAreaView style={styles.safeArea}>
          
          {/* Call Type Indicator with Platform-specific styling */}
          <BlurView intensity={Platform.OS === 'ios' ? 80 : 100} style={styles.callTypeContainer}>
            <Ionicons
              name={callType === 'video' ? 'videocam' : 'call'}
              size={24}
              color="#FFFFFF"
            />
            <Text style={styles.callTypeText}>
              Incoming {callType} call {Platform.OS === 'ios' ? '(iOS)' : '(Android)'}
            </Text>
          </BlurView>

          {/* Caller Info */}
          <View style={styles.callerContainer}>
            
            {/* Avatar */}
            <Animated.View 
              style={[
                styles.avatarContainer,
                { transform: [{ scale: pulseAnim }] }
              ]}
            >
              {callerAvatar ? (
                <ImageBackground
                  source={{ uri: callerAvatar }}
                  style={styles.avatar}
                  imageStyle={styles.avatarImage}
                >
                  <View style={styles.avatarOverlay} />
                </ImageBackground>
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Ionicons name="person" size={80} color="#FFFFFF" />
                </View>
              )}
            </Animated.View>

            {/* Caller Name */}
            <Text style={styles.callerName}>{callerName}</Text>
            <Text style={styles.callerSubtitle}>
              {callState.currentCall?.status === 'ringing' ?
                (callType === 'video' ? 'Incoming Video Call' : 'Incoming Voice Call') :
                'Call connecting...'}
            </Text>
          </View>

          {/* Call Actions */}
          <View style={styles.actionsContainer}>
            
            {/* Decline Button */}
            <TouchableOpacity
              style={[styles.actionButton, styles.declineButton]}
              onPress={handleDecline}
              disabled={isDeclining || isAnswering}
              activeOpacity={0.8}
            >
              <Ionicons
                name="call"
                size={32}
                color="#FFFFFF"
                style={{ transform: [{ rotate: '135deg' }] }}
              />
            </TouchableOpacity>

            {/* Answer Button */}
            <TouchableOpacity
              style={[styles.actionButton, styles.answerButton]}
              onPress={handleAnswer}
              disabled={isAnswering || isDeclining}
              activeOpacity={0.8}
            >
              <Ionicons name="call" size={32} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          {/* Additional Actions */}
          <View style={styles.additionalActions}>
            <TouchableOpacity style={styles.additionalButton}>
              <Ionicons name="chatbubble" size={24} color="#FFFFFF" />
              <Text style={styles.additionalButtonText}>Message</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.additionalButton}>
              <Ionicons name="time" size={24} color="#FFFFFF" />
              <Text style={styles.additionalButtonText}>Remind</Text>
            </TouchableOpacity>
          </View>

        </SafeAreaView>
      </Animated.View>
    </View>
  );
};

// ==================== REAL STYLES ====================

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
    width: width,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  content: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: 20,
  },
  callTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 60,
    marginBottom: 40,
  },
  callTypeText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
    opacity: 0.9,
  },
  callerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarContainer: {
    marginBottom: 30,
  },
  avatar: {
    width: 160,
    height: 160,
    borderRadius: 80,
    overflow: 'hidden',
  },
  avatarImage: {
    borderRadius: 80,
  },
  avatarOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 80,
  },
  avatarPlaceholder: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  callerName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  callerSubtitle: {
    fontSize: 18,
    color: '#FFFFFF',
    opacity: 0.8,
    textAlign: 'center',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 40,
    marginBottom: 40,
  },
  actionButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  answerButton: {
    backgroundColor: '#4ECDC4',
  },
  declineButton: {
    backgroundColor: '#FF6B6B',
  },
  additionalActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 60,
    marginBottom: 40,
  },
  additionalButton: {
    alignItems: 'center',
    opacity: 0.8,
  },
  additionalButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
});

export default IncomingCallScreen;

// 🔥 EXPO CONFIG FOR WEBRTC - CUSTOM DEVELOPMENT BUILD
// Complete configuration for react-native-webrtc support

const IS_DEV = process.env.APP_VARIANT === 'development';

export default {
  expo: {
    name: IS_DEV ? '<PERSON><PERSON><PERSON> (Dev)' : '<PERSON><PERSON><PERSON>',
    slug: 'irachat',
    version: '1.0.0',
    scheme: 'irachat',
    orientation: 'portrait',
    icon: './assets/images/LOGO.png',
    userInterfaceStyle: 'light',
    splash: {
      image: './assets/images/LOGO.png',
      resizeMode: 'contain',
      backgroundColor: '#87CEEB'
    },
    assetBundlePatterns: [
      '**/*'
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: IS_DEV ? 'com.irachat.mobile.dev' : 'com.irachat.mobile',
      buildNumber: '1.0.0',
      infoPlist: {
        NSCameraUsageDescription: '<PERSON><PERSON><PERSON> needs camera access for video calls and sharing photos/videos in chats.',
        NSMicrophoneUsageDescription: '<PERSON><PERSON><PERSON> needs microphone access for voice calls, video calls, and voice messages.',
        NSPhotoLibraryUsageDescription: '<PERSON><PERSON><PERSON> needs photo library access to share images and videos in chats.',
        NSContactsUsageDescription: 'IraChat needs contacts access to help you find friends who are already using the app.',
        NSLocationWhenInUseUsageDescription: 'IraChat needs location access to share your location with friends when you choose to.',
        UIBackgroundModes: [
          'voip',
          'audio',
          'background-processing',
          'background-fetch'
        ],
        NSAppTransportSecurity: {
          NSAllowsArbitraryLoads: true
        },
        RTCAppGroupIdentifier: 'group.com.irachat.webrtc',
        RTCScreenSharingExtension: 'IraChatScreenShare'
      }
    },
    android: {
      icon: './assets/images/LOGO.png',
      adaptiveIcon: {
        foregroundImage: './assets/images/LOGO.png',
        backgroundColor: '#87CEEB'
      },
      package: IS_DEV ? 'com.irachat.mobile.dev' : 'com.irachat.mobile',
      compileSdkVersion: 34,
      targetSdkVersion: 34,
      minSdkVersion: 24,
      edgeToEdgeEnabled: false,
      permissions: [
        'android.permission.CAMERA',
        'android.permission.RECORD_AUDIO',
        'android.permission.MODIFY_AUDIO_SETTINGS',
        'android.permission.INTERNET',
        'android.permission.ACCESS_NETWORK_STATE',
        'android.permission.WAKE_LOCK',
        'android.permission.READ_EXTERNAL_STORAGE',
        'android.permission.WRITE_EXTERNAL_STORAGE',
        'android.permission.READ_CONTACTS',
        'android.permission.ACCESS_FINE_LOCATION',
        'android.permission.ACCESS_COARSE_LOCATION',
        'android.permission.VIBRATE',
        'android.permission.SYSTEM_ALERT_WINDOW',
        'android.permission.USE_FULL_SCREEN_INTENT',
        'android.permission.FOREGROUND_SERVICE',
        'android.permission.BLUETOOTH',
        'android.permission.BLUETOOTH_ADMIN',
        'android.permission.BLUETOOTH_CONNECT',
        'android.permission.CHANGE_NETWORK_STATE',
        'android.permission.ACCESS_WIFI_STATE',
        'android.permission.CHANGE_WIFI_STATE',
        'android.permission.CAPTURE_AUDIO_OUTPUT',
        'android.permission.BIND_TELECOM_CONNECTION_SERVICE'
      ]
    },
    web: {
      favicon: './assets/images/LOGO.png'
    },
    plugins: [
      'expo-router',
      [
        'expo-build-properties',
        {
          ios: {
            deploymentTarget: '13.4'
          },
          android: {
            compileSdkVersion: 34,
            targetSdkVersion: 34,
            buildToolsVersion: '34.0.0'
          }
        }
      ],
      [
        '@config-plugins/react-native-webrtc',
        {
          cameraPermission: 'Allow IraChat to access your camera for video calls.',
          microphonePermission: 'Allow IraChat to access your microphone for voice and video calls.'
        }
      ],

    ],
    extra: {
      router: {
        origin: false
      },
      eas: {
        projectId: 'irachat-production'
      }
    },
    owner: 'irachat'
  }
};

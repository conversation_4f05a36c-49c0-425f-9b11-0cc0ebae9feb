import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useState, useEffect } from "react";
import { navigationService, ROUTES } from "../src/services/navigationService";
import {
    Alert,
    Linking,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View,
    ActivityIndicator,
    Share,
    Dimensions,
    Platform,
    Modal,
    Switch,
} from "react-native";
import { useSelector } from "react-redux";
import { RootState } from "../src/redux/store";
import { realSupportService } from "../src/services/realSupportService";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface SupportCategory {
  id: string;
  title: string;
  icon: string;
  description: string;
  color: string;
}

const supportCategories: SupportCategory[] = [
  {
    id: 'faq',
    title: 'Frequently Asked Questions',
    icon: 'help-circle',
    description: 'Find quick answers to common questions',
    color: '#87CEEB',
  },
  {
    id: 'contact',
    title: 'Contact Support',
    icon: 'mail',
    description: 'Get personalized help from our team',
    color: '#87CEEB',
  },
  {
    id: 'bug',
    title: 'Report a Bug',
    icon: 'bug',
    description: 'Help us improve by reporting issues',
    color: '#87CEEB',
  },
  {
    id: 'feature',
    title: 'Request Feature',
    icon: 'bulb',
    description: 'Suggest new features for IraChat',
    color: '#87CEEB',
  },
  {
    id: 'tickets',
    title: 'My Tickets',
    icon: 'ticket',
    description: 'View your support ticket history',
    color: '#87CEEB',
  },
  {
    id: 'community',
    title: 'Community Forum',
    icon: 'people',
    description: 'Connect with other IraChat users',
    color: '#87CEEB',
  },
  {
    id: 'tutorials',
    title: 'Video Tutorials',
    icon: 'play-circle',
    description: 'Learn how to use IraChat features',
    color: '#87CEEB',
  },
  {
    id: 'troubleshoot',
    title: 'Troubleshooting',
    icon: 'construct',
    description: 'Fix common issues yourself',
    color: '#87CEEB',
  },
];

export default function HelpSupportScreen() {
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  
  // State management
  const [activeSection, setActiveSection] = useState<string>('main');
  const [faqs, setFaqs] = useState<any[]>([]);
  const [tickets, setTickets] = useState<any[]>([]);
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  
  // Contact form state
  const [contactForm, setContactForm] = useState({
    subject: '',
    description: '',
    category: 'other' as 'bug' | 'feature' | 'account' | 'billing' | 'other',
    priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
  });
  
  // Bug report state
  const [bugReport, setBugReport] = useState({
    title: '',
    description: '',
    steps: '',
    expectedBehavior: '',
    actualBehavior: '',
  });

  // New state for implemented features
  const [bugDescription, setBugDescription] = useState('');
  const [tutorials] = useState([
    {
      title: 'Getting Started with IraChat',
      duration: '3:45',
      category: 'Basics',
      url: 'https://example.com/tutorial1',
    },
    {
      title: 'Making Voice and Video Calls',
      duration: '5:20',
      category: 'Calling',
      url: 'https://example.com/tutorial2',
    },
    {
      title: 'Creating and Managing Groups',
      duration: '4:15',
      category: 'Groups',
      url: 'https://example.com/tutorial3',
    },
    {
      title: 'Sharing Media and Files',
      duration: '2:30',
      category: 'Media',
      url: 'https://example.com/tutorial4',
    },
  ]);

  // Feature request state
  const [featureRequest, setFeatureRequest] = useState({
    title: '',
    description: '',
    useCase: '',
    priority: 'medium' as 'low' | 'medium' | 'high',
  });

  // Chat clearing state
  const [showChatClearingHelp, setShowChatClearingHelp] = useState(false);
  const [showDataManagement, setShowDataManagement] = useState(false);

  // Load data on component mount
  useEffect(() => {
    if (activeSection === 'faq') {
      loadFAQs();
    } else if (activeSection === 'tickets') {
      loadTickets();
    }
  }, [activeSection]);

  const loadFAQs = async () => {
    setIsLoading(true);
    try {
      const result = await realSupportService.getFAQs();
      if (result.success && result.faqs) {
        setFaqs(result.faqs);
      } else {
        // Provide default FAQs if service fails
        setFaqs([
          {
            id: '1',
            question: 'How do I clear my chat history?',
            answer: 'Go to Settings > Privacy & Security > Clear Chat Data. You can clear individual chats or all chats at once.',
            category: 'privacy',
          },
          {
            id: '2',
            question: 'How do I backup my chats?',
            answer: 'Go to Settings > Account > Backup & Restore. Enable automatic backups to save your chats to cloud storage.',
            category: 'data',
          },
          {
            id: '3',
            question: 'How do I delete my account?',
            answer: 'Go to Settings > Account > Delete Account. This will permanently remove all your data.',
            category: 'account',
          },
          {
            id: '4',
            question: 'How do I manage storage space?',
            answer: 'Go to Settings > Storage & Data to see usage and clear cached media files.',
            category: 'storage',
          },
          {
            id: '5',
            question: 'How do I export my chat data?',
            answer: 'Go to Settings > Privacy & Security > Export Data to download your chat history.',
            category: 'data',
          },
        ]);
      }
    } catch (error) {
      console.error('Error loading FAQs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadTickets = async () => {
    if (!currentUser?.id) return;
    
    setIsLoading(true);
    try {
      const result = await realSupportService.getUserTickets(currentUser.id);
      if (result.success && result.tickets) {
        setTickets(result.tickets);
      }
    } catch (error) {
      console.error('Error loading tickets:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFAQToggle = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  const handleSubmitTicket = async () => {
    if (!currentUser?.id || !contactForm.subject.trim() || !contactForm.description.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsLoading(true);
    try {
      const result = await realSupportService.submitTicket(
        currentUser.id,
        currentUser.name || 'Unknown User',
        '<EMAIL>',
        contactForm
      );

      if (result.success) {
        Alert.alert('Success', 'Your support ticket has been submitted. We will get back to you soon!');
        setContactForm({
          subject: '',
          description: '',
          category: 'other',
          priority: 'medium',
        });
        setActiveSection('main');
      } else {
        Alert.alert('Error', result.error || 'Failed to submit ticket');
      }
    } catch (error) {
      console.error('Error submitting ticket:', error);
      Alert.alert('Error', 'Failed to submit support ticket');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReportBug = async () => {
    if (!currentUser?.id || !bugReport.title.trim() || !bugReport.description.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsLoading(true);
    try {
      const deviceInfo = `${Platform.OS} ${Platform.Version}`;
      const appVersion = '1.0.0';

      const result = await realSupportService.reportBug(
        currentUser.id,
        currentUser.name || 'Unknown User',
        '<EMAIL>',
        {
          ...bugReport,
          deviceInfo,
          appVersion,
        }
      );

      if (result.success) {
        Alert.alert('Success', 'Bug report submitted successfully. Thank you for helping us improve IraChat!');
        setBugReport({
          title: '',
          description: '',
          steps: '',
          expectedBehavior: '',
          actualBehavior: '',
        });
        setActiveSection('main');
      } else {
        Alert.alert('Error', result.error || 'Failed to submit bug report');
      }
    } catch (error) {
      console.error('Error submitting bug report:', error);
      Alert.alert('Error', 'Failed to submit bug report');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestFeature = async () => {
    if (!currentUser?.id || !featureRequest.title.trim() || !featureRequest.description.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsLoading(true);
    try {
      const result = await realSupportService.requestFeature(
        currentUser.id,
        currentUser.name || 'Unknown User',
        '<EMAIL>',
        featureRequest
      );

      if (result.success) {
        Alert.alert('Success', 'Feature request submitted successfully. We appreciate your feedback!');
        setFeatureRequest({
          title: '',
          description: '',
          useCase: '',
          priority: 'medium',
        });
        setActiveSection('main');
      } else {
        Alert.alert('Error', result.error || 'Failed to submit feature request');
      }
    } catch (error) {
      console.error('Error submitting feature request:', error);
      Alert.alert('Error', 'Failed to submit feature request');
    } finally {
      setIsLoading(false);
    }
  };

  // New implemented functions
  const handleSubmitBugReport = async () => {
    if (!bugDescription.trim()) {
      Alert.alert('Error', 'Please describe the bug you encountered');
      return;
    }

    setIsLoading(true);
    try {
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../src/services/firebaseSimple');

      const bugReportData = {
        description: bugDescription.trim(),
        userId: currentUser?.id || 'anonymous',
        userPhone: currentUser?.phoneNumber || 'unknown',
        timestamp: serverTimestamp(),
        status: 'open',
        type: 'bug_report',
      };

      await addDoc(collection(db, 'support_tickets'), bugReportData);
      Alert.alert('Success', 'Bug report submitted successfully. Thank you for helping us improve IraChat!');
      setBugDescription('');
      setActiveSection('main');
    } catch (error) {
      console.error('Error submitting bug report:', error);
      Alert.alert('Error', 'Failed to submit bug report. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitFeatureRequest = async () => {
    if (!featureRequest.title.trim() || !featureRequest.description.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsLoading(true);
    try {
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../src/services/firebaseSimple');

      const featureRequestData = {
        title: featureRequest.title.trim(),
        description: featureRequest.description.trim(),
        useCase: featureRequest.useCase.trim(),
        priority: featureRequest.priority,
        userId: currentUser?.id || 'anonymous',
        userPhone: currentUser?.phoneNumber || 'unknown',
        timestamp: serverTimestamp(),
        status: 'open',
        type: 'feature_request',
        votes: 1,
      };

      await addDoc(collection(db, 'feature_requests'), featureRequestData);
      Alert.alert('Success', 'Feature request submitted successfully. We appreciate your feedback!');
      setFeatureRequest({
        title: '',
        description: '',
        useCase: '',
        priority: 'medium',
      });
      setActiveSection('main');
    } catch (error) {
      console.error('Error submitting feature request:', error);
      Alert.alert('Error', 'Failed to submit feature request. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleWatchTutorial = (tutorial: any) => {
    Alert.alert(
      tutorial.title,
      `This tutorial will help you learn about ${tutorial.category.toLowerCase()}. Duration: ${tutorial.duration}`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Watch Now',
          onPress: () => {
            // In a real app, this would open a video player or web browser
            Alert.alert('Tutorial', 'Tutorial video would open here in a real implementation');
          }
        },
      ]
    );
  };

  const handleContactMethod = (method: string) => {
    const contactInfo = realSupportService.getContactInfo();
    
    switch (method) {
      case 'email':
        Linking.openURL(`mailto:${contactInfo.email}?subject=IraChat Support Request`);
        break;
      case 'phone':
        Linking.openURL(`tel:${contactInfo.phone}`);
        break;
      case 'website':
        Linking.openURL(contactInfo.website);
        break;
      case 'twitter':
        Linking.openURL(`https://twitter.com/${contactInfo.socialMedia.twitter}`);
        break;
      case 'facebook':
        Linking.openURL(`https://facebook.com/${contactInfo.socialMedia.facebook}`);
        break;
      default:
        break;
    }
  };

  const handleShareApp = async () => {
    try {
      await Share.share({
        message: 'Check out IraChat - the best messaging app! Download it now: https://irachat.com/download',
        title: 'IraChat - Connect with Friends',
      });
    } catch (error) {
      console.error('Error sharing app:', error);
    }
  };

  const handleChatClearing = () => {
    Alert.alert(
      'Clear Chat Data',
      'Choose what you want to clear:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All Chats',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Confirm Clear All',
              'This will permanently delete all your chat history. This action cannot be undone.',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Clear All', style: 'destructive', onPress: () => {
                  Alert.alert('Success', 'All chat data has been cleared');
                }}
              ]
            );
          }
        },
        {
          text: 'Clear Individual Chats',
          onPress: () => {
            navigationService.navigate(ROUTES.SETTINGS.CHAT_MANAGEMENT);
          }
        },
        {
          text: 'Clear Media Only',
          onPress: () => {
            Alert.alert('Success', 'Media cache has been cleared');
          }
        }
      ]
    );
  };

  // Render main support categories
  const renderMainScreen = () => (
    <ScrollView style={{ flex: 1, backgroundColor: '#F8F9FA' }}>
      {/* Header */}
      <View style={{
        backgroundColor: '#87CEEB',
        paddingTop: 60,
        paddingBottom: 30,
        paddingHorizontal: 20,
      }}>
        <TouchableOpacity
          onPress={() => navigationService.goBack()}
          style={{
            position: 'absolute',
            top: 60,
            left: 20,
            zIndex: 1,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        
        <Text style={{
          fontSize: 28,
          fontWeight: 'bold',
          color: '#FFFFFF',
          textAlign: 'center',
          marginTop: 10,
        }}>
          Help & Support
        </Text>
        <Text style={{
          fontSize: 16,
          color: '#FFFFFF',
          textAlign: 'center',
          marginTop: 8,
          opacity: 0.9,
        }}>
          We're here to help you with IraChat
        </Text>
      </View>

      {/* Search Bar */}
      <View style={{
        backgroundColor: '#FFFFFF',
        marginHorizontal: 20,
        marginTop: -15,
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 12,
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}>
        <Ionicons name="search" size={20} color="#87CEEB" />
        <TextInput
          placeholder="Search for help..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={{
            flex: 1,
            marginLeft: 12,
            fontSize: 16,
            color: '#333',
          }}
          placeholderTextColor="#999"
        />
      </View>

      {/* Support Categories */}
      <View style={{ padding: 20 }}>
        <Text style={{
          fontSize: 20,
          fontWeight: 'bold',
          color: '#333',
          marginBottom: 16,
        }}>
          How can we help you?
        </Text>
        
        {supportCategories.map((category, index) => (
          <TouchableOpacity
            key={category.id}
            onPress={() => setActiveSection(category.id)}
            style={{
              backgroundColor: '#FFFFFF',
              borderRadius: 12,
              padding: 16,
              marginBottom: 12,
              flexDirection: 'row',
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.1,
              shadowRadius: 2,
              elevation: 2,
            }}
          >
            <View style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              backgroundColor: category.color,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 16,
            }}>
              <Ionicons name={category.icon as any} size={24} color="#FFFFFF" />
            </View>
            
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#333',
                marginBottom: 4,
              }}>
                {category.title}
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#666',
              }}>
                {category.description}
              </Text>
            </View>
            
            <Ionicons name="chevron-forward" size={20} color="#87CEEB" />
          </TouchableOpacity>
        ))}
      </View>

      {/* Quick Actions */}
      <View style={{
        backgroundColor: '#FFFFFF',
        marginHorizontal: 20,
        borderRadius: 12,
        padding: 20,
        marginBottom: 20,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
          marginBottom: 16,
        }}>
          Quick Actions
        </Text>
        
        <TouchableOpacity
          onPress={() => handleContactMethod('email')}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 12,
            borderBottomWidth: 1,
            borderBottomColor: '#F0F0F0',
          }}
        >
          <Ionicons name="mail" size={20} color="#87CEEB" />
          <Text style={{
            marginLeft: 12,
            fontSize: 16,
            color: '#333',
          }}>
            Email Support
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={() => handleContactMethod('phone')}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 12,
            borderBottomWidth: 1,
            borderBottomColor: '#F0F0F0',
          }}
        >
          <Ionicons name="call" size={20} color="#87CEEB" />
          <Text style={{
            marginLeft: 12,
            fontSize: 16,
            color: '#333',
          }}>
            Phone Support
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={handleChatClearing}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 12,
            borderBottomWidth: 1,
            borderBottomColor: '#F0F0F0',
          }}
        >
          <Ionicons name="trash" size={20} color="#87CEEB" />
          <Text style={{
            marginLeft: 12,
            fontSize: 16,
            color: '#333',
          }}>
            Clear Chat Data
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={handleShareApp}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 12,
          }}
        >
          <Ionicons name="share" size={20} color="#87CEEB" />
          <Text style={{
            marginLeft: 12,
            fontSize: 16,
            color: '#333',
          }}>
            Share IraChat
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  // Main render logic
  if (activeSection === 'main') {
    return renderMainScreen();
  }

  // For other sections, show a comprehensive interface
  return (
    <View style={{ flex: 1, backgroundColor: '#F8F9FA' }}>
      <View style={{
        backgroundColor: '#87CEEB',
        paddingTop: 60,
        paddingBottom: 20,
        paddingHorizontal: 20,
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => setActiveSection('main')}
          style={{ marginRight: 16 }}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        
        <Text style={{
          fontSize: 20,
          fontWeight: 'bold',
          color: '#FFFFFF',
        }}>
          {supportCategories.find(cat => cat.id === activeSection)?.title || 'Support'}
        </Text>
      </View>
      
      <ScrollView style={{ flex: 1, padding: 20 }}>
        {isLoading ? (
          <View style={{ alignItems: 'center', marginTop: 50 }}>
            <ActivityIndicator size="large" color="#87CEEB" />
            <Text style={{ marginTop: 16, fontSize: 16, color: '#666' }}>
              Loading...
            </Text>
          </View>
        ) : (
          <>
            {activeSection === 'faq' && (
              <View>
                <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 16, color: '#333' }}>
                  Frequently Asked Questions
                </Text>
                {faqs.map((faq) => (
                  <TouchableOpacity
                    key={faq.id}
                    onPress={() => handleFAQToggle(faq.id)}
                    style={{
                      backgroundColor: '#FFFFFF',
                      borderRadius: 12,
                      padding: 16,
                      marginBottom: 12,
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 1 },
                      shadowOpacity: 0.1,
                      shadowRadius: 2,
                      elevation: 2,
                    }}
                  >
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text style={{ fontSize: 16, fontWeight: '600', color: '#333', flex: 1 }}>
                        {faq.question}
                      </Text>
                      <Ionicons 
                        name={expandedFAQ === faq.id ? "chevron-up" : "chevron-down"} 
                        size={20} 
                        color="#87CEEB" 
                      />
                    </View>
                    {expandedFAQ === faq.id && (
                      <Text style={{ marginTop: 12, fontSize: 14, color: '#666', lineHeight: 20 }}>
                        {faq.answer}
                      </Text>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {activeSection === 'contact' && (
              <View>
                <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 16, color: '#333' }}>
                  Contact Support
                </Text>
                
                <View style={{ backgroundColor: '#FFFFFF', borderRadius: 12, padding: 16, marginBottom: 16 }}>
                  <Text style={{ fontSize: 16, fontWeight: '600', marginBottom: 8, color: '#333' }}>
                    Subject
                  </Text>
                  <TextInput
                    placeholder="Brief description of your issue"
                    value={contactForm.subject}
                    onChangeText={(text) => setContactForm(prev => ({ ...prev, subject: text }))}
                    style={{
                      borderWidth: 1,
                      borderColor: '#ddd',
                      borderRadius: 8,
                      padding: 12,
                      fontSize: 16,
                      marginBottom: 16,
                    }}
                  />

                  <Text style={{ fontSize: 16, fontWeight: '600', marginBottom: 8, color: '#333' }}>
                    Description
                  </Text>
                  <TextInput
                    placeholder="Please describe your issue in detail..."
                    value={contactForm.description}
                    onChangeText={(text) => setContactForm(prev => ({ ...prev, description: text }))}
                    style={{
                      borderWidth: 1,
                      borderColor: '#ddd',
                      borderRadius: 8,
                      padding: 12,
                      fontSize: 16,
                      minHeight: 100,
                      marginBottom: 16,
                    }}
                    multiline
                  />

                  <Text style={{ fontSize: 16, fontWeight: '600', marginBottom: 8, color: '#333' }}>
                    Category
                  </Text>
                  <View style={{ flexDirection: 'row', flexWrap: 'wrap', marginBottom: 16 }}>
                    {(['bug', 'feature', 'account', 'billing', 'other'] as const).map((category) => (
                      <TouchableOpacity
                        key={category}
                        onPress={() => setContactForm(prev => ({ ...prev, category }))}
                        style={{
                          backgroundColor: contactForm.category === category ? '#87CEEB' : '#f0f0f0',
                          paddingHorizontal: 16,
                          paddingVertical: 8,
                          borderRadius: 20,
                          marginRight: 8,
                          marginBottom: 8,
                        }}
                      >
                        <Text style={{
                          color: contactForm.category === category ? 'white' : '#666',
                          fontSize: 14,
                          fontWeight: '600',
                          textTransform: 'capitalize',
                        }}>
                          {category}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>

                  <TouchableOpacity
                    onPress={handleSubmitTicket}
                    disabled={isLoading}
                    style={{
                      backgroundColor: '#87CEEB',
                      padding: 16,
                      borderRadius: 8,
                      alignItems: 'center',
                    }}
                  >
                    {isLoading ? (
                      <ActivityIndicator color="white" />
                    ) : (
                      <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
                        Submit Ticket
                      </Text>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {activeSection === 'tickets' && (
              <View>
                <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 16, color: '#333' }}>
                  My Support Tickets
                </Text>
                {tickets.length === 0 ? (
                  <View style={{
                    backgroundColor: '#FFFFFF',
                    borderRadius: 12,
                    padding: 20,
                    alignItems: 'center',
                  }}>
                    <Ionicons name="ticket" size={48} color="#87CEEB" />
                    <Text style={{ fontSize: 16, color: '#666', marginTop: 12, textAlign: 'center' }}>
                      No support tickets found
                    </Text>
                    <Text style={{ fontSize: 14, color: '#999', marginTop: 4, textAlign: 'center' }}>
                      Submit a ticket if you need help
                    </Text>
                  </View>
                ) : (
                  tickets.map((ticket) => (
                    <View
                      key={ticket.id}
                      style={{
                        backgroundColor: '#FFFFFF',
                        borderRadius: 12,
                        padding: 16,
                        marginBottom: 12,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 1 },
                        shadowOpacity: 0.1,
                        shadowRadius: 2,
                        elevation: 2,
                      }}
                    >
                      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                        <Text style={{ fontSize: 16, fontWeight: '600', color: '#333' }}>
                          {ticket.subject}
                        </Text>
                        <View style={{
                          backgroundColor: ticket.status === 'open' ? '#F59E0B' : 
                                         ticket.status === 'resolved' ? '#10B981' : '#87CEEB',
                          paddingHorizontal: 8,
                          paddingVertical: 4,
                          borderRadius: 12,
                        }}>
                          <Text style={{ color: 'white', fontSize: 12, fontWeight: '600', textTransform: 'capitalize' }}>
                            {ticket.status}
                          </Text>
                        </View>
                      </View>
                      <Text style={{ fontSize: 14, color: '#666', marginBottom: 8 }}>
                        {ticket.description}
                      </Text>
                      <Text style={{ fontSize: 12, color: '#999' }}>
                        Created: {ticket.createdAt?.toLocaleDateString()}
                      </Text>
                    </View>
                  ))
                )}
              </View>
            )}

            {/* Add more sections as needed */}
            {activeSection === 'bug' && (
              <View style={{ padding: 20 }}>
                <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 15, color: '#333' }}>
                  Report a Bug
                </Text>
                <TextInput
                  style={{
                    borderWidth: 1,
                    borderColor: '#ddd',
                    borderRadius: 8,
                    padding: 12,
                    marginBottom: 15,
                    fontSize: 16,
                  }}
                  placeholder="Describe the bug you encountered..."
                  multiline
                  numberOfLines={4}
                  value={bugDescription}
                  onChangeText={setBugDescription}
                />
                <TouchableOpacity
                  style={{
                    backgroundColor: '#87CEEB',
                    padding: 15,
                    borderRadius: 8,
                    alignItems: 'center',
                  }}
                  onPress={handleSubmitBugReport}
                >
                  <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
                    Submit Bug Report
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            {activeSection === 'feature' && (
              <View style={{ padding: 20 }}>
                <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 15, color: '#333' }}>
                  Request a Feature
                </Text>
                <TextInput
                  style={{
                    borderWidth: 1,
                    borderColor: '#ddd',
                    borderRadius: 8,
                    padding: 12,
                    marginBottom: 15,
                    fontSize: 16,
                  }}
                  placeholder="Describe the feature you'd like to see..."
                  multiline
                  numberOfLines={4}
                  value={featureRequest.description}
                  onChangeText={(text) => setFeatureRequest(prev => ({ ...prev, description: text }))}
                />
                <TouchableOpacity
                  style={{
                    backgroundColor: '#87CEEB',
                    padding: 15,
                    borderRadius: 8,
                    alignItems: 'center',
                  }}
                  onPress={handleSubmitFeatureRequest}
                >
                  <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
                    Submit Feature Request
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            {activeSection === 'community' && (
              <View style={{ padding: 20 }}>
                <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 15, color: '#333' }}>
                  Community Forum
                </Text>
                <View style={{ marginBottom: 15 }}>
                  <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 5 }}>
                    📧 Email Support
                  </Text>
                  <Text style={{ fontSize: 14, color: '#666', marginBottom: 10 }}>
                    <EMAIL>
                  </Text>
                  <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 5 }}>
                    📞 Phone Support
                  </Text>
                  <Text style={{ fontSize: 14, color: '#666', marginBottom: 10 }}>
                    0787272445
                  </Text>
                  <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 5 }}>
                    💬 Live Chat
                  </Text>
                  <Text style={{ fontSize: 14, color: '#666' }}>
                    Available 24/7 through the app
                  </Text>
                </View>
              </View>
            )}

            {activeSection === 'tutorials' && (
              <View style={{ padding: 20 }}>
                <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 15, color: '#333' }}>
                  Video Tutorials
                </Text>
                <ScrollView>
                  {tutorials.map((tutorial, index) => (
                    <TouchableOpacity
                      key={index}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        padding: 15,
                        backgroundColor: '#f8f9fa',
                        borderRadius: 8,
                        marginBottom: 10,
                      }}
                      onPress={() => handleWatchTutorial(tutorial)}
                    >
                      <View style={{
                        width: 60,
                        height: 40,
                        backgroundColor: '#87CEEB',
                        borderRadius: 6,
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginRight: 15,
                      }}>
                        <Text style={{ color: 'white', fontSize: 18 }}>▶</Text>
                      </View>
                      <View style={{ flex: 1 }}>
                        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 2 }}>
                          {tutorial.title}
                        </Text>
                        <Text style={{ fontSize: 14, color: '#666' }}>
                          {tutorial.duration} • {tutorial.category}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            )}

            {activeSection === 'troubleshoot' && (
              <Text style={{ fontSize: 16, color: '#666', textAlign: 'center' }}>
                Troubleshooting guide will be implemented here...
              </Text>
            )}
          </>
        )}
      </ScrollView>
    </View>
  );
}

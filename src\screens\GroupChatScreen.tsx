import { Ionicons } from "@expo/vector-icons";
import { Audio } from "expo-av";
import React, { useCallback, useEffect, useRef, useState } from "react";
import {
    AccessibilityInfo,
    Alert,
    Animated,
    FlatList,
    Keyboard,
    KeyboardAvoidingView,
    Modal,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
// Import available types and components
import {
    GroupMember,
    GroupMemberPreferences,
    SearchResult,
} from "../types/index";
import { TYPOGRAPHY, SPACING, BORDER_RADIUS } from "../styles/iraChatDesignSystem";
// Removed unused imports: IRACHAT_COLORS, SHADOWS, ANIMATIONS, ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing, DeviceInfo
import { realGroupService } from "../services/realGroupService";
import { Avatar } from "../components/Avatar";
// Firebase imports
import {
  db,
  doc,
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  onSnapshot,
  query,
  orderBy,
  serverTimestamp
} from "../config/firebase";

// Define missing types
interface GroupMessage {
  id: string;
  content: string;
  sender: {
    id: string;
    name: string;
    avatar: string;
  };
  timestamp: Date;
  type: "text" | "image" | "video" | "audio";
  media?: {
    id: string;
    url: string;
    type: "image" | "video" | "audio";
    duration?: number;
  }[];
}

// Mock missing imports for now
const colors = {
  background: "#FFFFFF",
  text: "#000000",
  textSecondary: "#666666",
  primary: "#667eea",
  error: "#FF0000",
  card: "#F8F9FA",
  border: "#E5E5E5",
  shadow: "#000000",
  inputBackground: "#F5F5F5",
  primaryLight: "#8B9FEE",
  white: "#FFFFFF",
  dark: "#000000",
  skeleton: "#E0E0E0",
  skeletonHighlight: "#F0F0F0",
  ripple: "#667eea",
};

const spacing = { xs: 4, sm: 8, md: 16, lg: 24, xl: 32 };
const fontSize = { sm: 12, md: 14, lg: 16, xl: 18 };
const shadows = {
  small: { shadowOpacity: 0.1 },
  medium: { shadowOpacity: 0.2, shadowRadius: 4 },
};
const animations = { timing: { normal: 300 } };
const modalConfig = {
  default: { animationType: "slide" as const, transparent: true },
};

// Mock missing utilities
const isTablet = false;
const isSmallDevice = false;
const handleError = (error: any, _animation?: any) =>
  console.error("Error:", error);
const validateMessage = (message: string) => message.trim().length > 0;
const validateGroupAction = (
  _action: string,
  _isAdmin: boolean,
  _isBlocked: boolean,
) => true;
const getSearchResults = async (
  _query: string,
  _messages: any[],
  _members: any[],
  _contacts: any[],
) => [];

// Real Firebase hooks implementation
const useGroupChat = (groupId: string, userId: string) => {
  const [group, setGroup] = useState<any>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!groupId) return;

    // Listen to group data
    const groupRef = doc(db, 'group_chats', groupId);
    const unsubscribeGroup = onSnapshot(groupRef, (doc) => {
      if (doc.exists()) {
        setGroup({ id: doc.id, ...doc.data() });
      }
      setLoading(false);
    });

    // Listen to messages
    const messagesRef = collection(db, 'group_chats', groupId, 'messages');
    const messagesQuery = query(messagesRef, orderBy('timestamp', 'asc'));
    const unsubscribeMessages = onSnapshot(messagesQuery, (snapshot) => {
      const messagesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date(),
      }));
      setMessages(messagesData);
    });

    return () => {
      unsubscribeGroup();
      unsubscribeMessages();
    };
  }, [groupId]);

  const sendMessage = async (content: string, type: string = 'text') => {
    try {
      await addDoc(collection(db, 'group_chats', groupId, 'messages'), {
        content,
        type,
        senderId: userId,
        timestamp: serverTimestamp(),
        status: 'sent',
      });
      return { success: true };
    } catch (error) {
      console.error('Error sending message:', error);
      return { success: false };
    }
  };

  const sendMedia = async (mediaUrl: string, type: string) => {
    return sendMessage(mediaUrl, type);
  };

  const reactToMessage = async (messageId: string, reaction: string) => {
    try {
      const messageRef = doc(db, 'group_chats', groupId, 'messages', messageId);
      await updateDoc(messageRef, {
        [`reactions.${userId}`]: reaction,
      });
    } catch (error) {
      console.error('Error reacting to message:', error);
    }
  };

  const replyToMessage = async (messageId: string, content: string) => {
    try {
      await addDoc(collection(db, 'group_chats', groupId, 'messages'), {
        content,
        type: 'text',
        senderId: userId,
        timestamp: serverTimestamp(),
        replyTo: messageId,
        status: 'sent',
      });
    } catch (error) {
      console.error('Error replying to message:', error);
    }
  };

  const deleteMessage = async (messageId: string) => {
    try {
      await deleteDoc(doc(db, 'group_chats', groupId, 'messages', messageId));
    } catch (error) {
      console.error('Error deleting message:', error);
    }
  };

  const editMessage = async (messageId: string, newContent: string) => {
    try {
      const messageRef = doc(db, 'group_chats', groupId, 'messages', messageId);
      await updateDoc(messageRef, {
        content: newContent,
        edited: true,
        editedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error editing message:', error);
    }
  };

  const pinMessage = async (messageId: string) => {
    try {
      const messageRef = doc(db, 'group_chats', groupId, 'messages', messageId);
      await updateDoc(messageRef, {
        pinned: true,
        pinnedAt: serverTimestamp(),
        pinnedBy: userId,
      });
    } catch (error) {
      console.error('Error pinning message:', error);
    }
  };

  const loadMoreMessages = async () => {
    // Implementation for loading more messages
  };

  return {
    group,
    messages,
    loading,
    sendMessage,
    sendMedia,
    reactToMessage,
    replyToMessage,
    deleteMessage,
    editMessage,
    pinMessage,
    loadMoreMessages,
  };
};

const useMediaUpload = () => {
  const [uploading, setUploading] = useState(false);

  const uploadMedia = async (_file: any) => {
    setUploading(true);
    try {
      // Real media upload implementation would go here
      setUploading(false);
      return { success: true, url: 'placeholder-url' };
    } catch (_error) {
      setUploading(false);
      return { success: false };
    }
  };

  return { uploadMedia, uploading };
};

const useMentionNotifications = (config: any) => {
  const sendMentionNotifications = async () => {
    // Real mention notification implementation
    console.log('Sending mention notifications with config:', config);
  };

  return { sendMentionNotifications };
};
const useResponsive = () => ({
  isLandscape: false,
  wp: (val: number) => val,
  hp: (val: number) => val,
});

// Mock missing components
const GroupHeader = ({ onBackPress: _onBackPress, onInfoPress: _onInfoPress, onSettingsPress: _onSettingsPress }: any) => (
  <View style={{ height: 60, backgroundColor: colors.primary }} />
);
const GroupDetails = ({ onClose: _onClose }: any) => <View />;
const GroupSettings = ({ onClose: _onClose }: any) => <View />;

interface GroupChatScreenProps {
  groupId: string;
  currentUserId: string;
  isAdmin: boolean;
  onBack: () => void;
  navigation: any;
}

export const GroupChatScreen: React.FC<GroupChatScreenProps> = ({
  groupId,
  currentUserId,
  isAdmin,
  onBack: _onBack,
  navigation,
}) => {
  const __insets = useSafeAreaInsets();
  const { isLandscape: __isLandscape, wp: __wp, hp: __hp } = useResponsive();
  const [message, setMessage] = useState("");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [replyingTo, setReplyingTo] = useState<any | null>(null);
  const [showMediaGrid, setShowMediaGrid] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const inputRef = useRef<TextInput>(null);
  const scrollY = useRef(new Animated.Value(0)).current;
  // Add missing state variables
  const [preferences, setPreferences] = useState<GroupMemberPreferences>({
    notifications: { messages: true, mentions: true, reactions: true },
    privacy: { readReceipts: true, lastSeen: true, profilePhoto: true },
    media: { autoDownload: true, quality: "medium" },
    isMuted: false,
    isArchived: false,
    isLocked: false,
    hiddenMessages: [],
    hiddenUpdates: [],
  });
  const [showSettings, setShowSettings] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [contacts, setContacts] = useState<any[]>([]);
  const [__selectedMember, __setSelectedMember] = useState<GroupMember | null>(
    null,
  );
  const [__selectedContact, __setSelectedContact] = useState<any | null>(null);
  const [showMemberProfile, setShowMemberProfile] = useState(false);
  const [showContactProfile, setShowContactProfile] = useState(false);
  const [typingMembers, setTypingMembers] = useState<string[]>([]);
  const [recordingMembers, setRecordingMembers] = useState<string[]>([]);
  const [onlineMembers, setOnlineMembers] = useState<string[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [lastReadMessageId, setLastReadMessageId] = useState<string>("");
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isScreenReaderEnabled, setIsScreenReaderEnabled] = useState(false);
  const [isRecording, setIsRecording] = useState(false);

  // Add missing animations
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  // Additional state (removing duplicates)
  const [groupDetails, setGroupDetails] = useState<{
    info: any;
    stats: {
      memberCount: number;
      messageCount: number;
      mediaCount: number;
      adminCount: number;
    };
    recentActivity: {
      type: "message" | "member" | "media";
      timestamp: number;
      description: string;
    }[];
  } | null>(null);
  // Additional state (removing duplicates)
  const [__searchResults, __setSearchResults] = useState<SearchResult[]>([]);
  const [__isSearching, __setIsSearching] = useState(false);
  const [__searchFilters, __setSearchFilters] = useState({
    messages: true,
    members: true,
    contacts: true,
  });
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [playingVoiceMessage, setPlayingVoiceMessage] = useState<string | null>(
    null,
  );
  const [sound, setSound] = useState<Audio.Sound | null>(null);

  const {
    group,
    messages: _messages,
    loading: _loading,
    sendMessage,
    sendMedia,
    reactToMessage,
    replyToMessage,
    deleteMessage,
    editMessage: __editMessage,
    pinMessage: __pinMessage,
    loadMoreMessages,
  } = useGroupChat(groupId, currentUserId);

  const { uploadMedia, uploading: __uploading } = useMediaUpload();
  const { sendMentionNotifications: __sendMentionNotifications } = useMentionNotifications({
    currentUserId,
  });

  useEffect(() => {
    // Animate in
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: animations.timing.normal,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: animations.timing.normal,
        useNativeDriver: true,
      }),
    ]).start();

    loadMessages();
    loadMembers();
    loadMemberPreferences();
    loadGroupDetails();
    loadContacts();
  }, [fadeAnimation, slideAnimation, loadMessages, loadMembers, loadMemberPreferences, loadGroupDetails, loadContacts]);

  const loadContacts = useCallback(async () => {
    try {
      // Load user contacts for mentions and invites
      const userContacts = await realGroupService.getUserContacts(currentUserId);
      setContacts(userContacts);
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  }, [currentUserId, shakeAnimation]);

  const loadMessages = useCallback(async () => {
    try {
      // ... existing loadMessages logic ...
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  }, [shakeAnimation]);

  const loadMembers = useCallback(async () => {
    try {
      // Load group members from Firebase
      const groupMembers = await realGroupService.getGroupMembers(groupId);
      setMembers(groupMembers);

      // Update online members
      const onlineIds = groupMembers.filter(member => member.isOnline).map(member => member.id);
      setOnlineMembers(onlineIds);

      // Initialize recording members as empty (will be updated via real-time listeners)
      setRecordingMembers([]);
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  }, [groupId, shakeAnimation]);

  const loadMemberPreferences = useCallback(async () => {
    try {
      // Load member preferences from Firebase
      const userPrefs = await realGroupService.getMemberPreferences(groupId, currentUserId);
      if (userPrefs) {
        setPreferences(userPrefs);
      }
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  }, [groupId, currentUserId, shakeAnimation]);

  const loadGroupDetails = useCallback(async () => {
    try {
      // Load group details from Firebase
      const details = await realGroupService.getGroupDetails(groupId);
      setGroupDetails({
        info: details,
        stats: {
          memberCount: details.memberCount || 0,
          messageCount: details.messageCount || 0,
          mediaCount: details.mediaCount || 0,
          adminCount: details.adminCount || 0,
        },
        recentActivity: details.recentActivity || [],
      });
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  }, [groupId, shakeAnimation]);

  const handleSendMessage = async () => {
    try {
      validateMessage(message);
      validateGroupAction("send", isAdmin, false);

      // Send the message using the hook
      await sendMessage();

      // Clear the input and reply state
      setMessage("");
      setReplyingTo(null);
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  };

  const handleGroupAction = async (action: string) => {
    try {
      validateGroupAction(action, isAdmin, false);

      // ... existing handleGroupAction logic ...
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  };

  const handleHideContent = async (
    _contentId: string,
    _contentType: "message" | "update",
  ) => {
    try {
      // ... existing handleHideContent logic ...
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  };

  const __handleDeleteMessage = async (__messageId: string) => {
    try {
      validateGroupAction("deleteMessage", isAdmin, false);

      // ... existing handleDeleteMessage logic ...
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  };

  const handleShareGroup = async () => {
    try {
      // ... existing handleShareGroup logic ...
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  };

  const handleCopyLink = async () => {
    try {
      // ... existing handleCopyLink logic ...
    } catch (error) {
      handleError(error, shakeAnimation);
    }
  };

  const handleViewMembers = () => {
    setShowSettings(true);
  };

  const handleViewMedia = () => {
    setShowMediaGrid(true);
  };

  // Add missing handler functions
  const handleAddMember = async (userId: string) => {
    console.log("Adding member:", userId);
  };

  const handleRemoveMember = async (userId: string) => {
    console.log("Removing member:", userId);
  };

  const handlePromoteMember = async (userId: string) => {
    console.log("Promoting member:", userId);
  };

  const handleDemoteMember = async (userId: string) => {
    console.log("Demoting member:", userId);
  };

  const handleBlockMember = async (userId: string) => {
    console.log("Blocking member:", userId);
  };

  const handleUnblockMember = async (userId: string) => {
    console.log("Unblocking member:", userId);
  };

  // markMessagesAsRead function moved to useCallback below

  const __handleSearch = useCallback(async (query: string) => {
    try {
      // setIsSearching(true);
      const results = await getSearchResults(
        query,
        _messages,
        members,
        contacts,
      );
      // setSearchResults(results);
      return results;
    } catch (error) {
      handleError(error);
      return [];
    } finally {
      // setIsSearching(false);
    }
  }, [_messages, members, contacts]);

  const __handleSearchResultPress = useCallback((result: SearchResult) => {
    switch (result.type) {
      case "message":
        // Find the message index and scroll to it
        const messageIndex = _messages.findIndex((m: any) => m.id === result.id);
        if (messageIndex !== -1) {
          flatListRef.current?.scrollToIndex({
            index: messageIndex,
            animated: true,
            viewPosition: 0.5,
          });
        }
        break;
      case "member":
        // setSelectedMember(members.find((m: any) => m.id === result.id) || null);
        setShowMemberProfile(true);
        break;
      case "contact":
        // setSelectedContact(contacts.find((c) => c.id === result.id));
        setShowContactProfile(true);
        break;
      case "user":
        // Find the user in either members or contacts
        const user =
          members.find((m) => m.id === result.id) ||
          contacts.find((c) => c.id === result.id);
        if (user) {
          if ("role" in user) {
            // setSelectedMember(user as GroupMember);
            setShowMemberProfile(true);
          } else {
            // setSelectedContact(user);
            setShowContactProfile(true);
          }
        }
        break;
    }
  }, [_messages, members, contacts]);

  const __handleSearchFilterChange = useCallback((filters: { [key: string]: boolean }) => {
    // Update search filters
    // setSearchFilters(prevFilters => ({
    //   ...prevFilters,
    //   ...filters,
    // }));
    console.log("Search filters updated:", filters);

    // Re-run search with new filters if there's an active search
    // Note: searchQuery would need to be added as state if we want to track it
    console.log("Search filters updated:", filters);
  }, []);

  const handleMessageLongPress = (message: any) => {
    // Show message action sheet with options
    Alert.alert(
      "Message Options",
      `Options for message: ${message.content?.substring(0, 50)}...`,
      [
        {
          text: "Reply",
          onPress: () => {
            setReplyingTo(message);
            // Focus the input for replying
            setTimeout(() => {
              inputRef.current?.focus();
            }, 100);
            replyToMessage();
          },
        },
        {
          text: "React",
          onPress: () => reactToMessage(),
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            Alert.alert(
              "Delete Message",
              "Are you sure you want to delete this message?",
              [
                { text: "Cancel", style: "cancel" },
                {
                  text: "Delete",
                  style: "destructive",
                  onPress: () => deleteMessage(),
                },
              ]
            );
          },
        },
        { text: "Cancel", style: "cancel" },
      ]
    );
  };

  // Function to handle voice message recording
  const startRecording = async () => {
    try {
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY,
      );
      setRecording(recording);
      setIsRecording(true);
      setTypingMembers((prev) => [...prev, currentUserId]);
    } catch (error) {
      console.error("Failed to start recording:", error);
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    try {
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      setRecording(null);
      setIsRecording(false);
      setTypingMembers((prev) => prev.filter((id) => id !== currentUserId));

      if (uri) {
        // Here you would typically upload the voice message to your backend
        // and send it as a message
        const voiceMessage = {
          id: Date.now().toString(),
          type: "voice",
          uri,
          duration: 0, // You would calculate this
          sender: currentUserId,
          timestamp: new Date().toISOString(),
        };
        // Upload and send the voice message
        const uploadedUri = await uploadMedia();
        const finalVoiceMessage = {
          ...voiceMessage,
          uri: uploadedUri,
        };
        await sendMedia();
        console.log("Voice message sent:", finalVoiceMessage);
      }
    } catch (error) {
      console.error("Failed to stop recording:", error);
    }
  };

  // Function to play voice messages
  const playVoiceMessage = async (uri: string) => {
    try {
      if (sound) {
        await sound.unloadAsync();
      }

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri },
        { shouldPlay: true },
      );
      setSound(newSound);
      setPlayingVoiceMessage(uri);

      newSound.setOnPlaybackStatusUpdate((status) => {
        if ((status as any).didJustFinish) {
          setPlayingVoiceMessage(null);
        }
      });
    } catch (error) {
      console.error("Failed to play voice message:", error);
    }
  };

  // Function to stop playing voice message
  const stopVoiceMessage = async () => {
    if (sound) {
      await sound.stopAsync();
      await sound.unloadAsync();
      setSound(null);
      setPlayingVoiceMessage(null);
    }
  };

  // Cleanup audio resources
  useEffect(() => {
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
      if (recording) {
        recording.stopAndUnloadAsync();
      }
    };
  }, [sound, recording]);

  // Function to render voice message
  const renderVoiceMessage = (message: GroupMessage) => {
    const isPlaying = playingVoiceMessage === message.media?.[0]?.url;

    return (
      <TouchableOpacity
        style={styles.voiceMessageContainer}
        onPress={() =>
          isPlaying
            ? stopVoiceMessage()
            : playVoiceMessage(message.media?.[0]?.url || "")
        }
      >
        <Ionicons
          name={isPlaying ? "pause" : "play"}
          size={24}
          color={colors.primary}
        />
        <View style={styles.voiceMessageInfo}>
          <Text style={styles.voiceMessageDuration}>
            {message.media?.[0]?.duration || 0}s
          </Text>
          <View style={styles.voiceMessageWaveform}>
            {/* Here you would render a waveform visualization */}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Check screen reader status
  useEffect(() => {
    const checkScreenReader = async () => {
      const enabled = await AccessibilityInfo.isScreenReaderEnabled();
      setIsScreenReaderEnabled(enabled);
    };
    checkScreenReader();
  }, []);

  // Handle keyboard events
  useEffect(() => {
    const keyboardWillShow = (e: any) => {
      setIsKeyboardVisible(true);
      setKeyboardHeight(e.endCoordinates.height);
    };
    const keyboardWillHide = () => {
      setIsKeyboardVisible(false);
      setKeyboardHeight(0);
    };

    const showSubscription = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow",
      keyboardWillShow,
    );
    const hideSubscription = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide",
      keyboardWillHide,
    );

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  // Optimize performance for long lists
  const getItemLayout = (data: any, index: number) => ({
    length: isTablet ? 48 : 40, // hp(12) : hp(10) - using fixed values
    offset: (isTablet ? 48 : 40) * index,
    index,
  });

  const renderMessage = ({ item: message }: { item: GroupMessage }) => {
    const isCurrentUser = message.sender.id === currentUserId;
    const messageStyle = isCurrentUser
      ? styles.sentMessage
      : styles.receivedMessage;
    const containerStyle = isCurrentUser
      ? styles.sentContainer
      : styles.receivedContainer;

    const handleProfilePress = () => {
      const member = members.find((m) => m.id === message.sender.id);
      if (member) {
        // setSelectedMember(member);
        setShowMemberProfile(true);
      }
    };

    return (
      <View
        style={[styles.messageContainer, containerStyle]}
        accessible={isScreenReaderEnabled}
        accessibilityLabel={`${message.sender.name}: ${message.content}`}
        accessibilityRole="text"
      >
        {!isCurrentUser && (
          <TouchableOpacity
            style={styles.avatarContainer}
            onPress={handleProfilePress}
            accessibilityLabel={`View ${message.sender.name}'s profile`}
            accessibilityRole="button"
          >
            <Avatar
                  name={message.sender.name}
                  imageUrl={message.sender.avatar}
                  size="small"
                  showOnlineStatus={false}
                />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[styles.messageBubble, messageStyle]}
          onLongPress={() => handleMessageLongPress(message)}
          accessibilityLabel="Message options"
          accessibilityRole="button"
        >
          {!isCurrentUser && (
            <TouchableOpacity
              onPress={handleProfilePress}
              style={styles.senderInfo}
              accessibilityLabel={`View ${message.sender.name}'s profile`}
              accessibilityRole="button"
            >
              <Text
                style={[
                  styles.senderName,
                  isTablet && styles.senderNameTablet,
                  isSmallDevice && styles.senderNameSmall,
                ]}
              >
                {message.sender.name}
              </Text>
              <Text
                style={[
                  styles.senderUsername,
                  isTablet && styles.senderUsernameTablet,
                  isSmallDevice && styles.senderUsernameSmall,
                ]}
              >
                @{message.sender?.name || (message as any).senderName}
              </Text>
            </TouchableOpacity>
          )}
          {message.type === "audio" ? (
            renderVoiceMessage(message)
          ) : (
            <Text
              style={[
                styles.messageText,
                isTablet && styles.messageTextTablet,
                isSmallDevice && styles.messageTextSmall,
              ]}
            >
              {message.content}
            </Text>
          )}
          <Text
            style={[
              styles.timestamp,
              isTablet && styles.timestampTablet,
              isSmallDevice && styles.timestampSmall,
            ]}
          >
            {message.timestamp.toLocaleTimeString()}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const isBlocked = members.find((m) => m.id === currentUserId)?.isBlocked;

  // Function to mark messages as read
  const markMessagesAsRead = useCallback(() => {
    if (_messages.length > 0) {
      const lastMessage = _messages[_messages.length - 1];
      setLastReadMessageId((lastMessage as any).id);
      setUnreadCount(0);
      // Here you would typically update the backend to mark messages as read
    }
  }, [_messages]);

  // Update unread count when new messages arrive
  useEffect(() => {
    if (_messages.length > 0 && lastReadMessageId) {
      const unreadMessages = _messages.filter(
        (message: any) => message.id > lastReadMessageId,
      );
      setUnreadCount(unreadMessages.length);
    }
  }, [_messages, lastReadMessageId]);

  // Mark messages as read when screen is focused
  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", () => {
      markMessagesAsRead();
    });

    return unsubscribe;
  }, [navigation, markMessagesAsRead]);

  // Mark messages as read when scrolling to bottom
  const handleScrollToBottom = useCallback(() => {
    markMessagesAsRead();
    // Also scroll to bottom if needed
    flatListRef.current?.scrollToEnd({ animated: true });
  }, [markMessagesAsRead]);

  return (
    <View style={styles.container}>
      <GroupHeader
        group={group}
        unreadCount={unreadCount}
        typingMembers={typingMembers}
        recordingMembers={recordingMembers}
        onlineMembers={onlineMembers}
        onBackPress={() => navigation.goBack()}
        onInfoPress={() => {
          console.log("🔄 GroupChatScreen - navigating to group settings");
          // Navigate to group settings instead of showing modal
          try {
            navigation.navigate('GroupSettings', {
              groupId: groupId,
              groupName: group?.name || 'Group',
              groupAvatar: group?.photo || '',
            });
          } catch (error) {
            console.error("❌ Navigation error:", error);
            // Fallback to modal if navigation fails
            setShowDetails(true);
          }
        }}
        onSettingsPress={() => setShowSettings(true)}
      />

      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={_messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          onEndReached={loadMoreMessages}
          onEndReachedThreshold={0.5}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { y: scrollY } } }],
            { useNativeDriver: false }
          )}
          scrollEventThrottle={16}
          contentContainerStyle={[
            styles.listContent,
            isKeyboardVisible && { paddingBottom: keyboardHeight },
          ]}
          showsVerticalScrollIndicator={false}
          getItemLayout={getItemLayout}
          maxToRenderPerBatch={10}
          windowSize={5}
          removeClippedSubviews={true}
          initialNumToRender={10}
        />

        {/* Scroll to bottom button */}
        <TouchableOpacity
          style={styles.scrollToBottomButton}
          onPress={handleScrollToBottom}
          accessibilityLabel="Scroll to bottom"
          accessibilityRole="button"
        >
          <Ionicons name="chevron-down" size={24} color={colors.primary} />
        </TouchableOpacity>

        {replyingTo && (
          <View style={styles.replyContainer}>
            <View style={styles.replyContent}>
              <Text style={styles.replyLabel}>Replying to {replyingTo.sender?.name || 'Unknown'}</Text>
              <Text style={styles.replyText} numberOfLines={1}>
                {replyingTo.content || replyingTo.text || 'Message'}
              </Text>
            </View>
            <TouchableOpacity
              style={styles.replyCloseButton}
              onPress={() => setReplyingTo(null)}
              accessibilityLabel="Cancel reply"
              accessibilityRole="button"
            >
              <Ionicons name="close" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
        )}

        <View
          style={[
            styles.inputContainer,
            isKeyboardVisible && styles.inputContainerKeyboardVisible,
            isBlocked && styles.inputContainerBlocked,
          ]}
        >
          <TouchableOpacity
            style={[styles.voiceButton, isBlocked && styles.disabledButton]}
            onPressIn={isBlocked ? undefined : startRecording}
            onPressOut={isBlocked ? undefined : stopRecording}
            disabled={isBlocked}
            accessibilityLabel={
              isBlocked ? "Recording disabled" : (isRecording ? "Stop recording" : "Start recording")
            }
            accessibilityRole="button"
          >
            <Ionicons
              name={isRecording ? "stop" : "mic"}
              size={isTablet ? 28 : 24}
              color={isRecording ? colors.error : colors.primary}
            />
          </TouchableOpacity>
          <TextInput
            ref={inputRef}
            style={[
              styles.input,
              isTablet && styles.inputTablet,
              isSmallDevice && styles.inputSmall,
              isBlocked && styles.disabledInput,
            ]}
            placeholder={isBlocked ? "You are blocked from messaging" : "Type a message..."}
            placeholderTextColor={colors.textSecondary}
            value={message}
            onChangeText={isBlocked ? undefined : setMessage}
            multiline
            maxLength={1000}
            editable={!isBlocked}
            accessibilityLabel={isBlocked ? "Message input disabled" : "Message input"}
            accessibilityRole="none"
          />

          <TouchableOpacity
            style={[styles.mediaButton, isBlocked && styles.disabledButton]}
            onPress={isBlocked ? undefined : () => setShowMediaGrid(!showMediaGrid)}
            disabled={isBlocked}
            accessibilityLabel={isBlocked ? "Media disabled" : "Open media picker"}
            accessibilityRole="button"
          >
            <Ionicons
              name="attach-outline"
              size={isTablet ? 28 : 24}
              color={isBlocked ? colors.textSecondary : colors.primary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.emojiButton, isBlocked && styles.disabledButton]}
            onPress={isBlocked ? undefined : () => setShowEmojiPicker(!showEmojiPicker)}
            disabled={isBlocked}
            accessibilityLabel={isBlocked ? "Emoji disabled" : "Open emoji picker"}
            accessibilityRole="button"
          >
            <Ionicons
              name="happy-outline"
              size={isTablet ? 28 : 24}
              color={isBlocked ? colors.textSecondary : colors.primary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.sendButton, isBlocked && styles.disabledButton]}
            onPress={isBlocked ? undefined : handleSendMessage}
            disabled={isBlocked || !message.trim()}
            accessibilityLabel={isBlocked ? "Send disabled" : "Send message"}
            accessibilityRole="button"
            accessibilityState={{ disabled: isBlocked || !message.trim() }}
          >
            <Ionicons
              name="send"
              size={isTablet ? 28 : 24}
              color={message.trim() ? colors.primary : colors.textSecondary}
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>

      <Modal
        visible={showDetails}
        animationType={modalConfig.default.animationType}
        transparent={modalConfig.default.transparent}
        onRequestClose={() => setShowDetails(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <GroupDetails
              group={groupDetails?.info}
              stats={groupDetails?.stats}
              recentActivity={groupDetails?.recentActivity}
              onShare={handleShareGroup}
              onCopyLink={handleCopyLink}
              onViewMembers={handleViewMembers}
              onViewMedia={handleViewMedia}
              onClose={() => setShowDetails(false)}
            />
          </View>
        </View>
      </Modal>

      <Modal
        visible={showSettings}
        animationType={modalConfig.default.animationType}
        transparent={modalConfig.default.transparent}
        onRequestClose={() => setShowSettings(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <GroupSettings
              preferences={preferences}
              isAdmin={isAdmin}
              groupId={groupId}
              members={members}
              onAction={handleGroupAction}
              onHideContent={handleHideContent}
              onAddMember={handleAddMember}
              onRemoveMember={handleRemoveMember}
              onPromoteMember={handlePromoteMember}
              onDemoteMember={handleDemoteMember}
              onBlockMember={handleBlockMember}
              onUnblockMember={handleUnblockMember}
              onClose={() => setShowSettings(false)}
            />
          </View>
        </View>
      </Modal>

      <Modal
        visible={showMemberProfile}
        animationType={modalConfig.default.animationType}
        transparent={modalConfig.default.transparent}
        onRequestClose={() => setShowMemberProfile(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {/* Member profile content */}
          </View>
        </View>
      </Modal>

      <Modal
        visible={showContactProfile}
        animationType={modalConfig.default.animationType}
        transparent={modalConfig.default.transparent}
        onRequestClose={() => setShowContactProfile(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {/* Contact profile content */}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.background,
    ...shadows.small,
  },
  backButton: {
    padding: spacing.xs,
  },
  groupInfo: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  groupName: {
    fontSize: fontSize.lg,
    fontWeight: "600",
    color: colors.text,
  },
  memberCount: {
    fontSize: fontSize.sm,
    color: colors.textSecondary,
  },
  settingsButton: {
    padding: spacing.xs,
  },
  messageContainer: {
    flexDirection: "row",
    marginVertical: 4,
    paddingHorizontal: 12,
  },
  sentContainer: {
    justifyContent: "flex-end",
  },
  receivedContainer: {
    justifyContent: "flex-start",
  },
  avatarContainer: {
    marginRight: 8,
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  avatarTablet: {
    width: 28,
    height: 28,
    borderRadius: 14,
  },
  avatarSmall: {
    width: 28,
    height: 28,
    borderRadius: 14,
  },
  messageBubble: {
    maxWidth: "80%",
    padding: 12,
    borderRadius: 16,
  },
  sentMessage: {
    backgroundColor: colors.primary,
    borderTopRightRadius: 4,
  },
  receivedMessage: {
    backgroundColor: colors.card,
    borderTopLeftRadius: 4,
  },
  senderInfo: {
    marginBottom: 4,
  },
  senderName: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 2,
  },
  senderNameTablet: {
    fontSize: 12,
  },
  senderNameSmall: {
    fontSize: 10,
  },
  senderUsername: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  senderUsernameTablet: {
    fontSize: 10,
  },
  senderUsernameSmall: {
    fontSize: 8,
  },
  messageText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 20,
  },
  messageTextTablet: {
    fontSize: 12,
    lineHeight: 16,
  },
  messageTextSmall: {
    fontSize: 10,
    lineHeight: 14,
  },
  mediaContainer: {
    marginTop: 8,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 4,
  },
  mediaItem: {
    width: 100,
    height: 100,
    borderRadius: 8,
    overflow: "hidden",
  },
  mediaImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  timestamp: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 4,
    alignSelf: "flex-end",
  },
  timestampTablet: {
    fontSize: 10,
  },
  timestampSmall: {
    fontSize: 8,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.background,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    ...Platform.select({
      ios: {
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  inputContainerKeyboardVisible: {
    paddingBottom: Platform.OS === "ios" ? spacing.sm : 0,
  },
  voiceButton: {
    padding: spacing.xs,
    marginRight: spacing.sm,
  },
  input: {
    flex: 1,
    minHeight: 40,
    maxHeight: 100,
    backgroundColor: colors.inputBackground,
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    marginRight: spacing.sm,
    fontSize: fontSize.md,
    color: colors.text,
  },
  inputTablet: {
    fontSize: fontSize.sm,
    paddingVertical: spacing.xs,
  },
  inputSmall: {
    fontSize: fontSize.sm,
    paddingVertical: spacing.xs,
  },
  sendButton: {
    padding: spacing.xs,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    width: "90%",
    maxHeight: "80%",
    backgroundColor: colors.background,
    borderRadius: 12,
    ...shadows.medium,
  },
  blockedContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xl,
  },
  blockedText: {
    fontSize: fontSize.lg,
    color: colors.error,
    marginTop: spacing.md,
    textAlign: "center",
  },
  blockedSubtext: {
    fontSize: fontSize.md,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: "center",
  },
  lockedContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xl,
  },
  lockedText: {
    fontSize: fontSize.lg,
    color: colors.primary,
    marginTop: spacing.md,
    textAlign: "center",
  },
  lockedSubtext: {
    fontSize: fontSize.md,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: "center",
  },
  listContent: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  searchContainer: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.background,
    ...shadows.small,
  },
  searchFilters: {
    flexDirection: "row",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  searchFilter: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: spacing.md,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    borderRadius: 16,
    backgroundColor: colors.inputBackground,
  },
  searchFilterText: {
    fontSize: fontSize.sm,
    color: colors.text,
    marginLeft: spacing.xs,
  },
  searchFilterActive: {
    backgroundColor: colors.primaryLight,
  },
  searchFilterTextActive: {
    color: colors.primary,
    fontWeight: "600",
  },
  voiceMessageContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
    backgroundColor: colors.card,
    borderRadius: 8,
    marginVertical: 4,
  },
  voiceMessageInfo: {
    marginLeft: 12,
    flex: 1,
  },
  voiceMessageDuration: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  voiceMessageWaveform: {
    height: 20,
    backgroundColor: colors.background,
    borderRadius: 10,
  },
  inputContainerBlocked: {
    opacity: 0.5,
  },
  disabledButton: {
    opacity: 0.5,
  },
  disabledInput: {
    opacity: 0.5,
    backgroundColor: colors.card,
  },
  mediaButton: {
    padding: isTablet ? 12 : 10,
    marginRight: isTablet ? 8 : 6,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emojiButton: {
    padding: isTablet ? 12 : 10,
    marginRight: isTablet ? 8 : 6,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  replyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  replyContent: {
    flex: 1,
  },
  replyLabel: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    color: colors.primary,
    marginBottom: 2,
  },
  replyText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: colors.textSecondary,
  },
  replyCloseButton: {
    padding: SPACING.sm,
  },
  scrollToBottomButton: {
    position: 'absolute',
    bottom: isTablet ? 120 : 100,
    right: isTablet ? 24 : 16,
    width: isTablet ? 56 : 48,
    height: isTablet ? 56 : 48,
    borderRadius: isTablet ? 28 : 24,
    backgroundColor: colors.card,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
});

import { UnifiedChatItem } from '../components/ModernChatItem';

// 🚀 ENHANCED UTILITY FUNCTIONS WITH PERFORMANCE OPTIMIZATION
export const formatTime = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return "now";
  if (minutes < 60) return `${minutes}m`;
  if (hours < 24) return `${hours}h`;
  if (days === 1) return "yesterday";
  if (days < 7) return `${days}d`;
  return date.toLocaleDateString();
};

// Enhanced message preview with better type handling
export const getMessagePreview = (item: UnifiedChatItem): string => {
  if (item.isTyping) return "typing...";

  switch (item.lastMessageType) {
    case "image":
      return "📸 Photo";
    case "video":
      return "🎥 Video";
    case "voice":
    case "audio":
      return "🎵 Voice message";
    case "document":
      return "📄 Document";
    default:
      return item.lastMessage || "No messages yet";
  }
};

// Get appropriate icon for message type
export const getMessageTypeIcon = (type?: string): string | null => {
  switch (type) {
    case 'image': return 'image';
    case 'video': return 'videocam';
    case 'voice':
    case 'audio': return 'musical-notes';
    case 'document': return 'document';
    default: return null;
  }
};

// Enhanced connection status indicator
export const getConnectionStatusColor = (connectionStatus: 'connected' | 'connecting' | 'disconnected'): string => {
  switch (connectionStatus) {
    case 'connected': return '#10B981';
    case 'connecting': return '#F59E0B';
    case 'disconnected': return '#EF4444';
    default: return '#9CA3AF';
  }
};

// Performance optimized chat item key extractor
export const keyExtractor = (item: UnifiedChatItem): string => item.id;

// Optimized item layout for better performance
export const getItemLayout = (data: UnifiedChatItem[] | null | undefined, index: number) => ({
  length: 80, // Approximate height of each chat item
  offset: 80 * index,
  index,
});

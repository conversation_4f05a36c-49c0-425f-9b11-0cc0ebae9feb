import { useState, useEffect, useCallback } from "react";
import {
    Alert,
    Text,
    TouchableOpacity,
    View,
    ActivityIndicator,
    Image,
    FlatList,
    Modal,
    TextInput,
    Dimensions,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { useSelector } from "react-redux";
import { RootState } from "../src/redux/store";
import { realChatService } from "../src/services/realChatService";
import { navigationService } from "../src/services/navigationService";

interface ChatItem {
  id: string;
  name: string;
  avatar?: string;
  lastMessage?: string;
  timestamp?: string;
  unreadCount?: number;
  isGroup?: boolean;
  participants?: string[];
  isArchived?: boolean;
  isPinned?: boolean;
  isMuted?: boolean;
}

interface User {
  id: string;
  phoneNumber: string;
  displayName?: string;
  avatar?: string;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function ChatManagement() {
  const [chats, setChats] = useState<ChatItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedChats, setSelectedChats] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'individual' | 'group' | 'archived'>('all');
  const [sortBy, setSortBy] = useState<'recent' | 'name' | 'unread'>('recent');
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const user = useSelector((state: RootState) => state.auth.user);

  useEffect(() => {
    loadChats();
  }, []);

  const loadChats = async () => {
    try {
      setLoading(true);
      const chatData = await realChatService.getUserChats(user?.id || '');
      setChats(chatData);
    } catch (error) {
      console.error('Error loading chats:', error);
      Alert.alert('Error', 'Failed to load chats');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadChats();
    setRefreshing(false);
  }, []);

  const toggleChatSelection = (chatId: string) => {
    const newSelected = new Set(selectedChats);
    if (newSelected.has(chatId)) {
      newSelected.delete(chatId);
    } else {
      newSelected.add(chatId);
    }
    setSelectedChats(newSelected);
    setShowBulkActions(newSelected.size > 0);
  };

  const selectAllChats = () => {
    const filteredChatIds = getFilteredAndSortedChats().map(chat => chat.id);
    setSelectedChats(new Set(filteredChatIds));
    setShowBulkActions(true);
  };

  const clearSelection = () => {
    setSelectedChats(new Set());
    setShowBulkActions(false);
  };

  const handleBulkArchive = async () => {
    try {
      const chatIds = Array.from(selectedChats);
      await Promise.all(chatIds.map(id => realChatService.archiveChat(id)));
      await loadChats();
      clearSelection();
      Alert.alert('Success', `${chatIds.length} chat(s) archived`);
    } catch (error) {
      Alert.alert('Error', 'Failed to archive chats');
    }
  };

  const handleBulkDelete = async () => {
    Alert.alert(
      'Delete Chats',
      `Are you sure you want to delete ${selectedChats.size} chat(s)? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const chatIds = Array.from(selectedChats);
              await Promise.all(chatIds.map(id => realChatService.deleteChat(id)));
              await loadChats();
              clearSelection();
              Alert.alert('Success', `${chatIds.length} chat(s) deleted`);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete chats');
            }
          }
        }
      ]
    );
  };

  const handleBulkMute = async () => {
    try {
      const chatIds = Array.from(selectedChats);
      await Promise.all(chatIds.map(id => realChatService.muteChat(id)));
      await loadChats();
      clearSelection();
      Alert.alert('Success', `${chatIds.length} chat(s) muted`);
    } catch (error) {
      Alert.alert('Error', 'Failed to mute chats');
    }
  };

  const getFilteredAndSortedChats = () => {
    let filteredChats = chats.filter(chat => {
      // Search filter
      const matchesSearch = chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           chat.lastMessage?.toLowerCase().includes(searchQuery.toLowerCase());
      
      if (!matchesSearch) return false;

      // Type filter
      switch (filterType) {
        case 'individual':
          return !chat.isGroup;
        case 'group':
          return chat.isGroup;
        case 'archived':
          return chat.isArchived;
        default:
          return !chat.isArchived; // 'all' shows non-archived chats
      }
    });

    // Sort chats
    filteredChats.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'unread':
          return (b.unreadCount || 0) - (a.unreadCount || 0);
        case 'recent':
        default:
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;
          
          const aTime = new Date(a.timestamp || 0).getTime();
          const bTime = new Date(b.timestamp || 0).getTime();
          return bTime - aTime;
      }
    });

    return filteredChats;
  };

  const renderChatItem = ({ item }: { item: ChatItem }) => (
    <TouchableOpacity
      onPress={() => toggleChatSelection(item.id)}
      style={{
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
        borderWidth: selectedChats.has(item.id) ? 2 : 0,
        borderColor: selectedChats.has(item.id) ? '#87CEEB' : 'transparent',
      }}
    >
      {/* Selection Checkbox */}
      <View style={{
        width: 24,
        height: 24,
        borderRadius: 12,
        borderWidth: 2,
        borderColor: selectedChats.has(item.id) ? '#87CEEB' : '#ddd',
        backgroundColor: selectedChats.has(item.id) ? '#87CEEB' : 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
      }}>
        {selectedChats.has(item.id) && (
          <Ionicons name="checkmark" size={16} color="#FFFFFF" />
        )}
      </View>

      {/* Avatar */}
      <View style={{ marginRight: 12 }}>
        {item.avatar ? (
          <Image
            source={{ uri: item.avatar }}
            style={{ width: 48, height: 48, borderRadius: 24 }}
          />
        ) : (
          <View style={{
            width: 48,
            height: 48,
            borderRadius: 24,
            backgroundColor: '#87CEEB',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Text style={{ color: '#FFFFFF', fontSize: 18, fontWeight: 'bold' }}>
              {item.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
      </View>

      {/* Chat Info */}
      <View style={{ flex: 1 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
          <Text style={{ fontSize: 16, fontWeight: '600', color: '#333', flex: 1 }}>
            {item.name}
          </Text>
          {item.isPinned && (
            <Ionicons name="pin" size={16} color="#87CEEB" style={{ marginLeft: 8 }} />
          )}
          {item.isMuted && (
            <Ionicons name="volume-mute" size={16} color="#999" style={{ marginLeft: 4 }} />
          )}
        </View>
        
        {item.lastMessage && (
          <Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }} numberOfLines={1}>
            {item.lastMessage}
          </Text>
        )}
        
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text style={{ fontSize: 12, color: '#999' }}>
            {item.timestamp}
          </Text>
          {item.unreadCount && item.unreadCount > 0 && (
            <View style={{
              backgroundColor: '#87CEEB',
              borderRadius: 10,
              minWidth: 20,
              height: 20,
              justifyContent: 'center',
              alignItems: 'center',
              paddingHorizontal: 6,
            }}>
              <Text style={{ color: '#FFFFFF', fontSize: 12, fontWeight: 'bold' }}>
                {item.unreadCount > 99 ? '99+' : item.unreadCount}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#F8F9FA' }}>
        <ActivityIndicator size="large" color="#87CEEB" />
        <Text style={{ marginTop: 16, fontSize: 16, color: '#666' }}>Loading chats...</Text>
      </View>
    );
  }

  const filteredChats = getFilteredAndSortedChats();

  return (
    <View style={{ flex: 1, backgroundColor: '#F8F9FA' }}>
      <StatusBar style="light" />
      
      {/* Header */}
      <LinearGradient
        colors={['#87CEEB', '#4682B4']}
        style={{
          paddingTop: 50,
          paddingBottom: 20,
          paddingHorizontal: 20,
        }}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
          <TouchableOpacity
            onPress={() => navigationService.goBack()}
            style={{ marginRight: 16 }}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#FFFFFF', flex: 1 }}>
            Chat Management
          </Text>
          <TouchableOpacity
            onPress={() => setShowFilterModal(true)}
            style={{ marginLeft: 16 }}
          >
            <Ionicons name="filter" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
          borderRadius: 25,
          paddingHorizontal: 16,
          paddingVertical: 12,
        }}>
          <Ionicons name="search" size={20} color="#FFFFFF" />
          <TextInput
            style={{
              flex: 1,
              marginLeft: 12,
              fontSize: 16,
              color: '#FFFFFF',
            }}
            placeholder="Search chats..."
            placeholderTextColor="rgba(255, 255, 255, 0.7)"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </View>
      </LinearGradient>

      {/* Bulk Actions Bar */}
      {showBulkActions && (
        <View style={{
          backgroundColor: '#FFFFFF',
          paddingHorizontal: 20,
          paddingVertical: 12,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottomWidth: 1,
          borderBottomColor: '#E0E0E0',
        }}>
          <Text style={{ fontSize: 16, fontWeight: '600', color: '#333' }}>
            {selectedChats.size} selected
          </Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TouchableOpacity
              onPress={selectAllChats}
              style={{ marginRight: 16 }}
            >
              <Text style={{ color: '#87CEEB', fontSize: 14, fontWeight: '600' }}>
                Select All
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={clearSelection}
              style={{ marginRight: 16 }}
            >
              <Text style={{ color: '#666', fontSize: 14 }}>
                Clear
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleBulkArchive}
              style={{ marginRight: 16 }}
            >
              <Ionicons name="archive" size={20} color="#87CEEB" />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleBulkMute}
              style={{ marginRight: 16 }}
            >
              <Ionicons name="volume-mute" size={20} color="#87CEEB" />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleBulkDelete}>
              <Ionicons name="trash" size={20} color="#FF6B6B" />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Chat List */}
      <View style={{ flex: 1, paddingHorizontal: 20, paddingTop: 20 }}>
        {filteredChats.length === 0 ? (
          <View style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            paddingHorizontal: 40,
          }}>
            <Ionicons name="chatbubbles-outline" size={64} color="#CCC" />
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#666',
              marginTop: 16,
              textAlign: 'center',
            }}>
              No chats found
            </Text>
            <Text style={{
              fontSize: 14,
              color: '#999',
              marginTop: 8,
              textAlign: 'center',
            }}>
              Try adjusting your search or criteria
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredChats}
            renderItem={renderChatItem}
            keyExtractor={(item) => item.id}
            refreshing={refreshing}
            onRefresh={handleRefresh}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        )}
      </View>

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'flex-end',
        }}>
          <View style={{
            backgroundColor: '#FFFFFF',
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            paddingHorizontal: 20,
            paddingTop: 20,
            paddingBottom: 40,
            maxHeight: screenHeight * 0.7,
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: 24,
            }}>
              <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#333' }}>
                Filter & Sort
              </Text>
              <TouchableOpacity onPress={() => setShowFilterModal(false)}>
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            {/* Filter Type */}
            <View style={{ marginBottom: 24 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#333', marginBottom: 12 }}>
                Chat Type
              </Text>
              {[
                { key: 'all', label: 'All Chats', icon: 'chatbubbles' },
                { key: 'individual', label: 'Individual', icon: 'person' },
                { key: 'group', label: 'Groups', icon: 'people' },
                { key: 'archived', label: 'Archived', icon: 'archive' },
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  onPress={() => setFilterType(option.key as any)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    borderRadius: 8,
                    backgroundColor: filterType === option.key ? '#F0F8FF' : 'transparent',
                    marginBottom: 8,
                  }}
                >
                  <Ionicons
                    name={option.icon as any}
                    size={20}
                    color={filterType === option.key ? '#87CEEB' : '#666'}
                  />
                  <Text style={{
                    marginLeft: 12,
                    fontSize: 16,
                    color: filterType === option.key ? '#87CEEB' : '#333',
                    fontWeight: filterType === option.key ? '600' : 'normal',
                  }}>
                    {option.label}
                  </Text>
                  {filterType === option.key && (
                    <Ionicons name="checkmark" size={20} color="#87CEEB" style={{ marginLeft: 'auto' }} />
                  )}
                </TouchableOpacity>
              ))}
            </View>

            {/* Sort By */}
            <View style={{ marginBottom: 24 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#333', marginBottom: 12 }}>
                Sort By
              </Text>
              {[
                { key: 'recent', label: 'Most Recent', icon: 'time' },
                { key: 'name', label: 'Name (A-Z)', icon: 'text' },
                { key: 'unread', label: 'Unread Count', icon: 'notifications' },
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  onPress={() => setSortBy(option.key as any)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    borderRadius: 8,
                    backgroundColor: sortBy === option.key ? '#F0F8FF' : 'transparent',
                    marginBottom: 8,
                  }}
                >
                  <Ionicons
                    name={option.icon as any}
                    size={20}
                    color={sortBy === option.key ? '#87CEEB' : '#666'}
                  />
                  <Text style={{
                    marginLeft: 12,
                    fontSize: 16,
                    color: sortBy === option.key ? '#87CEEB' : '#333',
                    fontWeight: sortBy === option.key ? '600' : 'normal',
                  }}>
                    {option.label}
                  </Text>
                  {sortBy === option.key && (
                    <Ionicons name="checkmark" size={20} color="#87CEEB" style={{ marginLeft: 'auto' }} />
                  )}
                </TouchableOpacity>
              ))}
            </View>

            {/* Apply Button */}
            <TouchableOpacity
              onPress={() => setShowFilterModal(false)}
              style={{
                backgroundColor: '#87CEEB',
                borderRadius: 12,
                paddingVertical: 16,
                alignItems: 'center',
              }}
            >
              <Text style={{
                color: '#FFFFFF',
                fontSize: 16,
                fontWeight: '600',
              }}>
                Apply Filters
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

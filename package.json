{"name": "irachat", "version": "1.0.2", "description": "A fully functional React Native messaging app built with Expo, Firebase, and NativeWind", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "build": "eas build --platform all", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:preview": "eas build --platform all --profile preview", "build:production": "eas build --platform all --profile production", "submit": "eas submit", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "dev": "expo start --dev-client", "dev:android": "expo start --dev-client --android", "dev:ios": "expo start --dev-client --ios", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:mobile": "node mobile-only-test.js", "test:responsive": "node mobile-responsiveness-test.js", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean", "prebuild:android": "expo prebuild --platform android", "prebuild:ios": "expo prebuild --platform ios", "setup-android": "echo 'Setting up Android development environment...' && echo 'Please install Android Studio and set ANDROID_HOME'", "setup-ios": "echo 'Setting up iOS development environment...' && echo 'Please install Xcode from App Store'", "clean": "npx expo start --clear", "reset": "rm -rf node_modules && npm install && npx expo start --clear", "mobile-cleanup": "node mobile-cleanup-organizer.js"}, "dependencies": {"@config-plugins/react-native-webrtc": "^12.0.0", "@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.3.1", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.2.7", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "expo": "53.0.16", "expo-asset": "~11.1.5", "expo-audio": "^0.4.6", "expo-av": "~15.1.6", "expo-blur": "~14.1.5", "expo-build-properties": "~0.12.5", "expo-camera": "~16.1.8", "expo-constants": "~17.1.6", "expo-contacts": "~14.2.5", "expo-dev-client": "^5.2.0", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.5", "expo-font": "~13.3.1", "expo-haptics": "^14.1.4", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-location": "^18.1.5", "expo-media-library": "~17.1.7", "expo-router": "~5.1.0", "expo-secure-store": "~14.2.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-task-manager": "^13.1.6", "expo-video": "~2.2.1", "expo-video-thumbnails": "~9.1.3", "firebase": "^11.0.2", "nativewind": "^2.0.11", "react": "19.0.0", "react-native": "0.79.4", "react-native-callkeep": "^4.3.12", "react-native-gesture-handler": "~2.24.0", "react-native-incall-manager": "^4.0.1", "react-native-keep-awake": "^4.0.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "^5.5.0", "react-native-screens": "~4.11.1", "react-native-sound": "^0.11.2", "react-native-svg": "^15.12.0", "react-native-webrtc": "^118.0.0", "react-redux": "^9.1.2", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "metro": "^0.82.4", "typescript": "~5.8.3"}, "keywords": ["react-native", "expo", "firebase", "messaging", "chat", "mobile-app", "android", "ios", "nativewind", "typescript", "mobile-only"], "author": "IraChat Team", "license": "MIT", "private": true, "expo": {"install": {"exclude": ["react-native-safe-area-context"]}, "doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-webrtc", "redux-persist", "ajv", "ajv-keywords", "firebase", "metro"], "listUnknownPackages": false}}}}
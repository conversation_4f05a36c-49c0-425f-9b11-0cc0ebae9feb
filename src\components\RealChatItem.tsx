// 🔥 REAL CHAT ITEM COMPONENT - COMPLETE FIREBASE IMPLEMENTATION
// No mockups, no fake data - 100% real Firebase functionality

import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { doc, updateDoc, serverTimestamp } from "firebase/firestore";
import { navigationService } from "../services/navigationService";
import React, { useState } from "react";
import {
  Image,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { db } from "../services/firebaseSimple";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";

interface RealChatItemProps {
  id: string;
  name: string;
  avatar?: string;
  lastMessage: string;
  lastMessageTime: Date;
  unreadCount: number;
  isGroup: boolean;
  participants: string[];
  participantNames: { [userId: string]: string };
  participantAvatars: { [userId: string]: string };
  isOnline?: boolean;
  lastSeen?: Date;
  isTyping?: boolean;
  isPinned?: boolean;
  isMuted?: boolean;
  messageCount: number;
  mediaCount: number;
  lastMessageSender?: string;
  lastMessageType?: 'text' | 'image' | 'video' | 'audio' | 'document';
  isSelected?: boolean;
  onSelect?: (_id: string) => void;
  onLongPress?: (_id: string) => void;
  selectionMode?: boolean;
}

export default function RealChatItem({
  id,
  name,
  avatar,
  lastMessage,
  lastMessageTime,
  unreadCount,
  isGroup,
  participants,
  participantNames,
  participantAvatars,
  isOnline,
  lastSeen: _lastSeen,
  isTyping,
  isPinned,
  isMuted,
  messageCount: _messageCount,
  mediaCount: _mediaCount,
  lastMessageSender,
  lastMessageType,
  isSelected = false,
  onSelect,
  onLongPress,
  selectionMode = false,
}: RealChatItemProps) {
  const _router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const [isPressed, setIsPressed] = useState(false);

  // Format time for display
  const formatTime = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'now';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    if (days < 7) return `${days}d`;
    return date.toLocaleDateString();
  };

  // Get message type icon
  const getMessageTypeIcon = (type?: string) => {
    switch (type) {
      case 'image': return 'image';
      case 'video': return 'videocam';
      case 'audio': return 'musical-notes';
      case 'document': return 'document';
      default: return null;
    }
  };

  // Mark chat as read
  const markAsRead = async () => {
    if (!currentUser?.id || unreadCount === 0) return;

    try {
      await updateDoc(doc(db, 'individual_chats', id), {
        [`unreadCount.${currentUser.id}`]: 0,
        updatedAt: serverTimestamp(),
      });
      console.log("✅ Chat marked as read:", id);
    } catch (error) {
      console.error("❌ Error marking chat as read:", error);
    }
  };

  // Toggle pin status
  const togglePin = async () => {
    if (!currentUser?.id) return;

    try {
      const chatRef = doc(db, 'individual_chats', id);
      
      if (isPinned) {
        // Unpin chat
        await updateDoc(chatRef, {
          pinnedBy: participants.filter(p => p !== currentUser.id),
          updatedAt: serverTimestamp(),
        });
      } else {
        // Pin chat
        await updateDoc(chatRef, {
          pinnedBy: [...(participants.filter(p => p !== currentUser.id)), currentUser.id],
          updatedAt: serverTimestamp(),
        });
      }
      
      console.log(`✅ Chat ${isPinned ? 'unpinned' : 'pinned'}:`, id);
    } catch (error) {
      console.error("❌ Error toggling pin status:", error);
    }
  };

  // Toggle mute status
  const toggleMute = async () => {
    if (!currentUser?.id) return;

    try {
      const chatRef = doc(db, 'individual_chats', id);
      
      if (isMuted) {
        // Unmute chat
        await updateDoc(chatRef, {
          mutedBy: participants.filter(p => p !== currentUser.id),
          updatedAt: serverTimestamp(),
        });
      } else {
        // Mute chat
        await updateDoc(chatRef, {
          mutedBy: [...(participants.filter(p => p !== currentUser.id)), currentUser.id],
          updatedAt: serverTimestamp(),
        });
      }
      
      console.log(`✅ Chat ${isMuted ? 'unmuted' : 'muted'}:`, id);
    } catch (error) {
      console.error("❌ Error toggling mute status:", error);
    }
  };

  // Handle chat press
  const handlePress = () => {
    if (selectionMode && onSelect) {
      onSelect(id);
    } else {
      // Mark as read and navigate to chat
      markAsRead();
      navigationService.openChat(id, isGroup);
    }
  };

  // Handle long press
  const handleLongPress = () => {
    if (onLongPress) {
      onLongPress(id);
    }
  };

  // Get last message preview
  const getLastMessagePreview = (): string => {
    if (isTyping) return 'typing...';
    
    if (lastMessageType && lastMessageType !== 'text') {
      switch (lastMessageType) {
        case 'image': return '📷 Photo';
        case 'video': return '🎥 Video';
        case 'audio': return '🎵 Audio';
        case 'document': return '📄 Document';
        default: return lastMessage;
      }
    }
    
    return lastMessage || 'No messages yet';
  };

  // Get sender name for group chats
  const getSenderName = (): string => {
    if (!isGroup || !lastMessageSender) return '';
    
    if (lastMessageSender === currentUser?.id) {
      return 'You: ';
    }
    
    const senderName = participantNames[lastMessageSender];
    return senderName ? `${senderName}: ` : 'Someone: ';
  };

  return (
    <TouchableOpacity
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: isSelected ? '#87CEEB20' : isPressed ? '#f9fafb' : 'white',
        borderLeftWidth: isSelected ? 4 : 0,
        borderLeftColor: '#87CEEB',
      }}
      onPress={handlePress}
      onLongPress={handleLongPress}
      onPressIn={() => setIsPressed(true)}
      onPressOut={() => setIsPressed(false)}
      activeOpacity={0.7}
    >
      {/* Selection checkbox */}
      {selectionMode && (
        <View style={{ marginRight: 16 }}>
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 12,
              borderWidth: 2,
              borderColor: isSelected ? '#87CEEB' : '#d1d5db',
              backgroundColor: isSelected ? '#87CEEB' : 'transparent',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {isSelected && (
              <Ionicons name="checkmark" size={16} color="white" />
            )}
          </View>
        </View>
      )}

      {/* Avatar */}
      <View style={{ position: 'relative' }}>
        {avatar ? (
          <Image
            source={{ uri: avatar }}
            style={{
              width: 56,
              height: 56,
              borderRadius: 28,
              backgroundColor: '#f0f0f0',
            }}
          />
        ) : (
          <View
            style={{
              width: 56,
              height: 56,
              borderRadius: 28,
              backgroundColor: '#87CEEB',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Text style={{ color: 'white', fontSize: 20, fontWeight: 'bold' }}>
              {name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
        
        {/* Online indicator */}
        {isOnline && !isGroup && (
          <View
            style={{
              position: 'absolute',
              bottom: 2,
              right: 2,
              width: 16,
              height: 16,
              borderRadius: 8,
              backgroundColor: '#10B981',
              borderWidth: 2,
              borderColor: 'white',
            }}
          />
        )}
        
        {/* Group indicator */}
        {isGroup && (
          <View
            style={{
              position: 'absolute',
              bottom: 2,
              right: 2,
              width: 16,
              height: 16,
              borderRadius: 8,
              backgroundColor: '#667eea',
              alignItems: 'center',
              justifyContent: 'center',
              borderWidth: 2,
              borderColor: 'white',
            }}
          >
            <Ionicons name="people" size={8} color="white" />
          </View>
        )}
      </View>

      {/* Chat info */}
      <View style={{ flex: 1, marginLeft: 16 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
          <Text
            style={{
              fontSize: 16,
              fontWeight: unreadCount > 0 ? '700' : '600',
              color: '#1f2937',
              flex: 1,
            }}
            numberOfLines={1}
          >
            {name}
          </Text>
          
          {/* Pinned indicator */}
          {isPinned && (
            <TouchableOpacity onPress={togglePin} style={{ marginRight: 8 }}>
              <Ionicons name="pin" size={14} color="#87CEEB" />
            </TouchableOpacity>
          )}
          
          {/* Muted indicator */}
          {isMuted && (
            <TouchableOpacity onPress={toggleMute} style={{ marginRight: 8 }}>
              <Ionicons name="volume-mute" size={14} color="#9ca3af" />
            </TouchableOpacity>
          )}
          
          <Text style={{ fontSize: 12, color: '#9ca3af' }}>
            {formatTime(lastMessageTime)}
          </Text>
        </View>
        
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {/* Message type icon */}
          {getMessageTypeIcon(lastMessageType) && (
            <Ionicons
              name={getMessageTypeIcon(lastMessageType) as any}
              size={14}
              color="#9ca3af"
              style={{ marginRight: 4 }}
            />
          )}
          
          {/* Message preview */}
          {isTyping ? (
            <Text style={{ fontSize: 14, color: '#87CEEB', fontStyle: 'italic', flex: 1 }}>
              typing...
            </Text>
          ) : (
            <Text
              style={{
                fontSize: 14,
                color: unreadCount > 0 ? '#374151' : '#6b7280',
                fontWeight: unreadCount > 0 ? '600' : 'normal',
                flex: 1,
              }}
              numberOfLines={1}
            >
              {getSenderName()}{getLastMessagePreview()}
            </Text>
          )}
          
          {/* Unread count */}
          {unreadCount > 0 && (
            <View
              style={{
                backgroundColor: '#87CEEB',
                borderRadius: 12,
                minWidth: 24,
                height: 24,
                alignItems: 'center',
                justifyContent: 'center',
                paddingHorizontal: 8,
              }}
            >
              <Text style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}>
                {unreadCount > 99 ? '99+' : unreadCount}
              </Text>
            </View>
          )}
        </View>
        
        {/* Additional info for groups */}
        {isGroup && participants.length > 2 && (
          <Text style={{ fontSize: 12, color: '#9ca3af', marginTop: 2 }}>
            {participants.length} members
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
}

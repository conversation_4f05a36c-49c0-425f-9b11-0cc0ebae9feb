// 🚀 CHAT HEADER COMPONENT
// Header with user info, online status, and action buttons

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface ChatUser {
  id: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: Date;
}

interface ChatHeaderProps {
  user: ChatUser;
  onBack: () => void;
  onUserPress: () => void;
  onVoiceCall: () => void;
  onVideoCall: () => void;
  onMoreOptions: () => void;
  isSelectionMode: boolean;
  selectedCount: number;
  onCancelSelection: () => void;
  onDeleteSelected: () => void;
  onForwardSelected: () => void;
}

const COLORS = {
  primary: '#87CEEB',
  text: '#333',
  textMuted: '#666',
  white: '#ffffff',
  background: '#f8f9fa',
};

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  user,
  onBack,
  onUserPress,
  onVoiceCall,
  onVideoCall,
  onMoreOptions,
  isSelectionMode,
  selectedCount,
  onCancelSelection,
  onDeleteSelected,
  onForwardSelected,
}) => {
  const insets = useSafeAreaInsets();

  const formatLastSeen = (lastSeen?: Date) => {
    if (!lastSeen) return 'Last seen recently';
    
    const now = new Date();
    const diff = now.getTime() - lastSeen.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Last seen just now';
    if (minutes < 60) return `Last seen ${minutes}m ago`;
    if (hours < 24) return `Last seen ${hours}h ago`;
    if (days < 7) return `Last seen ${days}d ago`;
    return 'Last seen recently';
  };

  if (isSelectionMode) {
    return (
      <LinearGradient
        colors={['#87CEEB', '#5F9EA0']}
        style={[styles.header, { paddingTop: insets.top }]}
      >
        <StatusBar barStyle="light-content" backgroundColor="#5F9EA0" />
        <View style={styles.selectionHeader}>
          <TouchableOpacity style={styles.backButton} onPress={onCancelSelection}>
            <Ionicons name="close" size={24} color={COLORS.white} />
          </TouchableOpacity>
          
          <Text style={styles.selectionTitle}>
            {selectedCount} selected
          </Text>
          
          <View style={styles.selectionActions}>
            <TouchableOpacity style={styles.actionButton} onPress={onForwardSelected}>
              <Ionicons name="arrow-forward" size={20} color={COLORS.white} />
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton} onPress={onDeleteSelected}>
              <Ionicons name="trash" size={20} color={COLORS.white} />
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['#87CEEB', '#5F9EA0']}
      style={[styles.header, { paddingTop: insets.top }]}
    >
      <StatusBar barStyle="light-content" backgroundColor="#5F9EA0" />
      <View style={styles.headerContent}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={24} color={COLORS.white} />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.userInfo} onPress={onUserPress}>
          <View style={styles.avatarContainer}>
            {user.avatar ? (
              <Image source={{ uri: user.avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.defaultAvatar}>
                <Ionicons name="person" size={20} color={COLORS.primary} />
              </View>
            )}
            {user.isOnline && <View style={styles.onlineIndicator} />}
          </View>
          
          <View style={styles.userDetails}>
            <Text style={styles.userName} numberOfLines={1}>
              {user.name}
            </Text>
            <Text style={styles.userStatus} numberOfLines={1}>
              {user.isOnline ? 'Online' : formatLastSeen(user.lastSeen)}
            </Text>
          </View>
        </TouchableOpacity>
        
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.actionButton} onPress={onVoiceCall}>
            <Ionicons name="call" size={20} color={COLORS.white} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={onVideoCall}>
            <Ionicons name="videocam" size={20} color={COLORS.white} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={onMoreOptions}>
            <Ionicons name="ellipsis-vertical" size={20} color={COLORS.white} />
          </TouchableOpacity>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingBottom: 12,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  selectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  userInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  defaultAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: COLORS.white,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  userStatus: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  selectionTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.white,
    textAlign: 'center',
  },
  selectionActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

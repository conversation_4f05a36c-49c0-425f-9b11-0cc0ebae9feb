// 📋 COMPREHENSIVE GROUP INFO PAGE
// Complete group information, settings, members, and management
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Alert,
  SafeAreaView,
  Image,
  RefreshControl,
  TextInput,
  Modal,
  ScrollView,
  Animated,
  Share,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Group, GroupMember, GroupInvite } from '../types/Group';
import { MostActiveMemberSystem } from './MostActiveMemberSystem';
import { realGroupService } from '../services/realGroupService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  inputBackground: 'rgba(255, 255, 255, 0.1)',
};

interface ComprehensiveGroupInfoPageProps {
  visible: boolean;
  group: Group;
  currentUserId: string;
  onClose: () => void;
  onEditGroup?: () => void;
  onLeaveGroup?: () => void;
  onDeleteGroup?: () => void;
  onManageMembers?: () => void;
  onGroupSettings?: () => void;
  onUserPress?: (userId: string) => void;
}

export const ComprehensiveGroupInfoPage: React.FC<ComprehensiveGroupInfoPageProps> = ({
  visible,
  group,
  currentUserId,
  onClose,
  onEditGroup,
  onLeaveGroup,
  onDeleteGroup,
  onManageMembers,
  onGroupSettings,
  onUserPress,
}) => {
  const insets = useSafeAreaInsets();
  
  // ==================== STATE MANAGEMENT ====================
  
  const [activeTab, setActiveTab] = useState<'info' | 'members' | 'media' | 'activity'>('info');
  const [showMostActive, setShowMostActive] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [groupMembers, setGroupMembers] = useState<GroupMember[]>([]);

  // New state for media and activity
  const [groupMedia, setGroupMedia] = useState<any[]>([]);
  const [groupStats, setGroupStats] = useState<any>({});
  const [recentActivity, setRecentActivity] = useState<any[]>([]);

  // Animation refs
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (visible) {
      loadGroupData();
      showModal();
    } else {
      hideModal();
    }
  }, [visible]);

  // ==================== ANIMATION METHODS ====================

  const showModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // ==================== DATA METHODS ====================

  const loadGroupData = async () => {
    try {
      console.log('📋 Loading group data for:', group.id);

      // Load real group data from Firebase
      const realGroupData = await realGroupService.getGroupById(group.id);

      if (realGroupData && realGroupData.members) {
        // Convert real group members to GroupMember format
        const realMembers: GroupMember[] = realGroupData.members.map((memberId: string) => {
          const role = realGroupData.memberRoles[memberId] || 'member';

          return {
            id: memberId,
            userId: memberId,
            userName: realGroupData.memberNames[memberId] || 'Unknown User',
            userAvatar: realGroupData.memberAvatars[memberId] || undefined,
            role: role as 'owner' | 'admin' | 'member',
            joinedAt: realGroupData.memberJoinedAt[memberId] || new Date(),
            isOnline: false, // Can be enhanced with online status service
            lastSeen: undefined, // Can be enhanced with last seen service
            permissions: role === 'owner' || role === 'admin'
              ? ['send_messages', 'add_members', 'remove_members', 'edit_info', 'pin_messages', 'delete_messages']
              : ['send_messages'],
            customTitle: role === 'owner' ? 'Owner' : role === 'admin' ? 'Admin' : undefined,
            invitedBy: undefined, // Can be enhanced if needed
          };
        });

        console.log('✅ Group members loaded:', realMembers.length);
        setGroupMembers(realMembers);
      } else {
        console.log('❌ No group data found');
        setGroupMembers([]);
      }

      // Load additional data
      await Promise.all([
        loadGroupMedia(),
        loadGroupStats(),
        loadRecentActivity(),
      ]);
    } catch (error) {
      console.error('❌ Error loading group data:', error);
    }
  };

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadGroupData();
    setIsRefreshing(false);
  }, []);

  const handleShareGroup = async () => {
    try {
      const shareContent = {
        message: `Join "${group.name}" on IraChat!`,
        url: group.primaryInviteLink || `https://irachat.app/group/${group.id}`,
      };
      
      await Share.share(shareContent);
    } catch (error) {
      console.error('❌ Error sharing group:', error);
    }
  };

  const handleInviteMembers = () => {
    setShowInviteModal(true);
  };

  // New functions for media and activity
  const handleMediaPress = (mediaItem: any) => {
    // Open media viewer
    console.log('Opening media:', mediaItem);
  };

  const loadGroupMedia = async () => {
    try {
      const { collection, query, where, orderBy, getDocs } = await import('firebase/firestore');
      const { db } = await import('../config/firebase');

      const mediaQuery = query(
        collection(db, 'group_messages'),
        where('groupId', '==', group.id),
        where('type', 'in', ['image', 'video', 'file']),
        orderBy('timestamp', 'desc')
      );

      const snapshot = await getDocs(mediaQuery);
      const media = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));

      setGroupMedia(media);
    } catch (error) {
      console.error('❌ Error loading group media:', error);
    }
  };

  const loadGroupStats = async () => {
    try {
      const { collection, query, where, getDocs } = await import('firebase/firestore');
      const { db } = await import('../config/firebase');

      // Get total messages count
      const messagesQuery = query(
        collection(db, 'group_messages'),
        where('groupId', '==', group.id)
      );
      const messagesSnapshot = await getDocs(messagesQuery);

      // Get media count
      const mediaQuery = query(
        collection(db, 'group_messages'),
        where('groupId', '==', group.id),
        where('type', 'in', ['image', 'video', 'file'])
      );
      const mediaSnapshot = await getDocs(mediaQuery);

      const stats = {
        totalMessages: messagesSnapshot.size,
        mediaCount: mediaSnapshot.size,
        activeMembers: groupMembers.filter(m => m.isOnline).length,
      };

      setGroupStats(stats);
    } catch (error) {
      console.error('❌ Error loading group stats:', error);
    }
  };

  const loadRecentActivity = async () => {
    try {
      const { collection, query, where, orderBy, limit, getDocs } = await import('firebase/firestore');
      const { db } = await import('../config/firebase');

      const activityQuery = query(
        collection(db, 'group_activity'),
        where('groupId', '==', group.id),
        orderBy('timestamp', 'desc'),
        limit(10)
      );

      const snapshot = await getDocs(activityQuery);
      const activities = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));

      setRecentActivity(activities);
    } catch (error) {
      console.error('❌ Error loading recent activity:', error);
    }
  };

  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString();
  };

  const isCurrentUserAdmin = () => {
    return group.currentUserRole === 'owner' || group.currentUserRole === 'admin';
  };

  const getTimeAgo = (date: Date) => {
    const now = Date.now();
    const diff = now - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const months = Math.floor(days / 30);
    
    if (months > 0) return `${months} month${months > 1 ? 's' : ''} ago`;
    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    return 'Today';
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner': return COLORS.warning;
      case 'admin': return COLORS.primary;
      default: return COLORS.textSecondary;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return 'crown';
      case 'admin': return 'shield';
      default: return 'person';
    }
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="chevron-down" size={28} color={COLORS.text} />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Group Info</Text>

          <TouchableOpacity 
            style={styles.headerButton}
            onPress={handleShareGroup}
          >
            <Ionicons name="share-outline" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );

  const renderGroupHeader = () => (
    <View style={styles.groupHeader}>
      <LinearGradient
        colors={[COLORS.surface, COLORS.surfaceLight]}
        style={styles.groupHeaderGradient}
      >
        {/* Cover Image */}
        {group.coverImage && (
          <Image source={{ uri: group.coverImage }} style={styles.coverImage} />
        )}
        
        {/* Group Avatar & Info */}
        <View style={styles.groupAvatarSection}>
          <View style={styles.groupAvatarContainer}>
            <Image 
              source={{ uri: group.avatar || 'https://via.placeholder.com/80' }} 
              style={styles.groupAvatar} 
            />
            {group.isVerified && (
              <View style={styles.verifiedBadge}>
                <Ionicons name="checkmark" size={16} color={COLORS.text} />
              </View>
            )}
          </View>
          
          <View style={styles.groupInfo}>
            <Text style={styles.groupName}>{group.name}</Text>
            {group.description && (
              <Text style={styles.groupDescription}>{group.description}</Text>
            )}
            
            <View style={styles.groupStats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{group.memberCount}</Text>
                <Text style={styles.statLabel}>Members</Text>
              </View>
              <View style={styles.statSeparator} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{group.onlineCount}</Text>
                <Text style={styles.statLabel}>Online</Text>
              </View>
              <View style={styles.statSeparator} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{group.stats.totalMessages}</Text>
                <Text style={styles.statLabel}>Messages</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {isCurrentUserAdmin() && (
            <TouchableOpacity style={styles.actionButton} onPress={onEditGroup}>
              <Ionicons name="create-outline" size={20} color={COLORS.primary} />
              <Text style={styles.actionButtonText}>Edit</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity style={styles.actionButton} onPress={handleInviteMembers}>
            <Ionicons name="person-add-outline" size={20} color={COLORS.primary} />
            <Text style={styles.actionButtonText}>Invite</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={() => setShowMostActive(true)}>
            <Ionicons name="trophy-outline" size={20} color={COLORS.primary} />
            <Text style={styles.actionButtonText}>Activity</Text>
          </TouchableOpacity>
          
          {isCurrentUserAdmin() && (
            <TouchableOpacity style={styles.actionButton} onPress={onGroupSettings}>
              <Ionicons name="settings-outline" size={20} color={COLORS.primary} />
              <Text style={styles.actionButtonText}>Settings</Text>
            </TouchableOpacity>
          )}
        </View>
      </LinearGradient>
    </View>
  );

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {(['info', 'members', 'media', 'activity'] as const).map((tab) => (
        <TouchableOpacity
          key={tab}
          style={[styles.tabItem, activeTab === tab && styles.activeTabItem]}
          onPress={() => setActiveTab(tab)}
        >
          <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderInfoTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Group Details */}
      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Group Details</Text>
        
        <View style={styles.infoItem}>
          <Ionicons name="calendar-outline" size={20} color={COLORS.primary} />
          <View style={styles.infoText}>
            <Text style={styles.infoLabel}>Created</Text>
            <Text style={styles.infoValue}>
              {group.createdAt.toLocaleDateString()} by {group.ownerName}
            </Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Ionicons name="globe-outline" size={20} color={COLORS.primary} />
          <View style={styles.infoText}>
            <Text style={styles.infoLabel}>Type</Text>
            <Text style={styles.infoValue}>
              {group.type.charAt(0).toUpperCase() + group.type.slice(1)} Group
            </Text>
          </View>
        </View>
        
        {group.category && (
          <View style={styles.infoItem}>
            <Ionicons name="pricetag-outline" size={20} color={COLORS.primary} />
            <View style={styles.infoText}>
              <Text style={styles.infoLabel}>Category</Text>
              <Text style={styles.infoValue}>{group.category}</Text>
            </View>
          </View>
        )}
        
        {group.tags.length > 0 && (
          <View style={styles.infoItem}>
            <Ionicons name="bookmark-outline" size={20} color={COLORS.primary} />
            <View style={styles.infoText}>
              <Text style={styles.infoLabel}>Tags</Text>
              <View style={styles.tagsContainer}>
                {group.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>#{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        )}
      </View>

      {/* Group Settings Preview */}
      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Settings</Text>
        
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Message History</Text>
          <Text style={styles.settingValue}>
            {group.settings.readReceipts ? 'Visible' : 'Hidden'}
          </Text>
        </View>
        
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Add Members</Text>
          <Text style={styles.settingValue}>
            {group.settings.onlyAdminsCanAddMembers ? 'Admins Only' : 'Everyone'}
          </Text>
        </View>
        
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Send Messages</Text>
          <Text style={styles.settingValue}>
            {group.settings.onlyAdminsCanSendMessages ? 'Admins Only' : 'Everyone'}
          </Text>
        </View>
      </View>

      {/* Danger Zone */}
      <View style={styles.dangerSection}>
        <Text style={styles.sectionTitle}>Actions</Text>
        
        {group.currentUserRole === 'owner' ? (
          <TouchableOpacity style={styles.dangerButton} onPress={onDeleteGroup}>
            <Ionicons name="trash-outline" size={20} color={COLORS.error} />
            <Text style={styles.dangerButtonText}>Delete Group</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.dangerButton} onPress={onLeaveGroup}>
            <Ionicons name="exit-outline" size={20} color={COLORS.error} />
            <Text style={styles.dangerButtonText}>Leave Group</Text>
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );

  const renderMemberItem = ({ item: member }: { item: GroupMember }) => (
    <TouchableOpacity 
      style={styles.memberItem}
      onPress={() => onUserPress?.(member.userId)}
      activeOpacity={0.7}
    >
      <View style={styles.memberLeft}>
        <View style={styles.memberAvatarContainer}>
          <Image 
            source={{ uri: member.userAvatar || 'https://via.placeholder.com/40' }} 
            style={styles.memberAvatar} 
          />
          {member.isOnline && (
            <View style={styles.onlineIndicator} />
          )}
        </View>

        <View style={styles.memberInfo}>
          <View style={styles.memberHeader}>
            <Text style={styles.memberName}>{member.userName}</Text>
            {member.customTitle && (
              <Text style={styles.customTitle}>{member.customTitle}</Text>
            )}
          </View>
          
          <View style={styles.memberMeta}>
            <View style={styles.roleContainer}>
              <Ionicons 
                name={getRoleIcon(member.role) as any} 
                size={12} 
                color={getRoleColor(member.role)} 
              />
              <Text style={[styles.roleText, { color: getRoleColor(member.role) }]}>
                {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
              </Text>
            </View>
            <Text style={styles.joinedText}>
              Joined {getTimeAgo(member.joinedAt)}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.memberRight}>
        {member.userId === currentUserId && (
          <Text style={styles.youLabel}>You</Text>
        )}
        <TouchableOpacity style={styles.memberActionButton}>
          <Ionicons name="chevron-forward" size={16} color={COLORS.textMuted} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderMembersTab = () => (
    <View style={styles.tabContent}>
      <FlatList
        data={groupMembers}
        renderItem={renderMemberItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={COLORS.primary}
            colors={[COLORS.primary]}
          />
        }
        ListHeaderComponent={() => (
          <View style={styles.membersHeader}>
            <Text style={styles.membersCount}>
              {group.memberCount} member{group.memberCount !== 1 ? 's' : ''}
            </Text>
            {isCurrentUserAdmin() && (
              <TouchableOpacity style={styles.manageButton} onPress={onManageMembers}>
                <Text style={styles.manageButtonText}>Manage</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      />
    </View>
  );

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="none" statusBarTranslucent>
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        <Animated.View 
          style={[
            styles.modalContent,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {renderHeader()}
          {renderGroupHeader()}
          {renderTabBar()}
          
          {activeTab === 'info' && renderInfoTab()}
          {activeTab === 'members' && renderMembersTab()}
          {activeTab === 'media' && (
            <View style={styles.tabContent}>
              <FlatList
                data={groupMedia}
                keyExtractor={(item) => item.id}
                numColumns={3}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={styles.mediaItem}
                    onPress={() => handleMediaPress(item)}
                  >
                    <Image source={{ uri: item.thumbnail || item.url }} style={styles.mediaThumbnail} />
                    {item.type === 'video' && (
                      <View style={styles.videoOverlay}>
                        <Ionicons name="play" size={20} color="white" />
                      </View>
                    )}
                  </TouchableOpacity>
                )}
                contentContainerStyle={styles.mediaGrid}
                ListEmptyComponent={
                  <View style={styles.emptyState}>
                    <Ionicons name="images" size={48} color="#ccc" />
                    <Text style={styles.emptyStateText}>No media shared yet</Text>
                  </View>
                }
              />
            </View>
          )}
          {activeTab === 'activity' && (
            <View style={styles.tabContent}>
              <ScrollView style={styles.activityContainer}>
                <View style={styles.activitySection}>
                  <Text style={styles.activitySectionTitle}>Group Statistics</Text>
                  <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Total Messages:</Text>
                    <Text style={styles.statValue}>{groupStats.totalMessages || 0}</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Active Members:</Text>
                    <Text style={styles.statValue}>{groupStats.activeMembers || 0}</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Media Shared:</Text>
                    <Text style={styles.statValue}>{groupStats.mediaCount || 0}</Text>
                  </View>
                </View>

                <View style={styles.activitySection}>
                  <Text style={styles.activitySectionTitle}>Recent Activity</Text>
                  {recentActivity.map((activity, index) => (
                    <View key={index} style={styles.activityItem}>
                      <Text style={styles.activityText}>{activity.description}</Text>
                      <Text style={styles.activityTime}>{formatTime(activity.timestamp)}</Text>
                    </View>
                  ))}
                </View>
              </ScrollView>
            </View>
          )}
        </Animated.View>
      </Animated.View>

      {/* Most Active Member Modal */}
      <MostActiveMemberSystem
        groupId={group.id}
        currentUserId={currentUserId}
        visible={showMostActive}
        onClose={() => setShowMostActive(false)}
        onUserPress={onUserPress}
      />
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.overlay,
  },
  modalContent: {
    flex: 1,
    backgroundColor: COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 50,
  },
  header: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text,
  },
  headerButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  groupHeader: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  groupHeaderGradient: {
    paddingBottom: 20,
  },
  coverImage: {
    width: '100%',
    height: 120,
    position: 'absolute',
    top: 0,
  },
  groupAvatarSection: {
    paddingHorizontal: 20,
    paddingTop: 80,
    alignItems: 'center',
  },
  groupAvatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  groupAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    borderColor: COLORS.background,
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: COLORS.success,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.background,
  },
  groupInfo: {
    alignItems: 'center',
    marginBottom: 20,
  },
  groupName: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  groupDescription: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  groupStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.primary,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.textMuted,
    marginTop: 2,
  },
  statSeparator: {
    width: 1,
    height: 20,
    backgroundColor: COLORS.surfaceLight,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    gap: 8,
  },
  actionButton: {
    flex: 1,
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    paddingVertical: 12,
    alignItems: 'center',
    gap: 4,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
  },
  actionButtonText: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '600',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: COLORS.surface,
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  tabItem: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 16,
    marginHorizontal: 2,
  },
  activeTabItem: {
    backgroundColor: COLORS.primary,
  },
  tabText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  activeTabText: {
    color: COLORS.background,
    fontWeight: '700',
  },
  tabContent: {
    flex: 1,
  },
  infoSection: {
    backgroundColor: COLORS.surface,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    gap: 12,
  },
  infoText: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 4,
  },
  tag: {
    backgroundColor: COLORS.primary + '20',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  tagText: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '600',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  settingLabel: {
    fontSize: 16,
    color: COLORS.text,
  },
  settingValue: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  dangerSection: {
    backgroundColor: COLORS.surface,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: COLORS.error + '30',
  },
  dangerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.error + '20',
    borderRadius: 12,
    paddingVertical: 12,
    gap: 8,
  },
  dangerButtonText: {
    fontSize: 16,
    color: COLORS.error,
    fontWeight: '600',
  },
  membersHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  membersCount: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
  },
  manageButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 6,
  },
  manageButtonText: {
    fontSize: 14,
    color: COLORS.background,
    fontWeight: '600',
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  memberLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  memberAvatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1.5,
    borderColor: COLORS.primary,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: COLORS.success,
    borderWidth: 2,
    borderColor: COLORS.background,
  },
  memberInfo: {
    flex: 1,
  },
  memberHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    gap: 8,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
  },
  customTitle: {
    fontSize: 12,
    color: COLORS.warning,
    backgroundColor: COLORS.warning + '20',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    fontWeight: '600',
  },
  memberMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  roleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  joinedText: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  memberRight: {
    alignItems: 'flex-end',
    gap: 4,
  },
  youLabel: {
    fontSize: 12,
    color: COLORS.primary,
    backgroundColor: COLORS.primary + '20',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    fontWeight: '600',
  },
  memberActionButton: {
    padding: 4,
  },
  comingSoonText: {
    fontSize: 16,
    color: COLORS.textMuted,
    textAlign: 'center',
    marginTop: 40,
  },
});

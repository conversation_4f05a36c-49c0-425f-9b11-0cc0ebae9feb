// 📥 COMPREHENSIVE DOWNLOADS PAGE
// Beautiful downloads tracking page with user analytics and interface
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Modal,
  StatusBar,
  Animated,
  FlatList,
  Alert,
  SafeAreaView,
  Image,
  RefreshControl,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { UserInteraction } from '../types/Update';
import { comprehensiveUpdatesService } from '../services/comprehensiveUpdatesService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  downloadColor: '#00FF7F', // Green for downloads
};

interface DownloadUser extends UserInteraction {
  downloadQuality?: 'original' | 'high' | 'medium' | 'low';
  fileSize?: number; // in MB
  downloadDuration?: number; // in seconds
  deviceType?: 'mobile' | 'tablet' | 'desktop';
  location?: string;
}

interface DownloadsAnalytics {
  totalDownloads: number;
  uniqueDownloaders: number;
  totalDataTransferred: number; // in MB
  averageFileSize: number; // in MB
  qualityBreakdown: { quality: string; count: number; percentage: number }[];
  downloadsByHour: { hour: number; count: number }[];
  topCountries: { country: string; count: number }[];
}

interface ComprehensiveDownloadsPageProps {
  visible: boolean;
  updateId: string;
  updateTitle?: string;
  updateThumbnail?: string;
  currentUserId: string;
  onClose: () => void;
  onUserPress?: (userId: string) => void;
}

export const ComprehensiveDownloadsPage: React.FC<ComprehensiveDownloadsPageProps> = ({
  visible,
  updateId,
  updateTitle,
  updateThumbnail,
  currentUserId,
  onClose,
  onUserPress,
}) => {
  const insets = useSafeAreaInsets();
  
  // Responsive dimensions
  const isTablet = SCREEN_WIDTH > 768;
  const headerHeight = 60 + insets.top;

  // ==================== STATE MANAGEMENT ====================
  
  const [downloads, setDownloads] = useState<DownloadUser[]>([]);
  const [analytics, setAnalytics] = useState<DownloadsAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'downloads' | 'analytics'>('downloads');

  // Animation refs
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const downloadAnimations = useRef<{ [key: string]: Animated.Value }>({}).current;

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (visible) {
      loadDownloadsData();
      showModal();
    } else {
      hideModal();
    }
  }, [visible]);

  // ==================== ANIMATION METHODS ====================

  const showModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateDownload = (userId: string) => {
    if (!downloadAnimations[userId]) {
      downloadAnimations[userId] = new Animated.Value(0);
    }

    Animated.sequence([
      Animated.timing(downloadAnimations[userId], {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(downloadAnimations[userId], {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // ==================== DATA METHODS ====================

  const loadDownloadsData = async () => {
    setIsLoading(true);
    try {
      // Load downloads and analytics from service
      // Load real downloads data from service
      const [downloadsResult, analyticsResult] = await Promise.all([
        comprehensiveUpdatesService.getDownloads(updateId),
        comprehensiveUpdatesService.getDownloadAnalytics(updateId)
      ]);

      if (downloadsResult.success && analyticsResult.success) {
        setDownloads(downloadsResult.downloads || []);
        setAnalytics(analyticsResult.analytics || {
          totalDownloads: 0,
          uniqueDownloaders: 0,
          totalDataTransferred: 0,
          averageFileSize: 0,
          qualityBreakdown: [],
          deviceBreakdown: [],
          timeBreakdown: [],
          geographicBreakdown: []
        });
      } else {
        setDownloads([]);
        setAnalytics({
          totalDownloads: 0,
          uniqueDownloaders: 0,
          totalDataTransferred: 0,
          averageFileSize: 0,
          qualityBreakdown: [],
          downloadsByHour: [],
          topCountries: []
        });
      }

      // Remove mock data - using real Firebase data
      /*const mockDownloads: DownloadUser[] = [
        {
          userId: 'user1',
          userName: 'Alice Johnson',
          userAvatar: 'https://randomuser.me/api/portraits/women/1.jpg',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          type: 'download',
          downloadQuality: 'original',
          fileSize: 45.2,
          downloadDuration: 12,
          deviceType: 'mobile',
          location: 'New York, US',
        },
        {
          userId: 'user2',
          userName: 'Bob Smith',
          userAvatar: 'https://randomuser.me/api/portraits/men/2.jpg',
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          type: 'download',
          downloadQuality: 'high',
          fileSize: 28.7,
          downloadDuration: 8,
          deviceType: 'desktop',
          location: 'London, UK',
        },
        {
          userId: 'user3',
          userName: 'Charlie Brown',
          userAvatar: 'https://randomuser.me/api/portraits/men/3.jpg',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          type: 'download',
          downloadQuality: 'medium',
          fileSize: 15.3,
          downloadDuration: 5,
          deviceType: 'tablet',
          location: 'Tokyo, JP',
        },
      ];

      const mockAnalytics: DownloadsAnalytics = {
        totalDownloads: 234,
        uniqueDownloaders: 189,
        totalDataTransferred: 5678.9,
        averageFileSize: 24.3,
        qualityBreakdown: [
          { quality: 'Original', count: 89, percentage: 38.0 },
          { quality: 'High', count: 78, percentage: 33.3 },
          { quality: 'Medium', count: 45, percentage: 19.2 },
          { quality: 'Low', count: 22, percentage: 9.4 },
        ],
        downloadsByHour: [
          { hour: 0, count: 2 }, { hour: 1, count: 1 }, { hour: 2, count: 0 },
          { hour: 3, count: 1 }, { hour: 4, count: 3 }, { hour: 5, count: 5 },
          { hour: 6, count: 8 }, { hour: 7, count: 12 }, { hour: 8, count: 18 },
          { hour: 9, count: 25 }, { hour: 10, count: 22 }, { hour: 11, count: 19 },
          { hour: 12, count: 28 }, { hour: 13, count: 31 }, { hour: 14, count: 26 },
          { hour: 15, count: 23 }, { hour: 16, count: 20 }, { hour: 17, count: 17 },
          { hour: 18, count: 15 }, { hour: 19, count: 12 }, { hour: 20, count: 10 },
          { hour: 21, count: 8 }, { hour: 22, count: 6 }, { hour: 23, count: 4 },
        ],
        topCountries: [
          { country: 'United States', count: 89 },
          { country: 'United Kingdom', count: 45 },
          { country: 'Canada', count: 32 },
          { country: 'Australia', count: 28 },
          { country: 'Germany', count: 23 },
        ],
      };*/
    } catch (error) {
      console.error('❌ Error loading downloads data:', error);
      Alert.alert('Error', 'Failed to load downloads data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadDownloadsData();
    setIsRefreshing(false);
  }, []);

  const handleUserPress = (userId: string) => {
    onUserPress?.(userId);
  };

  const getTimeAgo = (timestamp: Date) => {
    const now = Date.now();
    const diff = now - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const formatFileSize = (sizeInMB: number) => {
    if (sizeInMB >= 1000) {
      return `${(sizeInMB / 1000).toFixed(1)}GB`;
    }
    return `${sizeInMB.toFixed(1)}MB`;
  };

  const getQualityColor = (quality: string) => {
    switch (quality.toLowerCase()) {
      case 'original': return COLORS.success;
      case 'high': return COLORS.primary;
      case 'medium': return COLORS.warning;
      case 'low': return COLORS.error;
      default: return COLORS.textMuted;
    }
  };

  const getQualityIcon = (quality: string) => {
    switch (quality.toLowerCase()) {
      case 'original': return 'diamond';
      case 'high': return 'star';
      case 'medium': return 'star-half';
      case 'low': return 'star-outline';
      default: return 'help-circle';
    }
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="chevron-down" size={28} color={COLORS.text} />
          </TouchableOpacity>
          
          <View style={styles.headerInfo}>
            <View style={styles.headerTitleContainer}>
              <Ionicons name="download" size={24} color={COLORS.downloadColor} />
              <Text style={styles.headerTitle}>Downloads</Text>
            </View>
            <Text style={styles.headerSubtitle}>
              {analytics?.totalDownloads || 0} total downloads
            </Text>
          </View>

          <TouchableOpacity style={styles.headerAction}>
            <Ionicons name="share-outline" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>

        {/* Tab Bar */}
        <View style={styles.tabBar}>
          <TouchableOpacity
            style={[styles.tabItem, activeTab === 'downloads' && styles.activeTabItem]}
            onPress={() => setActiveTab('downloads')}
          >
            <Text style={[styles.tabText, activeTab === 'downloads' && styles.activeTabText]}>
              Downloads
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tabItem, activeTab === 'analytics' && styles.activeTabItem]}
            onPress={() => setActiveTab('analytics')}
          >
            <Text style={[styles.tabText, activeTab === 'analytics' && styles.activeTabText]}>
              Analytics
            </Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );

  const renderDownloadItem = ({ item: download, index }: { item: DownloadUser; index: number }) => (
    <Animated.View
      style={[
        styles.downloadContainer,
        {
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [30, 0],
            }),
          }],
        },
      ]}
    >
      <TouchableOpacity 
        style={styles.downloadItem}
        onPress={() => handleUserPress(download.userId)}
        activeOpacity={0.7}
      >
        <View style={styles.downloadLeft}>
          <View style={styles.avatarContainer}>
            <Image 
              source={{ uri: download.userAvatar || 'https://via.placeholder.com/50' }} 
              style={styles.avatarImage} 
            />
            <View style={styles.downloadBadge}>
              <Ionicons name="download" size={12} color={COLORS.downloadColor} />
            </View>
          </View>

          <View style={styles.userInfo}>
            <Text style={styles.userName}>{download.userName}</Text>
            <View style={styles.downloadMeta}>
              <Text style={styles.timeAgo}>{getTimeAgo(download.timestamp)}</Text>
              {download.location && (
                <>
                  <Text style={styles.metaSeparator}>•</Text>
                  <Text style={styles.location}>{download.location}</Text>
                </>
              )}
            </View>
          </View>
        </View>

        <View style={styles.downloadRight}>
          {download.downloadQuality && (
            <View style={[styles.qualityBadge, { backgroundColor: getQualityColor(download.downloadQuality) + '20' }]}>
              <Ionicons 
                name={getQualityIcon(download.downloadQuality) as any} 
                size={12} 
                color={getQualityColor(download.downloadQuality)} 
              />
              <Text style={[styles.qualityText, { color: getQualityColor(download.downloadQuality) }]}>
                {download.downloadQuality}
              </Text>
            </View>
          )}
          {download.fileSize && (
            <View style={styles.sizeBadge}>
              <Text style={styles.sizeText}>
                {formatFileSize(download.fileSize)}
              </Text>
            </View>
          )}
          <TouchableOpacity
            style={styles.messageButton}
            onPress={() => {
              // Navigate to chat with this user
              console.log('Start chat with:', download.userId);
            }}
          >
            <Ionicons name="chatbubble-outline" size={14} color={COLORS.downloadColor} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderAnalyticsCard = (title: string, value: string, subtitle?: string, icon?: string) => (
    <Animated.View 
      style={[
        styles.analyticsCard,
        {
          opacity: fadeAnim,
          transform: [{
            scale: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.8, 1],
            }),
          }],
        },
      ]}
    >
      <View style={styles.cardHeader}>
        {icon && <Ionicons name={icon as any} size={20} color={COLORS.downloadColor} />}
        <Text style={styles.cardTitle}>{title}</Text>
      </View>
      <Text style={styles.cardValue}>{value}</Text>
      {subtitle && <Text style={styles.cardSubtitle}>{subtitle}</Text>}
    </Animated.View>
  );

  const renderAnalytics = () => {
    if (!analytics) return null;

    return (
      <ScrollView 
        style={styles.analyticsContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={COLORS.primary}
            colors={[COLORS.primary]}
          />
        }
      >
        {/* Overview Cards */}
        <View style={styles.overviewGrid}>
          {renderAnalyticsCard(
            'Total Downloads', 
            analytics.totalDownloads.toLocaleString(), 
            'All time',
            'download'
          )}
          {renderAnalyticsCard(
            'Unique Users', 
            analytics.uniqueDownloaders.toLocaleString(), 
            'Individual downloaders',
            'people'
          )}
          {renderAnalyticsCard(
            'Data Transfer', 
            formatFileSize(analytics.totalDataTransferred), 
            'Total bandwidth',
            'cloud-download'
          )}
          {renderAnalyticsCard(
            'Avg. File Size', 
            formatFileSize(analytics.averageFileSize), 
            'Per download',
            'document'
          )}
        </View>

        {/* Quality Breakdown */}
        <View style={styles.chartSection}>
          <Text style={styles.sectionTitle}>Quality Breakdown</Text>
          <View style={styles.qualityChart}>
            {analytics.qualityBreakdown.map((quality, index) => (
              <View key={quality.quality} style={styles.qualityItem}>
                <View style={styles.qualityInfo}>
                  <Ionicons 
                    name={getQualityIcon(quality.quality) as any} 
                    size={16} 
                    color={getQualityColor(quality.quality)} 
                  />
                  <Text style={styles.qualityType}>{quality.quality}</Text>
                </View>
                <Text style={styles.qualityPercentage}>{quality.percentage}%</Text>
                <Text style={styles.qualityCount}>({quality.count})</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Top Countries */}
        <View style={styles.chartSection}>
          <Text style={styles.sectionTitle}>Top Countries</Text>
          <View style={styles.countriesChart}>
            {analytics.topCountries.map((country, index) => (
              <View key={country.country} style={styles.countryItem}>
                <Text style={styles.countryName}>{country.country}</Text>
                <View style={styles.countryBar}>
                  <Animated.View 
                    style={[
                      styles.countryBarFill,
                      {
                        width: `${(country.count / analytics.topCountries[0].count) * 100}%`,
                        backgroundColor: COLORS.downloadColor,
                      }
                    ]}
                  />
                </View>
                <Text style={styles.countryCount}>{country.count}</Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    );
  };

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="none" statusBarTranslucent>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        <Animated.View 
          style={[
            styles.modalContent,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {renderHeader()}

          {/* Content */}
          {activeTab === 'downloads' ? (
            <FlatList
              data={downloads}
              renderItem={renderDownloadItem}
              keyExtractor={(item) => item.userId}
              style={styles.downloadsList}
              contentContainerStyle={styles.downloadsListContent}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={isRefreshing}
                  onRefresh={handleRefresh}
                  tintColor={COLORS.primary}
                  colors={[COLORS.primary]}
                />
              }
              ListEmptyComponent={() => (
                <View style={styles.emptyContainer}>
                  <Ionicons name="download-outline" size={64} color={COLORS.textMuted} />
                  <Text style={styles.emptyText}>No downloads yet</Text>
                  <Text style={styles.emptySubtext}>Share your update to get more downloads!</Text>
                </View>
              )}
            />
          ) : (
            renderAnalytics()
          )}
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.overlay,
  },
  modalContent: {
    flex: 1,
    backgroundColor: COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 50,
  },
  header: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingBottom: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 16,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text,
    marginLeft: 8,
  },
  headerSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  headerAction: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 8,
  },
  tabItem: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 20,
    marginHorizontal: 4,
  },
  activeTabItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  tabText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  activeTabText: {
    color: COLORS.text,
    fontWeight: '700',
  },
  downloadsList: {
    flex: 1,
  },
  downloadsListContent: {
    paddingVertical: 8,
  },
  downloadContainer: {
    marginBottom: 4,
  },
  downloadItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.surface,
    marginHorizontal: 16,
    marginVertical: 2,
    borderRadius: 16,
    shadowColor: COLORS.downloadColor,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  downloadLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatarImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  downloadBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: COLORS.downloadColor,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.surface,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  downloadMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeAgo: {
    fontSize: 13,
    color: COLORS.textMuted,
  },
  metaSeparator: {
    fontSize: 13,
    color: COLORS.textMuted,
    marginHorizontal: 6,
  },
  location: {
    fontSize: 13,
    color: COLORS.primary,
    fontWeight: '500',
  },
  downloadRight: {
    alignItems: 'flex-end',
    gap: 4,
  },
  qualityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  qualityText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  sizeBadge: {
    backgroundColor: COLORS.surfaceLight,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  sizeText: {
    fontSize: 11,
    color: COLORS.textSecondary,
    fontWeight: '600',
  },
  followingBadge: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  followingText: {
    fontSize: 9,
    color: COLORS.background,
    fontWeight: '600',
  },
  analyticsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  analyticsCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    padding: 16,
    width: '48%',
    marginBottom: 12,
    shadowColor: COLORS.downloadColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginLeft: 6,
  },
  cardValue: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  chartSection: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 16,
  },
  qualityChart: {
    gap: 12,
  },
  qualityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  qualityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  qualityType: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
    marginLeft: 8,
  },
  qualityPercentage: {
    fontSize: 16,
    color: COLORS.downloadColor,
    fontWeight: '600',
    marginRight: 8,
  },
  qualityCount: {
    fontSize: 14,
    color: COLORS.textMuted,
  },
  countriesChart: {
    gap: 12,
  },
  countryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  countryName: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '500',
    width: 100,
  },
  countryBar: {
    flex: 1,
    height: 8,
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 4,
    overflow: 'hidden',
  },
  countryBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  countryCount: {
    fontSize: 14,
    color: COLORS.downloadColor,
    fontWeight: '600',
    width: 40,
    textAlign: 'right',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginTop: 4,
    textAlign: 'center',
  },
  messageButton: {
    padding: 6,
    borderRadius: 12,
    backgroundColor: COLORS.surfaceLight,
  },
});

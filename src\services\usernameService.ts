// 🔥 REAL USERNAME SERVICE - COMPLETE FIREBASE INTEGRATION
// No mockups, no fake data - 100% real Firebase username functionality

import { 
  collection, 
  query, 
  where, 
  getDocs, 
  doc, 
  updateDoc,
  serverTimestamp 
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import { db, functions } from './firebaseSimple';

// Real Username Interface
interface UsernameCheckResult {
  available: boolean;
  reason?: string;
}

interface UsernameUpdateResult {
  success: boolean;
  username?: string;
  error?: string;
}

class UsernameService {
  
  // ==================== REAL USERNAME VALIDATION ====================
  
  validateUsername(username: string): { valid: boolean; error?: string } {
    if (!username || typeof username !== 'string') {
      return { valid: false, error: 'Username is required' };
    }

    const trimmed = username.trim();
    
    if (trimmed.length < 3) {
      return { valid: false, error: 'Username must be at least 3 characters' };
    }
    
    if (trimmed.length > 20) {
      return { valid: false, error: 'Username must be 20 characters or less' };
    }
    
    // Only allow alphanumeric characters and underscores
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(trimmed)) {
      return { valid: false, error: 'Username can only contain letters, numbers, and underscores' };
    }
    
    // Don't allow usernames that start with numbers
    if (/^[0-9]/.test(trimmed)) {
      return { valid: false, error: 'Username cannot start with a number' };
    }
    
    // Reserved usernames
    const reserved = [
      'admin', 'administrator', 'root', 'system', 'support', 'help',
      'irachat', 'iratech', 'official', 'verified', 'staff', 'team',
      'api', 'www', 'mail', 'email', 'ftp', 'http', 'https', 'ssl',
      'user', 'users', 'guest', 'anonymous', 'null', 'undefined'
    ];
    
    if (reserved.includes(trimmed.toLowerCase())) {
      return { valid: false, error: 'This username is reserved' };
    }
    
    return { valid: true };
  }

  // ==================== REAL USERNAME AVAILABILITY CHECK ====================
  
  async checkUsernameAvailability(username: string): Promise<UsernameCheckResult> {
    try {
      console.log('🔥 Checking username availability with real Firebase:', username);
      
      // First validate format
      const validation = this.validateUsername(username);
      if (!validation.valid) {
        return { available: false, reason: validation.error };
      }

      // Use Cloud Function for server-side check (more reliable)
      try {
        const checkUsernameFunction = httpsCallable(functions, 'checkUsernameAvailability');
        const result = await checkUsernameFunction({ username: username.trim() });
        
        console.log('✅ Cloud Function username check result:', result.data);
        return result.data as UsernameCheckResult;
      } catch (cloudError) {
        console.warn('⚠️ Cloud Function unavailable, falling back to client check:', cloudError);
        
        // Fallback to client-side check
        return await this.checkUsernameClient(username.trim());
      }
    } catch (error) {
      console.error('❌ Error checking username availability:', error);
      return { available: false, reason: 'Unable to check availability' };
    }
  }

  private async checkUsernameClient(username: string): Promise<UsernameCheckResult> {
    try {
      const usernameQuery = query(
        collection(db, 'users'),
        where('username', '==', username)
      );
      
      const snapshot = await getDocs(usernameQuery);
      const available = snapshot.empty;
      
      console.log('✅ Client-side username check:', { username, available });
      return { 
        available, 
        reason: available ? undefined : 'Username is already taken' 
      };
    } catch (error) {
      console.error('❌ Error in client username check:', error);
      return { available: false, reason: 'Unable to verify availability' };
    }
  }

  // ==================== REAL USERNAME UPDATE ====================
  
  async updateUsername(userId: string, newUsername: string): Promise<UsernameUpdateResult> {
    try {
      console.log('🔥 Updating username with real Firebase:', { userId, newUsername });
      
      // Validate new username
      const validation = this.validateUsername(newUsername);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      const trimmedUsername = newUsername.trim();

      // Check availability
      const availability = await this.checkUsernameAvailability(trimmedUsername);
      if (!availability.available) {
        return { success: false, error: availability.reason || 'Username not available' };
      }

      // Use Cloud Function for server-side update (more reliable)
      try {
        const updateUsernameFunction = httpsCallable(functions, 'updateUsername');
        const result = await updateUsernameFunction({ newUsername: trimmedUsername });
        
        console.log('✅ Cloud Function username update result:', result.data);
        return result.data as UsernameUpdateResult;
      } catch (cloudError) {
        console.warn('⚠️ Cloud Function unavailable, falling back to client update:', cloudError);
        
        // Fallback to client-side update
        return await this.updateUsernameClient(userId, trimmedUsername);
      }
    } catch (error) {
      console.error('❌ Error updating username:', error);
      return { success: false, error: 'Failed to update username' };
    }
  }

  private async updateUsernameClient(userId: string, username: string): Promise<UsernameUpdateResult> {
    try {
      // Double-check availability before updating
      const availability = await this.checkUsernameClient(username);
      if (!availability.available) {
        return { success: false, error: availability.reason || 'Username not available' };
      }

      // Update user document
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        username,
        updatedAt: serverTimestamp(),
      });

      console.log('✅ Client-side username update successful');
      return { success: true, username };
    } catch (error) {
      console.error('❌ Error in client username update:', error);
      return { success: false, error: 'Failed to update username' };
    }
  }

  // ==================== REAL USERNAME SUGGESTIONS ====================
  
  async generateUsernameSuggestions(baseName: string): Promise<string[]> {
    try {
      console.log('🔥 Generating username suggestions for:', baseName);
      
      const suggestions: string[] = [];
      const baseUsername = baseName.toLowerCase().replace(/[^a-z0-9]/g, '');
      
      if (baseUsername.length < 3) {
        return ['user123', 'newuser', 'irachat_user'];
      }

      // Generate variations
      const variations = [
        baseUsername,
        `${baseUsername}_`,
        `${baseUsername}123`,
        `${baseUsername}_user`,
        `${baseUsername}${new Date().getFullYear()}`,
        `the_${baseUsername}`,
        `${baseUsername}_official`,
        `${baseUsername}${Math.floor(Math.random() * 100)}`,
        `${baseUsername}_${Math.floor(Math.random() * 1000)}`,
        `${baseUsername.substring(0, 10)}_${Date.now().toString().slice(-4)}`,
      ];

      // Check availability for each variation
      for (const variation of variations) {
        if (suggestions.length >= 5) break; // Limit to 5 suggestions
        
        const validation = this.validateUsername(variation);
        if (validation.valid) {
          const availability = await this.checkUsernameAvailability(variation);
          if (availability.available) {
            suggestions.push(variation);
          }
        }
      }

      // If we don't have enough suggestions, add some random ones
      while (suggestions.length < 3) {
        const randomSuffix = Math.floor(Math.random() * 10000);
        const randomUsername = `${baseUsername}${randomSuffix}`;
        
        const validation = this.validateUsername(randomUsername);
        if (validation.valid) {
          const availability = await this.checkUsernameAvailability(randomUsername);
          if (availability.available && !suggestions.includes(randomUsername)) {
            suggestions.push(randomUsername);
          }
        }
      }

      console.log('✅ Generated username suggestions:', suggestions);
      return suggestions;
    } catch (error) {
      console.error('❌ Error generating username suggestions:', error);
      return ['user123', 'newuser', 'irachat_user'];
    }
  }

  // ==================== REAL USERNAME SEARCH ====================
  
  async searchUsersByUsername(searchTerm: string, limit: number = 10): Promise<any[]> {
    try {
      console.log('🔥 Searching users by username:', searchTerm);
      
      if (!searchTerm || searchTerm.trim().length < 2) {
        return [];
      }

      const trimmedSearch = searchTerm.trim().toLowerCase();
      
      // Search for usernames that start with the search term
      const usersQuery = query(
        collection(db, 'users'),
        where('username', '>=', trimmedSearch),
        where('username', '<=', trimmedSearch + '\uf8ff')
      );

      const snapshot = await getDocs(usersQuery);
      const users = snapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter((user: any) => user.username?.toLowerCase().includes(trimmedSearch))
        .slice(0, limit);

      console.log('✅ Found users:', users.length);
      return users;
    } catch (error) {
      console.error('❌ Error searching users by username:', error);
      return [];
    }
  }

  // ==================== UTILITY METHODS ====================
  
  formatUsername(username: string): string {
    return username.trim().toLowerCase();
  }

  isValidUsernameFormat(username: string): boolean {
    return this.validateUsername(username).valid;
  }



  getUsernameFromDisplayName(displayName: string): string {
    const baseUsername = displayName.toLowerCase().replace(/[^a-z0-9]/g, '');
    return baseUsername.length >= 3 ? baseUsername : 'user';
  }
}

export const usernameService = new UsernameService();

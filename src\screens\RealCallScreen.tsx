// 🔥 REAL CALL SCREEN - COMPLETE WEBRTC IMPLEMENTATION
// No mockups, no fake data - 100% real WebRTC calling functionality

import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Animated,
  Alert,
  Platform,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { RTCView } from 'react-native-webrtc';
import { useRealCallManager } from '../hooks/useRealCallManager';
import { useRoute, useNavigation } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';
import {
  formatCallDuration,
  getConnectionQualityColor,
  getConnectionQualityIcon
} from '../utils/callUtils';

const { width, height } = Dimensions.get('window');

interface RouteParams {
  callId?: string;
  isOutgoing?: boolean;
  contactId?: string;
  contactName?: string;
  callType?: 'voice' | 'video';
}

interface RealCallScreenProps {
  callId: string;
  isOutgoing: boolean;
  contactId?: string;
  contactName?: string;
  callType?: 'voice' | 'video';
}

export const RealCallScreen: React.FC<RealCallScreenProps> = (props) => {
  // ==================== REAL NAVIGATION & ROUTE ====================

  const route = useRoute();
  const navigation = useNavigation();
  const routeParams = route.params as RouteParams;

  // Use props if provided, otherwise use route params
  const params = useMemo(() => ({
    callId: props.callId || routeParams?.callId,
    isOutgoing: props.isOutgoing ?? routeParams?.isOutgoing,
    contactId: props.contactId || routeParams?.contactId,
    contactName: props.contactName || routeParams?.contactName,
    callType: props.callType || routeParams?.callType,
  }), [props.callId, props.isOutgoing, props.contactId, props.contactName, props.callType, routeParams]);

  // ==================== REAL CALL MANAGER ====================
  
  const {
    callState,
    permissions,
    isInitialized,
    answerCall,
    endCall,
    declineCall,
    toggleMute,
    toggleVideo,
    switchCamera,
    toggleSpeaker,
  } = useRealCallManager();

  // ==================== REAL STATE MANAGEMENT ====================
  
  const [showControls, setShowControls] = useState(true);
  const [isLocalVideoLarge, setIsLocalVideoLarge] = useState(false);
  const [callStartTime] = useState(new Date());

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const controlsTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // ==================== REAL UI UTILITIES ====================

  const resetControlsTimeout = useCallback(() => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }

    controlsTimeoutRef.current = setTimeout(() => {
      if (callState.currentCall?.type === 'video') {
        setShowControls(false);
      }
    }, 5000);
  }, [callState.currentCall?.type]);

  // ==================== REAL EFFECTS ====================

  useEffect(() => {
    console.log('🔥 Real call screen mounted:', params);
    
    if (!isInitialized) {
      console.warn('⚠️ Call manager not initialized');
      return;
    }

    // Set up auto-hide controls for video calls
    if (callState.currentCall?.type === 'video') {
      resetControlsTimeout();
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [isInitialized, callState.currentCall?.type, params, resetControlsTimeout]);

  useEffect(() => {
    // Handle call state changes
    if (callState.currentCall) {
      if (callState.currentCall.status === 'ended' || 
          callState.currentCall.status === 'declined' ||
          callState.currentCall.status === 'failed') {
        // Call ended, navigate back
        setTimeout(() => {
          navigation.goBack();
        }, 1000);
      }
    }
  }, [callState.currentCall?.status, callState.currentCall, navigation]);

  // ==================== REAL CALL CONTROLS ====================

  const handleEndCall = async () => {
    try {
      console.log('🔥 Ending call from screen...');
      await endCall();
      navigation.goBack();
    } catch (error) {
      console.error('❌ Error ending call:', error);
      navigation.goBack();
    }
  };

  const handleAnswerCall = async () => {
    try {
      console.log('🔥 Answering call from screen...');
      const result = await answerCall();
      if (!result.success) {
        Alert.alert('Error', result.error || 'Failed to answer call');
      }
    } catch (error) {
      console.error('❌ Error answering call:', error);
      Alert.alert('Error', 'Failed to answer call');
    }
  };

  const handleDeclineCall = async () => {
    try {
      console.log('🔥 Declining call from screen...');
      await declineCall();
      navigation.goBack();
    } catch (error) {
      console.error('❌ Error declining call:', error);
      navigation.goBack();
    }
  };

  const handleToggleMute = async () => {
    try {
      await toggleMute();
    } catch (error) {
      console.error('❌ Error toggling mute:', error);
    }
  };

  const handleToggleVideo = async () => {
    try {
      await toggleVideo();
    } catch (error) {
      console.error('❌ Error toggling video:', error);
    }
  };

  const handleSwitchCamera = async () => {
    try {
      await switchCamera();
    } catch (error) {
      console.error('❌ Error switching camera:', error);
    }
  };

  const handleToggleSpeaker = async () => {
    try {
      await toggleSpeaker();
    } catch (error) {
      console.error('❌ Error toggling speaker:', error);
    }
  };

  // ==================== REAL UI UTILITIES ====================

  const handleScreenTap = () => {
    if (callState.currentCall?.type === 'video') {
      setShowControls(!showControls);
      resetControlsTimeout();
    }
  };

  const toggleVideoView = () => {
    setIsLocalVideoLarge(!isLocalVideoLarge);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Utility functions are now imported from callUtils

  // ==================== REAL RENDER ====================

  if (!callState.currentCall) {
    return (
      <View style={styles.container}>
        <StatusBar hidden />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading call...</Text>
        </View>
      </View>
    );
  }

  const { currentCall } = callState;
  const isVideoCall = currentCall.type === 'video';
  const isIncoming = currentCall.direction === 'incoming' && currentCall.status === 'ringing';

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar hidden />

      {/* Call Info Header */}
      <View style={styles.callInfoHeader}>
        <Text style={styles.callTimeText}>
          Started: {callStartTime.toLocaleTimeString()}
        </Text>
        {!permissions.camera && isVideoCall && (
          <Text style={styles.permissionWarning}>
            Camera permission needed {Platform.OS === 'ios' ? '(iOS)' : '(Android)'}
          </Text>
        )}
        {!permissions.microphone && (
          <Text style={styles.permissionWarning}>
            Microphone permission needed {Platform.OS === 'ios' ? '(iOS)' : '(Android)'}
          </Text>
        )}
      </View>

      {/* Video Call Layout */}
      {isVideoCall ? (
        <TouchableOpacity 
          style={styles.videoContainer} 
          onPress={handleScreenTap}
          activeOpacity={1}
        >
          {/* Remote Video (Main) */}
          {callState.remoteStream && !isLocalVideoLarge && (
            <View style={styles.remoteVideo}>
              <RTCView
                streamURL={callState.remoteStream.toURL()}
                style={{ flex: 1 }}
              />
            </View>
          )}

          {/* Local Video (Main when toggled) */}
          {callState.localStream && isLocalVideoLarge && (
            <View style={styles.remoteVideo}>
              <RTCView
                streamURL={callState.localStream.toURL()}
                style={{ flex: 1 }}
              />
            </View>
          )}

          {/* Picture-in-Picture Video */}
          {callState.localStream && !isLocalVideoLarge && (
            <TouchableOpacity
              style={styles.localVideoContainer}
              onPress={toggleVideoView}
            >
              <View style={styles.localVideo}>
                <RTCView
                  streamURL={callState.localStream.toURL()}
                  style={{ flex: 1 }}
                />
              </View>
            </TouchableOpacity>
          )}

          {/* Remote Video (PiP when toggled) */}
          {callState.remoteStream && isLocalVideoLarge && (
            <TouchableOpacity
              style={styles.localVideoContainer}
              onPress={toggleVideoView}
            >
              <View style={styles.localVideo}>
                <RTCView
                  streamURL={callState.remoteStream.toURL()}
                  style={{ flex: 1 }}
                />
              </View>
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      ) : (
        /* Audio Call Layout */
        <LinearGradient
          colors={['#1a1a1a', '#2d2d2d', '#1a1a1a']}
          style={styles.audioContainer}
        >
          <View style={styles.audioCallInfo}>
            <Text style={styles.contactName}>
              {currentCall.direction === 'outgoing' ? currentCall.receiverName : currentCall.callerName}
            </Text>
            <Text style={styles.callStatus}>
              {callState.isConnected ? formatCallDuration(callState.callDuration) : currentCall.status}
            </Text>

            {/* Connection Quality Indicator */}
            <View style={styles.qualityIndicator}>
              <Ionicons
                name={getConnectionQualityIcon(callState.connectionQuality) as any}
                size={16}
                color={getConnectionQualityColor(callState.connectionQuality)}
              />
              <Text style={[styles.qualityText, { color: getConnectionQualityColor(callState.connectionQuality) }]}>
                {callState.connectionQuality}
              </Text>
            </View>
          </View>
        </LinearGradient>
      )}

      {/* Call Controls */}
      {(showControls || !isVideoCall) && (
        <Animated.View style={[styles.controlsContainer, { opacity: fadeAnim }]}>
          <BlurView intensity={80} style={styles.controlsBlur}>
            {isIncoming ? (
              /* Incoming Call Controls */
              <View style={styles.incomingControls}>
                <TouchableOpacity
                  style={[styles.controlButton, styles.declineButton]}
                  onPress={handleDeclineCall}
                >
                  <Ionicons name="call" size={32} color="white" />
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.controlButton, styles.answerButton]}
                  onPress={handleAnswerCall}
                >
                  <Ionicons name="call" size={32} color="white" />
                </TouchableOpacity>
              </View>
            ) : (
              /* Active Call Controls */
              <View style={styles.activeControls}>
                <TouchableOpacity
                  style={[styles.controlButton, callState.isMuted && styles.activeControl]}
                  onPress={handleToggleMute}
                >
                  <Ionicons
                    name={callState.isMuted ? 'mic-off' : 'mic'}
                    size={24}
                    color={callState.isMuted ? '#FF6B6B' : 'white'}
                  />
                </TouchableOpacity>

                {isVideoCall && (
                  <TouchableOpacity
                    style={[styles.controlButton, !callState.isVideoEnabled && styles.activeControl]}
                    onPress={handleToggleVideo}
                  >
                    <Ionicons
                      name={callState.isVideoEnabled ? 'videocam' : 'videocam-off'}
                      size={24}
                      color={callState.isVideoEnabled ? 'white' : '#FF6B6B'}
                    />
                  </TouchableOpacity>
                )}

                <TouchableOpacity
                  style={[styles.controlButton, styles.endCallButton]}
                  onPress={handleEndCall}
                >
                  <Ionicons name="call" size={28} color="white" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.controlButton, callState.isSpeakerOn && styles.activeControl]}
                  onPress={handleToggleSpeaker}
                >
                  <Ionicons
                    name={callState.isSpeakerOn ? 'volume-high' : 'volume-medium'}
                    size={24}
                    color={callState.isSpeakerOn ? '#4ECDC4' : 'white'}
                  />
                </TouchableOpacity>

                {isVideoCall && (
                  <TouchableOpacity
                    style={styles.controlButton}
                    onPress={handleSwitchCamera}
                  >
                    <Ionicons name="camera-reverse" size={24} color="white" />
                  </TouchableOpacity>
                )}
              </View>
            )}
          </BlurView>
        </Animated.View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    width: width,
    minHeight: height,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 18,
  },
  videoContainer: {
    flex: 1,
  },
  remoteVideo: {
    flex: 1,
  },
  localVideoContainer: {
    position: 'absolute',
    top: 60,
    right: 20,
    width: 120,
    height: 160,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'white',
  },
  localVideo: {
    flex: 1,
  },
  videoView: {
    flex: 1,
  },
  audioContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  audioCallInfo: {
    alignItems: 'center',
  },
  contactName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 16,
  },
  callStatus: {
    fontSize: 18,
    color: '#999',
    marginBottom: 24,
  },
  qualityIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  qualityText: {
    fontSize: 14,
    marginLeft: 8,
    textTransform: 'capitalize',
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  controlsBlur: {
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  incomingControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  activeControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeControl: {
    backgroundColor: 'rgba(255, 107, 107, 0.3)',
  },
  answerButton: {
    backgroundColor: '#4CAF50',
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  declineButton: {
    backgroundColor: '#F44336',
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  endCallButton: {
    backgroundColor: '#F44336',
    width: 70,
    height: 70,
    borderRadius: 35,
  },
  callInfoHeader: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    zIndex: 10,
    alignItems: 'center',
  },
  callTimeText: {
    color: 'white',
    fontSize: 14,
    marginBottom: 4,
  },
  permissionWarning: {
    color: '#FF6B6B',
    fontSize: 12,
    textAlign: 'center',
  },
});

export default RealCallScreen;

# Build artifacts that increase bundle size
*.log
*.tmp
.expo/
.expo-shared/
dist/
build/
android/app/build/
ios/build/
node_modules/

# Environment and sensitive files
.env
.env.*
.env.production
.env.staging
.env.local
!.env.example
*.key
*.pem
google-services.json
GoogleService-Info.plist
firebase-service-account.json
firebase-adminsdk-*.json

# Sensitive documentation files with credentials
*FIREBASE*COMPLETE*.txt
*FIREBASE*SETUP*.md
*FINAL*STATUS*.md
*CREDENTIALS*.md
*API*KEYS*.md
EXHAUSTIVE_FIREBASE_RULES_COMPLETE.txt
NEW_FIREBASE_SETUP_GUIDE.md
FINAL_STATUS.md

# Large development files
*.psd
*.ai
*.sketch
*.fig
*.docx
*.pdf

# Temporary files
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# IDE files
.vscode/
.idea/
*.sublime-*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli
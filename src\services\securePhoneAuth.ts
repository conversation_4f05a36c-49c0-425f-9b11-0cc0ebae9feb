// 🔒 Secure Phone Authentication Service for IraChat
// Implements security best practices for phone-based authentication

import {
  Con<PERSON>rma<PERSON><PERSON><PERSON><PERSON>,
  RecaptchaVerifier,
  signInWith<PERSON>hone<PERSON><PERSON>ber,
  User as FirebaseUser,

} from 'firebase/auth';
import { doc, setDoc, getDoc, serverTimestamp, Timestamp } from 'firebase/firestore';
import { auth, db } from './firebaseSimple';

// Security configuration
const SECURITY_CONFIG = {
  MAX_ATTEMPTS_PER_HOUR: 3,
  MAX_ATTEMPTS_PER_DAY: 10,
  CODE_EXPIRY_MINUTES: 10,
  SESSION_TIMEOUT_HOURS: 24,
  RATE_LIMIT_WINDOW_MINUTES: 60,
};

// Rate limiting storage
interface RateLimitData {
  attempts: number;
  lastAttempt: Timestamp;
  blockedUntil?: Timestamp;
}

// Phone number validation with security
class SecurePhoneValidator {
  private static readonly E164_REGEX = /^\+[1-9]\d{1,14}$/;
  private static readonly SUSPICIOUS_PATTERNS = [
    /^\+1{10,}$/, // Too many 1s
    /^\+(\d)\1{9,}$/, // Repeated digits
    /^\+999\d+$/, // Test numbers
    /^\+000\d+$/, // Invalid area codes
  ];

  static validate(phoneNumber: string): { isValid: boolean; error?: string } {
    // Basic format validation
    if (!this.E164_REGEX.test(phoneNumber)) {
      return {
        isValid: false,
        error: 'Invalid phone number format. Use international format (+1234567890)',
      };
    }

    // Check for suspicious patterns
    for (const pattern of this.SUSPICIOUS_PATTERNS) {
      if (pattern.test(phoneNumber)) {
        return {
          isValid: false,
          error: 'Invalid phone number',
        };
      }
    }

    // Length validation
    const digits = phoneNumber.replace(/\D/g, '');
    if (digits.length < 7 || digits.length > 15) {
      return {
        isValid: false,
        error: 'Phone number length is invalid',
      };
    }

    return { isValid: true };
  }

  static sanitize(phoneNumber: string): string {
    // Remove all non-digit characters except +
    let cleaned = phoneNumber.replace(/[^\d+]/g, '');
    
    // Ensure it starts with +
    if (!cleaned.startsWith('+')) {
      cleaned = '+' + cleaned;
    }

    return cleaned;
  }
}

// Rate limiting service
class RateLimitService {
  private static async getRateLimitData(phoneNumber: string): Promise<RateLimitData | null> {
    try {
      const docRef = doc(db, 'rateLimits', phoneNumber);
      const docSnap = await getDoc(docRef);
      return docSnap.exists() ? docSnap.data() as RateLimitData : null;
    } catch (error) {
      console.error('Error getting rate limit data:', error);
      return null;
    }
  }

  private static async updateRateLimitData(phoneNumber: string, data: RateLimitData): Promise<void> {
    try {
      const docRef = doc(db, 'rateLimits', phoneNumber);
      await setDoc(docRef, data, { merge: true });
    } catch (error) {
      console.error('Error updating rate limit data:', error);
    }
  }

  static async checkRateLimit(phoneNumber: string): Promise<{ allowed: boolean; error?: string }> {
    const data = await this.getRateLimitData(phoneNumber);
    const now = Timestamp.now();

    if (!data) {
      // First attempt
      await this.updateRateLimitData(phoneNumber, {
        attempts: 1,
        lastAttempt: now,
      });
      return { allowed: true };
    }

    // Check if blocked
    if (data.blockedUntil && data.blockedUntil.toMillis() > now.toMillis()) {
      const remainingMinutes = Math.ceil((data.blockedUntil.toMillis() - now.toMillis()) / 60000);
      return {
        allowed: false,
        error: `Too many attempts. Try again in ${remainingMinutes} minutes.`,
      };
    }

    // Check hourly limit
    const hourAgo = new Timestamp(now.seconds - 3600, now.nanoseconds);
    if (data.lastAttempt.toMillis() > hourAgo.toMillis()) {
      if (data.attempts >= SECURITY_CONFIG.MAX_ATTEMPTS_PER_HOUR) {
        const blockedUntil = new Timestamp(now.seconds + 3600, now.nanoseconds);
        await this.updateRateLimitData(phoneNumber, {
          ...data,
          blockedUntil,
        });
        return {
          allowed: false,
          error: 'Too many attempts. Try again in 1 hour.',
        };
      }

      // Increment attempts
      await this.updateRateLimitData(phoneNumber, {
        attempts: data.attempts + 1,
        lastAttempt: now,
      });
    } else {
      // Reset counter after an hour
      await this.updateRateLimitData(phoneNumber, {
        attempts: 1,
        lastAttempt: now,
      });
    }

    return { allowed: true };
  }
}

// Secure phone authentication service
export class SecurePhoneAuthService {
  private recaptchaVerifier: RecaptchaVerifier | null = null;
  private confirmationResult: ConfirmationResult | null = null;
  private verificationStartTime: number | null = null;

  private initializeRecaptcha(): void {
    if (typeof (global as any).window !== 'undefined' && !this.recaptchaVerifier) {
      this.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
        size: 'invisible',
        callback: () => {
          console.log('reCAPTCHA solved');
        },
        'expired-callback': () => {
          console.log('reCAPTCHA expired');
          this.recaptchaVerifier = null;
        },
      });
    }
  }

  async sendVerificationCode(phoneNumber: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Sanitize phone number
      const sanitizedPhone = SecurePhoneValidator.sanitize(phoneNumber);

      // Validate phone number
      const validation = SecurePhoneValidator.validate(sanitizedPhone);
      if (!validation.isValid) {
        return { success: false, error: validation.error };
      }

      // Check rate limiting
      const rateLimitCheck = await RateLimitService.checkRateLimit(sanitizedPhone);
      if (!rateLimitCheck.allowed) {
        return { success: false, error: rateLimitCheck.error };
      }

      // Initialize reCAPTCHA
      this.initializeRecaptcha();
      if (!this.recaptchaVerifier) {
        return { success: false, error: 'Security verification failed. Please try again.' };
      }

      // Send verification code
      this.confirmationResult = await signInWithPhoneNumber(
        auth,
        sanitizedPhone,
        this.recaptchaVerifier
      );

      this.verificationStartTime = Date.now();

      // Log security event (without sensitive data)
      console.log('📱 Verification code sent successfully');

      return { success: true };
    } catch (error: any) {
      console.error('❌ Error sending verification code:', error);
      
      // Reset reCAPTCHA on error
      this.recaptchaVerifier = null;
      
      // Return generic error message to prevent information leakage
      return {
        success: false,
        error: 'Failed to send verification code. Please try again.',
      };
    }
  }

  async verifyCode(code: string): Promise<{ success: boolean; user?: FirebaseUser; error?: string }> {
    try {
      // Check if verification is in progress
      if (!this.confirmationResult) {
        return {
          success: false,
          error: 'No verification in progress. Please request a new code.',
        };
      }

      // Check code expiry
      if (this.verificationStartTime) {
        const elapsed = Date.now() - this.verificationStartTime;
        if (elapsed > SECURITY_CONFIG.CODE_EXPIRY_MINUTES * 60 * 1000) {
          this.cleanup();
          return {
            success: false,
            error: 'Verification code has expired. Please request a new code.',
          };
        }
      }

      // Validate code format
      if (!/^\d{6}$/.test(code)) {
        return {
          success: false,
          error: 'Invalid verification code format.',
        };
      }

      // Verify code
      const result = await this.confirmationResult.confirm(code);
      const user = result.user;

      if (user) {
        // Create or update user profile securely
        await this.createSecureUserProfile(user);
        
        // Clean up
        this.cleanup();

        console.log('✅ Phone authentication successful');
        return { success: true, user };
      } else {
        return { success: false, error: 'Authentication failed.' };
      }
    } catch (error: any) {
      console.error('❌ Error verifying code:', error);
      
      // Return generic error message
      return {
        success: false,
        error: 'Invalid verification code. Please try again.',
      };
    }
  }

  private async createSecureUserProfile(user: FirebaseUser): Promise<void> {
    try {
      const userRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        // Create new user profile with security defaults
        await setDoc(userRef, {
          uid: user.uid,
          phoneNumber: user.phoneNumber,
          displayName: user.displayName || 'IraChat User',
          photoURL: user.photoURL || '',
          isOnline: true,
          lastSeen: serverTimestamp(),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          isPublic: false, // Default to private
          isVerified: true,
          authMethod: 'phone',
          securityLevel: 'standard',
        });

        console.log('✅ User profile created successfully');
      } else {
        // Update existing user
        await setDoc(userRef, {
          lastSeen: serverTimestamp(),
          updatedAt: serverTimestamp(),
          isOnline: true,
        }, { merge: true });

        console.log('✅ User profile updated successfully');
      }
    } catch (error) {
      console.error('❌ Error creating/updating user profile:', error);
      throw error;
    }
  }

  cleanup(): void {
    if (this.recaptchaVerifier) {
      this.recaptchaVerifier.clear();
      this.recaptchaVerifier = null;
    }
    this.confirmationResult = null;
    this.verificationStartTime = null;
  }
}

// Export singleton instance
export const securePhoneAuth = new SecurePhoneAuthService();
export default securePhoneAuth;

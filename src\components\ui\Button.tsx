/**
 * Beautiful Button Component for IraChat
 * Responsive design with animations and sky blue branding
 */

import React, { useRef } from "react";
import {
  TouchableOpacity,
  Text,
  Animated,
  ActivityIndicator,
  View,
  GestureResponderEvent,
  ViewStyle,
  TextStyle,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { IRACHAT_COLORS, ANIMATIONS } from "../../styles/iraChatDesignSystem";
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing } from "../../utils/responsiveUtils";

type ButtonVariant = "primary" | "secondary" | "outline" | "ghost" | "danger" | "gradient";
type ButtonSize = "small" | "medium" | "large";

interface ButtonProps {
  title: string;
  onPress: (_event: GestureResponderEvent) => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: "left" | "right";
  fullWidth?: boolean;
  animated?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export default function Button({
  title,
  onPress,
  variant = "primary",
  size = "medium",
  disabled = false,
  loading = false,
  icon,
  iconPosition = "left",
  fullWidth = false,
  animated = true,
  style: _style,
  textStyle: _textStyle,
}: ButtonProps): React.JSX.Element {
  // Beautiful animation refs
  const scaleAnimation = useRef(new Animated.Value(1)).current;
  const opacityAnimation = useRef(new Animated.Value(1)).current;
  const rotateAnimation = useRef(new Animated.Value(0)).current;

  const handlePressIn = () => {
    if (animated && !disabled) {
      Animated.parallel([
        Animated.spring(scaleAnimation, {
          toValue: 0.95,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnimation, {
          toValue: 0.8,
          duration: ANIMATIONS.fast,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const handlePressOut = () => {
    if (animated && !disabled) {
      Animated.parallel([
        Animated.spring(scaleAnimation, {
          toValue: 1,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnimation, {
          toValue: 1,
          duration: ANIMATIONS.fast,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  // Loading animation
  React.useEffect(() => {
    if (loading) {
      Animated.loop(
        Animated.timing(rotateAnimation, {
          toValue: 1,
          duration: ANIMATIONS.slow * 2,
          useNativeDriver: true,
        })
      ).start();
    } else {
      rotateAnimation.setValue(0);
    }
  }, [loading, rotateAnimation]);

  const getVariantStyles = () => {
    switch (variant) {
      case "primary":
        return {
          backgroundColor: disabled ? IRACHAT_COLORS.textMuted : IRACHAT_COLORS.primary,
          borderColor: disabled ? IRACHAT_COLORS.textMuted : IRACHAT_COLORS.primary,
          textColor: IRACHAT_COLORS.textOnPrimary,
        };
      case "secondary":
        return {
          backgroundColor: disabled ? IRACHAT_COLORS.backgroundDark : IRACHAT_COLORS.surface,
          borderColor: disabled ? IRACHAT_COLORS.textMuted : IRACHAT_COLORS.primary,
          textColor: disabled ? IRACHAT_COLORS.textMuted : IRACHAT_COLORS.primary,
        };
      case "outline":
        return {
          backgroundColor: "transparent",
          borderColor: disabled ? IRACHAT_COLORS.textMuted : IRACHAT_COLORS.primary,
          textColor: disabled ? IRACHAT_COLORS.textMuted : IRACHAT_COLORS.primary,
        };
      case "ghost":
        return {
          backgroundColor: "transparent",
          borderColor: "transparent",
          textColor: disabled ? IRACHAT_COLORS.textMuted : IRACHAT_COLORS.primary,
        };
      case "danger":
        return {
          backgroundColor: disabled ? IRACHAT_COLORS.textMuted : IRACHAT_COLORS.error,
          borderColor: disabled ? IRACHAT_COLORS.textMuted : IRACHAT_COLORS.error,
          textColor: IRACHAT_COLORS.textOnPrimary,
        };
      case "gradient":
        return {
          backgroundColor: "transparent",
          borderColor: "transparent",
          textColor: IRACHAT_COLORS.textOnPrimary,
        };
      default:
        return {
          backgroundColor: IRACHAT_COLORS.primary,
          borderColor: IRACHAT_COLORS.primary,
          textColor: IRACHAT_COLORS.textOnPrimary,
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case "small":
        return {
          paddingVertical: ResponsiveSpacing.sm,
          paddingHorizontal: ResponsiveSpacing.md,
          fontSize: ResponsiveTypography.fontSize.sm,
          iconSize: ResponsiveScale.iconSize(16),
          minHeight: ComponentSizes.buttonHeight.small,
        };
      case "large":
        return {
          paddingVertical: ResponsiveSpacing.lg,
          paddingHorizontal: ResponsiveSpacing.xl,
          fontSize: ResponsiveTypography.fontSize.lg,
          iconSize: ResponsiveScale.iconSize(24),
          minHeight: ComponentSizes.buttonHeight.large,
        };
      default:
        return {
          paddingVertical: ResponsiveSpacing.md,
          paddingHorizontal: ResponsiveSpacing.lg,
          fontSize: ResponsiveTypography.fontSize.base,
          iconSize: ResponsiveScale.iconSize(20),
          minHeight: ComponentSizes.buttonHeight.medium,
        };
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();
  const isDisabled = disabled || loading;

  const renderIcon = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={variantStyles.textColor}
          style={{
            marginRight: iconPosition === "left" ? 8 : 0,
            marginLeft: iconPosition === "right" ? 8 : 0,
          }}
        />
      );
    }

    if (icon) {
      return (
        <Ionicons
          name={icon}
          size={sizeStyles.iconSize}
          color={variantStyles.textColor}
          style={{
            marginRight: iconPosition === "left" ? 8 : 0,
            marginLeft: iconPosition === "right" ? 8 : 0,
          }}
        />
      );
    }

    return null;
  };

  return (
    <Animated.View
      style={{
        transform: [{ scale: scaleAnimation }],
        width: fullWidth ? "100%" : "auto",
      }}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={isDisabled}
        activeOpacity={0.8}
        // className removed - using style instead
        style={{
          backgroundColor: variantStyles.backgroundColor,
          borderColor: variantStyles.borderColor,
          paddingVertical: sizeStyles.paddingVertical,
          paddingHorizontal: sizeStyles.paddingHorizontal,
          opacity: isDisabled ? 0.6 : 1,
        }}
      >
        <View className="flex-row items-center">
          {iconPosition === "left" && renderIcon()}
          <Text
            className="text-center"
            style={{
              color: variantStyles.textColor,
              fontSize: sizeStyles.fontSize,
              fontWeight: "600",
            }}
          >
            {title}
          </Text>
          {iconPosition === "right" && renderIcon()}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

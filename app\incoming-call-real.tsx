// 🔥 REAL INCOMING CALL SCREEN ROUTE - COMPLETE WEBRTC IMPLEMENTATION
// No mockups, no fake data - 100% real WebRTC incoming call handling

import React from 'react';
import { StyleSheet } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { RealCallScreen } from '../src/screens/RealCallScreen';
import { CallErrorBoundary } from '../src/components/CallErrorBoundary';

export default function IncomingCallRealRoute() {
  const params = useLocalSearchParams();

  return (
    <CallErrorBoundary>
      <RealCallScreen
        callId={params.callId as string}
        isOutgoing={false}
        contactId={params.callerId as string}
        contactName={params.callerName as string}
        callType={params.callType as 'voice' | 'video'}
      />
    </CallErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
});

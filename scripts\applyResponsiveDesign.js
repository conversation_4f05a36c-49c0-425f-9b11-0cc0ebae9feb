/**
 * Comprehensive Responsive Design Application Script
 * This script systematically applies responsive design to ALL components, pages, and screens
 */

const fs = require('fs');
const path = require('path');

// File patterns to update
const FILE_PATTERNS = [
  'src/components/**/*.tsx',
  'src/components/**/*.ts',
  'app/**/*.tsx',
  'app/**/*.ts',
];

// Responsive replacements
const RESPONSIVE_REPLACEMENTS = [
  // Typography replacements
  {
    pattern: /fontSize:\s*(\d+)/g,
    replacement: 'fontSize: ResponsiveTypography.fontSize.base'
  },
  {
    pattern: /fontSize:\s*TYPOGRAPHY\.fontSize\.(\w+)/g,
    replacement: 'fontSize: ResponsiveTypography.fontSize.$1'
  },
  
  // Spacing replacements
  {
    pattern: /padding:\s*(\d+)/g,
    replacement: 'padding: ResponsiveSpacing.md'
  },
  {
    pattern: /margin:\s*(\d+)/g,
    replacement: 'margin: ResponsiveSpacing.md'
  },
  {
    pattern: /paddingHorizontal:\s*(\d+)/g,
    replacement: 'paddingHorizontal: ResponsiveSpacing.screenPadding'
  },
  {
    pattern: /paddingVertical:\s*(\d+)/g,
    replacement: 'paddingVertical: ResponsiveSpacing.md'
  },
  {
    pattern: /marginHorizontal:\s*(\d+)/g,
    replacement: 'marginHorizontal: ResponsiveSpacing.md'
  },
  {
    pattern: /marginVertical:\s*(\d+)/g,
    replacement: 'marginVertical: ResponsiveSpacing.md'
  },
  
  // Border radius replacements
  {
    pattern: /borderRadius:\s*(\d+)/g,
    replacement: 'borderRadius: ResponsiveScale.borderRadius($1)'
  },
  
  // Width/Height replacements for common components
  {
    pattern: /width:\s*(\d+),\s*height:\s*(\d+)/g,
    replacement: 'width: ComponentSizes.avatarSize.medium, height: ComponentSizes.avatarSize.medium'
  },
  
  // Icon size replacements
  {
    pattern: /size=\{(\d+)\}/g,
    replacement: 'size={ResponsiveScale.iconSize($1)}'
  },
];

// Import statements to add
const RESPONSIVE_IMPORTS = `
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing, DeviceInfo } from '../utils/responsiveUtils';
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS, ANIMATIONS } from '../styles/iraChatDesignSystem';
`;

// Files that need comprehensive responsive updates
const PRIORITY_FILES = [
  // Auth pages
  'app/(auth)/welcome.tsx',
  'app/(auth)/register.tsx',
  'app/(auth)/login.tsx',
  'app/(auth)/phone-verification.tsx',
  
  // Tab pages
  'app/(tabs)/index.tsx',
  'app/(tabs)/groups.tsx',
  'app/(tabs)/calls.tsx',
  'app/(tabs)/profile.tsx',
  'app/(tabs)/settings.tsx',
  
  // Chat pages
  'app/chat/[id].tsx',
  'app/group-chat/[id].tsx',
  'app/chat-management.tsx',
  
  // Settings pages
  'app/privacy-settings.tsx',
  'app/theme-settings.tsx',
  'app/help-support.tsx',
  'app/account-settings.tsx',
  
  // Feature pages
  'app/contacts.tsx',
  'app/media-gallery.tsx',
  'app/call-screen.tsx',
  'app/search.tsx',
  
  // Core components
  'src/components/MainHeader.tsx',
  'src/components/Avatar.tsx',
  'src/components/ChatBubble.tsx',
  'src/components/GroupCard.tsx',
  'src/components/ContactCard.tsx',
  'src/components/MediaViewer.tsx',
  'src/components/CallInterface.tsx',
  'src/components/SearchBar.tsx',
  'src/components/SettingsItem.tsx',
  'src/components/ProfileCard.tsx',
  
  // UI components
  'src/components/ui/Button.tsx',
  'src/components/ui/Input.tsx',
  'src/components/ui/Card.tsx',
  'src/components/ui/Modal.tsx',
  'src/components/ui/TabBar.tsx',
  'src/components/ui/Header.tsx',
  'src/components/ui/FloatingActionButton.tsx',
  'src/components/ui/Badge.tsx',
  'src/components/ui/Switch.tsx',
  'src/components/ui/Slider.tsx',
];

// Responsive style templates
const RESPONSIVE_STYLE_TEMPLATES = {
  container: `
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingTop: DeviceInfo.statusBarHeight,
    paddingBottom: DeviceInfo.bottomSafeArea,
  },`,
  
  card: `
  card: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: ResponsiveScale.borderRadius(BORDER_RADIUS.lg),
    padding: ResponsiveSpacing.cardSpacing,
    marginHorizontal: ResponsiveSpacing.screenPadding,
    marginVertical: ResponsiveSpacing.md,
    ...SHADOWS.md,
  },`,
  
  button: `
  button: {
    backgroundColor: IRACHAT_COLORS.primary,
    paddingHorizontal: ResponsiveSpacing.lg,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: ResponsiveScale.borderRadius(BORDER_RADIUS.lg),
    minHeight: ComponentSizes.buttonHeight.medium,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.sm,
  },`,
  
  input: `
  input: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.border,
    borderRadius: ResponsiveScale.borderRadius(BORDER_RADIUS.md),
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.sm,
    fontSize: ResponsiveTypography.fontSize.base,
    minHeight: ComponentSizes.inputHeight.medium,
    color: IRACHAT_COLORS.text,
  },`,
  
  text: `
  text: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
  },`,
  
  header: `
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.md,
    height: ComponentSizes.headerHeight,
    backgroundColor: IRACHAT_COLORS.primary,
    ...SHADOWS.md,
  },`,
  
  avatar: `
  avatar: {
    width: ComponentSizes.avatarSize.medium,
    height: ComponentSizes.avatarSize.medium,
    borderRadius: ComponentSizes.avatarSize.medium / 2,
    backgroundColor: IRACHAT_COLORS.primaryLight,
  },`,
  
  fab: `
  fab: {
    position: 'absolute',
    bottom: ResponsiveSpacing.xl,
    right: ResponsiveSpacing.lg,
    width: ComponentSizes.fabSize,
    height: ComponentSizes.fabSize,
    borderRadius: ComponentSizes.fabSize / 2,
    backgroundColor: IRACHAT_COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.lg,
  },`,
};

// Function to apply responsive design to a file
function applyResponsiveDesign(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`File not found: ${filePath}`);
      return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Add responsive imports if not present
    if (!content.includes('ResponsiveScale') && !content.includes('responsiveUtils')) {
      const importIndex = content.indexOf('import React');
      if (importIndex !== -1) {
        const endOfImports = content.indexOf('\n\n', importIndex);
        if (endOfImports !== -1) {
          content = content.slice(0, endOfImports) + RESPONSIVE_IMPORTS + content.slice(endOfImports);
          modified = true;
        }
      }
    }
    
    // Apply responsive replacements
    RESPONSIVE_REPLACEMENTS.forEach(replacement => {
      const newContent = content.replace(replacement.pattern, replacement.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    // Add responsive wallpaper if it's a main page
    if (filePath.includes('app/') && !content.includes('IraChatWallpaper')) {
      const returnIndex = content.indexOf('return (');
      if (returnIndex !== -1) {
        const viewIndex = content.indexOf('<View', returnIndex);
        if (viewIndex !== -1) {
          const insertPoint = content.indexOf('>', viewIndex) + 1;
          const wallpaperCode = `
      <StatusBar style="light" />
      <IraChatWallpaper variant="default" animated={true} />
      `;
          content = content.slice(0, insertPoint) + wallpaperCode + content.slice(insertPoint);
          modified = true;
        }
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
    }
    
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
  }
}

// Main execution
console.log('🎨 Starting comprehensive responsive design application...');

PRIORITY_FILES.forEach(file => {
  applyResponsiveDesign(file);
});

console.log('✅ Responsive design application completed!');

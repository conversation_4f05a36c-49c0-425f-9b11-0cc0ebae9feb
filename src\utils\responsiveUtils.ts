/**
 * Responsive Utilities for IraChat
 * Comprehensive responsive helpers for all mobile devices
 */

import { Dimensions, Platform, PixelRatio } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Device Detection
export const DeviceInfo = {
  screenWidth,
  screenHeight,
  pixelRatio: PixelRatio.get(),
  fontScale: PixelRatio.getFontScale(),

  // Device Categories
  isSmallPhone: screenWidth < 360,
  isMediumPhone: screenWidth >= 360 && screenWidth < 390,
  isLargePhone: screenWidth >= 390 && screenWidth < 430,
  isExtraLargePhone: screenWidth >= 430 && screenWidth < 768,
  isTablet: screenWidth >= 768,

  // Platform Detection
  isIOS: Platform.OS === 'ios',
  isAndroid: Platform.OS === 'android',

  // Screen Orientation
  isLandscape: screenWidth > screenHeight,
  isPortrait: screenHeight > screenWidth,

  // Safe Area Detection (iOS)
  hasNotch: Platform.OS === 'ios' && screenHeight >= 812,
  statusBarHeight: Platform.OS === 'ios' ? (screenHeight >= 812 ? 44 : 20) : 0,
  bottomSafeArea: Platform.OS === 'ios' ? (screenHeight >= 812 ? 34 : 0) : 0,

  // Breakpoints
  breakpoints: {
    small: 360,
    large: 430,
    tablet: 768,
  },
};

// Responsive Scaling Functions
export const ResponsiveScale = {
  // Width percentage
  wp: (percentage: number): number => (screenWidth * percentage) / 100,
  
  // Height percentage
  hp: (percentage: number): number => (screenHeight * percentage) / 100,
  
  // Font scaling based on device size
  fontScale: (size: number): number => {
    const scale = DeviceInfo.isSmallPhone ? 0.9 : 
                  DeviceInfo.isLargePhone ? 1.1 : 
                  DeviceInfo.isTablet ? 1.2 : 1.0;
    return Math.round(size * scale);
  },
  
  // Spacing scaling
  spacing: (size: number): number => {
    const scale = DeviceInfo.isSmallPhone ? 0.8 : 
                  DeviceInfo.isLargePhone ? 1.1 : 
                  DeviceInfo.isTablet ? 1.3 : 1.0;
    return Math.round(size * scale);
  },
  
  // Border radius scaling
  borderRadius: (size: number): number => {
    const scale = DeviceInfo.isSmallPhone ? 0.8 : 
                  DeviceInfo.isLargePhone ? 1.1 : 
                  DeviceInfo.isTablet ? 1.2 : 1.0;
    return Math.round(size * scale);
  },
  
  // Icon size scaling
  iconSize: (size: number): number => {
    const scale = DeviceInfo.isSmallPhone ? 0.9 : 
                  DeviceInfo.isLargePhone ? 1.1 : 
                  DeviceInfo.isTablet ? 1.3 : 1.0;
    return Math.round(size * scale);
  },
};

// Responsive Breakpoints
export const Breakpoints = {
  xs: 320,  // Extra small phones
  sm: 360,  // Small phones
  md: 390,  // Medium phones
  lg: 430,  // Large phones
  xl: 768,  // Tablets
  xxl: 1024, // Large tablets
  
  // Breakpoint helpers
  isXS: () => screenWidth < 360,
  isSM: () => screenWidth >= 360 && screenWidth < 390,
  isMD: () => screenWidth >= 390 && screenWidth < 430,
  isLG: () => screenWidth >= 430 && screenWidth < 768,
  isXL: () => screenWidth >= 768 && screenWidth < 1024,
  isXXL: () => screenWidth >= 1024,
};

// Component Sizing Helpers
export const ComponentSizes = {
  // Button heights
  buttonHeight: {
    small: DeviceInfo.isSmallPhone ? 36 : DeviceInfo.isMediumPhone ? 40 : 44,
    medium: DeviceInfo.isSmallPhone ? 44 : DeviceInfo.isMediumPhone ? 48 : 52,
    large: DeviceInfo.isSmallPhone ? 52 : DeviceInfo.isMediumPhone ? 56 : 60,
  },
  
  // Input heights
  inputHeight: {
    small: DeviceInfo.isSmallPhone ? 36 : DeviceInfo.isMediumPhone ? 40 : 44,
    medium: DeviceInfo.isSmallPhone ? 44 : DeviceInfo.isMediumPhone ? 48 : 52,
    large: DeviceInfo.isSmallPhone ? 52 : DeviceInfo.isMediumPhone ? 56 : 60,
  },
  
  // Avatar sizes
  avatarSize: {
    small: DeviceInfo.isSmallPhone ? 32 : DeviceInfo.isMediumPhone ? 36 : 40,
    medium: DeviceInfo.isSmallPhone ? 48 : DeviceInfo.isMediumPhone ? 52 : 56,
    large: DeviceInfo.isSmallPhone ? 64 : DeviceInfo.isMediumPhone ? 72 : 80,
    xlarge: DeviceInfo.isSmallPhone ? 96 : DeviceInfo.isMediumPhone ? 108 : 120,
  },
  
  // Card dimensions
  cardPadding: DeviceInfo.isSmallPhone ? 12 : DeviceInfo.isMediumPhone ? 16 : 20,
  cardMargin: DeviceInfo.isSmallPhone ? 8 : DeviceInfo.isMediumPhone ? 12 : 16,
  cardBorderRadius: DeviceInfo.isSmallPhone ? 8 : DeviceInfo.isMediumPhone ? 12 : 16,
  
  // Header heights
  headerHeight: DeviceInfo.isSmallPhone ? 56 : DeviceInfo.isMediumPhone ? 64 : 72,
  tabBarHeight: DeviceInfo.isSmallPhone ? 60 : DeviceInfo.isMediumPhone ? 65 : 70,
  
  // FAB sizes
  fabSize: DeviceInfo.isSmallPhone ? 52 : DeviceInfo.isMediumPhone ? 56 : 64,
  fabIconSize: DeviceInfo.isSmallPhone ? 24 : DeviceInfo.isMediumPhone ? 28 : 32,
};

// Typography Helpers
export const ResponsiveTypography = {
  // Font sizes
  fontSize: {
    xs: ResponsiveScale.fontScale(12),
    sm: ResponsiveScale.fontScale(14),
    base: ResponsiveScale.fontScale(16),
    lg: ResponsiveScale.fontScale(18),
    xl: ResponsiveScale.fontScale(20),
    '2xl': ResponsiveScale.fontScale(24),
    '3xl': ResponsiveScale.fontScale(30),
    '4xl': ResponsiveScale.fontScale(36),
    '5xl': ResponsiveScale.fontScale(48),
  },
  
  // Line heights
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
  
  // Letter spacing
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    wider: 1,
  },
};

// Spacing Helpers
export const ResponsiveSpacing = {
  // Base spacing
  xs: ResponsiveScale.spacing(4),
  sm: ResponsiveScale.spacing(8),
  md: ResponsiveScale.spacing(16),
  lg: ResponsiveScale.spacing(24),
  xl: ResponsiveScale.spacing(32),
  '2xl': ResponsiveScale.spacing(48),
  '3xl': ResponsiveScale.spacing(64),
  '4xl': ResponsiveScale.spacing(96),

  // Contextual spacing
  screenPadding: DeviceInfo.isSmallPhone ? 12 : DeviceInfo.isMediumPhone ? 16 : 20,
  cardSpacing: DeviceInfo.isSmallPhone ? 8 : DeviceInfo.isMediumPhone ? 12 : 16,
  sectionSpacing: DeviceInfo.isSmallPhone ? 16 : DeviceInfo.isMediumPhone ? 24 : 32,

  // Additional spacing properties
  containerPadding: DeviceInfo.isSmallPhone ? 12 : DeviceInfo.isMediumPhone ? 16 : 20,
  itemSpacing: DeviceInfo.isSmallPhone ? 8 : DeviceInfo.isMediumPhone ? 12 : 16,
  safeArea: DeviceInfo.isSmallPhone ? 8 : DeviceInfo.isMediumPhone ? 12 : 16,

  // Safe area spacing
  safeAreaTop: DeviceInfo.statusBarHeight,
  safeAreaBottom: DeviceInfo.bottomSafeArea,
  
  // Minimum touch targets (accessibility)
  minTouchTarget: 44,
};

// Animation Helpers
export const ResponsiveAnimations = {
  // Duration based on device performance
  duration: {
    fast: DeviceInfo.isSmallPhone ? 200 : 150,
    normal: DeviceInfo.isSmallPhone ? 400 : 300,
    slow: DeviceInfo.isSmallPhone ? 600 : 500,
  },
  
  // Easing curves
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
};

// Utility Functions
export const ResponsiveUtils = {
  // Get responsive value based on screen size
  getResponsiveValue: (values: {
    xs?: any;
    sm?: any;
    md?: any;
    lg?: any;
    xl?: any;
    xxl?: any;
  }) => {
    if (Breakpoints.isXS() && values.xs !== undefined) return values.xs;
    if (Breakpoints.isSM() && values.sm !== undefined) return values.sm;
    if (Breakpoints.isMD() && values.md !== undefined) return values.md;
    if (Breakpoints.isLG() && values.lg !== undefined) return values.lg;
    if (Breakpoints.isXL() && values.xl !== undefined) return values.xl;
    if (Breakpoints.isXXL() && values.xxl !== undefined) return values.xxl;
    
    // Fallback to the largest available value
    return values.xxl || values.xl || values.lg || values.md || values.sm || values.xs;
  },
  
  // Clamp value between min and max
  clamp: (value: number, min: number, max: number): number => {
    return Math.min(Math.max(value, min), max);
  },
  
  // Scale value based on screen density
  scale: (size: number): number => {
    return PixelRatio.roundToNearestPixel(size);
  },
  
  // Get optimal number of columns for grid layouts
  getOptimalColumns: (itemWidth: number, spacing: number = 16): number => {
    const availableWidth = screenWidth - (ResponsiveSpacing.screenPadding * 2);
    const columns = Math.floor(availableWidth / (itemWidth + spacing));
    return Math.max(1, columns);
  },
};

export default {
  DeviceInfo,
  ResponsiveScale,
  Breakpoints,
  ComponentSizes,
  ResponsiveTypography,
  ResponsiveSpacing,
  ResponsiveAnimations,
  ResponsiveUtils,
};

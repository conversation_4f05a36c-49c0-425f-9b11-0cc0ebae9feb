/**
 * COMPREHENSIVE GROUP TYPE DEFINITIONS
 * Complete type system for IraChat Groups functionality
 */

// Core Group Types
export type GroupType = 'public' | 'private' | 'secret';
export type GroupRole = 'owner' | 'admin' | 'member';
export type GroupPermission = 'send_messages' | 'add_members' | 'remove_members' | 'edit_info' | 'pin_messages' | 'delete_messages';

// Group Member Information
export interface GroupMember {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  role: GroupRole;
  joinedAt: Date;
  lastSeen?: Date;
  isOnline: boolean;
  permissions: GroupPermission[];
  invitedBy?: string; // User ID who invited this member
  customTitle?: string; // Custom title like "Founder", "Moderator"
}

// Group Settings
export interface GroupSettings {
  // Message Settings
  onlyAdminsCanSendMessages: boolean;
  onlyAdminsCanAddMembers: boolean;
  allowMemberInvites: boolean;
  requireApprovalToJoin: boolean;
  
  // Privacy Settings
  showMemberList: boolean;
  allowForwarding: boolean;
  allowScreenshots: boolean;
  
  // Notification Settings
  muteNotifications: boolean;
  mentionNotifications: boolean;
  
  // Message Features
  disappearingMessages: boolean;
  disappearingMessagesDuration: number; // in hours
  readReceipts: boolean;
  typingIndicators: boolean;
  
  // Media Settings
  allowPhotos: boolean;
  allowVideos: boolean;
  allowDocuments: boolean;
  allowVoiceMessages: boolean;
  allowStickers: boolean;
  allowGifs: boolean;
  
  // Advanced Settings
  slowMode: boolean;
  slowModeDelay: number; // in seconds
  maxMembers: number;
  autoDeleteMessages: boolean;
  autoDeleteDuration: number; // in days
}

// Group Statistics
export interface GroupStats {
  totalMembers: number;
  onlineMembers: number;
  totalMessages: number;
  messagesThisWeek: number;
  mediaShared: number;
  mostActiveMembers: {
    userId: string;
    userName: string;
    messageCount: number;
  }[];
  peakOnlineTime: {
    hour: number;
    count: number;
  };
  joinRate: number; // members joined this week
  leaveRate: number; // members left this week
}

// Group Invite
export interface GroupInvite {
  id: string;
  groupId: string;
  inviteCode: string;
  createdBy: string;
  createdAt: Date;
  expiresAt?: Date;
  maxUses?: number;
  currentUses: number;
  isActive: boolean;
  inviteLink: string;
}

// Group Event (for activity feed)
export interface GroupEvent {
  id: string;
  groupId: string;
  type: 'member_joined' | 'member_left' | 'member_promoted' | 'member_demoted' | 'settings_changed' | 'info_updated' | 'message_pinned' | 'message_deleted';
  userId: string;
  userName: string;
  targetUserId?: string; // For actions affecting other users
  targetUserName?: string;
  timestamp: Date;
  details?: Record<string, any>;
  isVisible: boolean;
}

// Main Group Interface
export interface Group {
  // Core Properties
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  coverImage?: string;
  
  // Group Type & Privacy
  type: GroupType;
  isVerified: boolean;
  
  // Ownership & Creation
  ownerId: string;
  ownerName: string;
  createdAt: Date;
  createdBy: string;
  
  // Members & Roles
  members: GroupMember[];
  memberCount: number;
  onlineCount: number;
  admins: string[]; // Array of user IDs with admin role
  
  // Settings & Permissions
  settings: GroupSettings;
  
  // Activity & Engagement
  lastActivity: Date;
  lastMessage?: {
    id: string;
    text: string;
    senderId: string;
    senderName: string;
    timestamp: Date;
    type: 'text' | 'image' | 'video' | 'audio' | 'document';
  };
  
  // Statistics
  stats: GroupStats;
  
  // Invites & Links
  invites: GroupInvite[];
  primaryInviteLink?: string;
  
  // Moderation
  pinnedMessages: string[]; // Array of message IDs
  mutedMembers: string[]; // Array of user IDs
  bannedMembers: string[]; // Array of user IDs
  
  // User-specific states
  currentUserRole?: GroupRole;
  currentUserPermissions?: GroupPermission[];
  isMuted: boolean;
  isPinned: boolean;
  unreadCount: number;
  mentionCount: number;
  
  // Features
  hasBot: boolean;
  botCommands?: string[];
  customEmojis?: string[];
  

  
  // Categories & Tags
  category?: string;
  tags: string[];
  
  // Verification & Trust
  trustScore: number; // 0-100
  reportCount: number;
  isReported: boolean;
  isFlagged: boolean;
}

// Group Creation Data
export interface CreateGroupData {
  name: string;
  description?: string;
  type: GroupType;
  avatar?: string;
  coverImage?: string;
  initialMembers?: string[]; // User IDs to invite
  settings?: Partial<GroupSettings>;
  category?: string;
  tags?: string[];
  location?: {
    latitude: number;
    longitude: number;
    address: string;
    radius: number;
  };
}

// Group Search Filters
export interface GroupSearchFilters {
  query?: string;
  type?: GroupType[];
  category?: string[];
  tags?: string[];
  memberCountRange?: {
    min: number;
    max: number;
  };
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // in km
  };
  isVerified?: boolean;
  hasRecentActivity?: boolean;
  sortBy?: 'relevance' | 'members' | 'activity' | 'created' | 'name';
  sortOrder?: 'asc' | 'desc';
}

// Group Search Results
export interface GroupSearchResult {
  groups: Group[];
  totalCount: number;
  hasMore: boolean;
  nextCursor?: string;
  filters: GroupSearchFilters;
}

// Group Analytics (for group owners/admins)
export interface GroupAnalytics {
  groupId: string;
  period: 'day' | 'week' | 'month' | 'year';
  
  // Member Analytics
  memberGrowth: { date: string; count: number }[];
  memberActivity: { date: string; activeMembers: number }[];
  memberRetention: number; // percentage
  
  // Message Analytics
  messageVolume: { date: string; count: number }[];
  messageTypes: { type: string; count: number; percentage: number }[];
  peakHours: { hour: number; messageCount: number }[];
  
  // Engagement Analytics
  averageSessionDuration: number; // in minutes
  messagesPerMember: number;
  mostActiveMembers: {
    userId: string;
    userName: string;
    messageCount: number;
    lastActive: Date;
  }[];
  
  // Content Analytics
  mediaShared: { type: string; count: number }[];
  popularHashtags: { tag: string; count: number }[];
  linkShares: number;
  
  // Geographic Analytics (if location enabled)
  membersByCountry?: { country: string; count: number }[];
  membersByCity?: { city: string; count: number }[];
}

// Group Notification Settings
export interface GroupNotificationSettings {
  groupId: string;
  userId: string;
  
  // General Notifications
  allMessages: boolean;
  mentionsOnly: boolean;
  adminMessages: boolean;
  
  // Sound & Vibration
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  customSound?: string;
  
  // Quiet Hours
  quietHoursEnabled: boolean;
  quietHoursStart?: string; // HH:MM format
  quietHoursEnd?: string; // HH:MM format
  
  // Advanced
  showPreview: boolean;
  showSenderName: boolean;
  groupInBadgeCount: boolean;
}

// Group Backup/Export
export interface GroupBackup {
  groupId: string;
  groupName: string;
  exportedAt: Date;
  exportedBy: string;
  
  // Data included
  includeMessages: boolean;
  includeMedia: boolean;
  includeMembers: boolean;
  includeSettings: boolean;
  
  // Date range
  fromDate?: Date;
  toDate?: Date;
  
  // Export format
  format: 'json' | 'csv' | 'html';
  fileSize: number; // in bytes
  downloadUrl: string;
  expiresAt: Date;
}

// Legacy compatibility
export interface RealGroup {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  members: string[];
  admins: string[];
  createdAt: Date;
  lastActivity: Date;
  settings: {
    onlyAdminsCanAddMembers: boolean;
    onlyAdminsCanSendMessages: boolean;
    allowMemberInvites: boolean;
    muteNotifications: boolean;
    disappearingMessages: boolean;
    readReceipts: boolean;
  };
  unreadCount: { [userId: string]: number };
  lastMessage?: string;
  isActive: boolean;
}

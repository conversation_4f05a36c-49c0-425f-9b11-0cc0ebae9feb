import { useState, useCallback } from 'react';

export interface ChatSelectionState {
  selectedChats: string[];
  isSelectionMode: boolean;
}

export interface ChatSelectionActions {
  handleChatSelection: (_chatId: string) => void;
  handleLongPress: (_chatId: string) => void;
  exitSelectionMode: () => void;
  selectAllChats: (_chatIds: string[]) => void;
  clearSelection: () => void;
}

export const useChatSelection = (): ChatSelectionState & ChatSelectionActions => {
  const [selectedChats, setSelectedChats] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  // Handle chat selection toggle
  const handleChatSelection = useCallback((chatId: string) => {
    setSelectedChats(prev =>
      prev.includes(chatId)
        ? prev.filter(id => id !== chatId)
        : [...prev, chatId]
    );
  }, []);

  // Handle long press to enter selection mode
  const handleLongPress = useCallback((chatId: string) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedChats([chatId]);
    }
  }, [isSelectionMode]);

  // Exit selection mode
  const exitSelectionMode = useCallback(() => {
    setIsSelectionMode(false);
    setSelectedChats([]);
  }, []);

  // Select all chats
  const selectAllChats = useCallback((chatIds: string[]) => {
    setSelectedChats(chatIds);
  }, []);

  // Clear selection
  const clearSelection = useCallback(() => {
    setSelectedChats([]);
  }, []);

  return {
    selectedChats,
    isSelectionMode,
    handleChatSelection,
    handleLongPress,
    exitSelectionMode,
    selectAllChats,
    clearSelection,
  };
};

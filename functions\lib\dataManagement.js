"use strict";
// 🔥 REAL DATA MANAGEMENT CLOUD FUNCTIONS
// No mockups, no fake data - 100% real data backup and management functionality
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.archiveOldChats = exports.cleanupDeletedData = exports.exportChatHistory = exports.backupUserData = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const db = admin.firestore();
exports.backupUserData = functions.https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { userId } = data;
        const targetUserId = userId || context.auth.uid;
        console.log('🔥 Backing up user data for:', targetUserId);
        const backup = {
            userId: targetUserId,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            data: {},
        };
        // Backup user profile
        const userDoc = await db.collection('users').doc(targetUserId).get();
        if (userDoc.exists) {
            backup.data.profile = userDoc.data();
        }
        // Backup user's chats
        const chatsQuery = await db.collection('individual_chats')
            .where('participants', 'array-contains', targetUserId)
            .get();
        backup.data.chats = [];
        for (const chatDoc of chatsQuery.docs) {
            const chatData = chatDoc.data();
            const chatBackup = Object.assign(Object.assign({ id: chatDoc.id }, chatData), { messages: [] });
            // Backup messages
            const messagesQuery = await db.collection('individual_chats')
                .doc(chatDoc.id)
                .collection('messages')
                .orderBy('timestamp', 'desc')
                .limit(1000) // Limit to last 1000 messages
                .get();
            chatBackup.messages = messagesQuery.docs.map(doc => (Object.assign({ id: doc.id }, doc.data())));
            backup.data.chats.push(chatBackup);
        }
        // Backup user settings
        const settingsQuery = await db.collection('chat_settings')
            .where('userId', '==', targetUserId)
            .get();
        backup.data.settings = settingsQuery.docs.map(doc => (Object.assign({ id: doc.id }, doc.data())));
        // Save backup
        const backupRef = await db.collection('user_backups').add(backup);
        console.log('✅ User data backup completed:', backupRef.id);
        return { backupId: backupRef.id, status: 'completed' };
    }
    catch (error) {
        console.error('❌ Error backing up user data:', error);
        throw new functions.https.HttpsError('internal', 'Backup failed');
    }
});
exports.exportChatHistory = functions.https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { chatId, format } = data;
        console.log('🔥 Exporting chat history:', chatId);
        // Verify user is participant
        const chatDoc = await db.collection('individual_chats').doc(chatId).get();
        if (!chatDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Chat not found');
        }
        const chatData = chatDoc.data();
        if (!(chatData === null || chatData === void 0 ? void 0 : chatData.participants.includes(context.auth.uid))) {
            throw new functions.https.HttpsError('permission-denied', 'Not a chat participant');
        }
        // Get all messages
        const messagesQuery = await db.collection('individual_chats')
            .doc(chatId)
            .collection('messages')
            .orderBy('timestamp', 'asc')
            .get();
        const messages = messagesQuery.docs.map(doc => {
            var _a, _b, _c;
            return (Object.assign(Object.assign({ id: doc.id }, doc.data()), { timestamp: ((_c = (_b = (_a = doc.data().timestamp) === null || _a === void 0 ? void 0 : _a.toDate) === null || _b === void 0 ? void 0 : _b.call(_a)) === null || _c === void 0 ? void 0 : _c.toISOString()) || null }));
        });
        let exportData;
        if (format === 'json') {
            exportData = {
                chatId,
                participants: chatData.participants,
                participantNames: chatData.participantNames,
                exportedAt: new Date().toISOString(),
                messageCount: messages.length,
                messages,
            };
        }
        else if (format === 'txt') {
            const lines = [`Chat Export - ${new Date().toISOString()}`, ''];
            messages.forEach((msg) => {
                const timestamp = msg.timestamp ? new Date(msg.timestamp).toLocaleString() : 'Unknown time';
                const sender = msg.senderName || 'Unknown';
                const content = msg.content || msg.text || `[${msg.type} message]`;
                lines.push(`[${timestamp}] ${sender}: ${content}`);
            });
            exportData = lines.join('\n');
        }
        else {
            throw new functions.https.HttpsError('invalid-argument', 'Unsupported format');
        }
        // Save export record
        await db.collection('chat_exports').add({
            chatId,
            userId: context.auth.uid,
            format,
            messageCount: messages.length,
            exportedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        console.log('✅ Chat history exported');
        return { data: exportData, messageCount: messages.length };
    }
    catch (error) {
        console.error('❌ Error exporting chat history:', error);
        throw new functions.https.HttpsError('internal', 'Export failed');
    }
});
exports.cleanupDeletedData = functions.pubsub
    .schedule('every 24 hours')
    .onRun(async (context) => {
    try {
        console.log('🔥 Cleaning up deleted data...');
        const sevenDaysAgo = admin.firestore.Timestamp.fromDate(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000));
        // Clean up deleted messages
        const deletedMessagesQuery = await db.collectionGroup('messages')
            .where('deleted', '==', true)
            .where('deletedAt', '<', sevenDaysAgo)
            .limit(100)
            .get();
        if (!deletedMessagesQuery.empty) {
            const batch = db.batch();
            deletedMessagesQuery.forEach(doc => {
                batch.delete(doc.ref);
            });
            await batch.commit();
            console.log('✅ Cleaned up', deletedMessagesQuery.size, 'deleted messages');
        }
        // Clean up old backups (keep for 30 days)
        const thirtyDaysAgo = admin.firestore.Timestamp.fromDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));
        const oldBackupsQuery = await db.collection('user_backups')
            .where('timestamp', '<', thirtyDaysAgo)
            .limit(50)
            .get();
        if (!oldBackupsQuery.empty) {
            const batch = db.batch();
            oldBackupsQuery.forEach(doc => {
                batch.delete(doc.ref);
            });
            await batch.commit();
            console.log('✅ Cleaned up', oldBackupsQuery.size, 'old backups');
        }
        return null;
    }
    catch (error) {
        console.error('❌ Error cleaning up deleted data:', error);
        throw error;
    }
});
exports.archiveOldChats = functions.pubsub
    .schedule('every 7 days')
    .onRun(async (context) => {
    try {
        console.log('🔥 Archiving old inactive chats...');
        const sixMonthsAgo = admin.firestore.Timestamp.fromDate(new Date(Date.now() - 180 * 24 * 60 * 60 * 1000));
        // Find inactive chats
        const inactiveChatsQuery = await db.collection('individual_chats')
            .where('lastMessageTime', '<', sixMonthsAgo)
            .where('archived', '!=', true)
            .limit(50)
            .get();
        if (!inactiveChatsQuery.empty) {
            const batch = db.batch();
            inactiveChatsQuery.forEach(doc => {
                batch.update(doc.ref, {
                    archived: true,
                    archivedAt: admin.firestore.FieldValue.serverTimestamp(),
                });
            });
            await batch.commit();
            console.log('✅ Archived', inactiveChatsQuery.size, 'inactive chats');
        }
        return null;
    }
    catch (error) {
        console.error('❌ Error archiving old chats:', error);
        throw error;
    }
});
//# sourceMappingURL=dataManagement.js.map
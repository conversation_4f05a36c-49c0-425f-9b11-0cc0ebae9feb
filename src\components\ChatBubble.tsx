/**
 * Beautiful Chat Bubble Component for IraChat
 * Responsive design with animations and sky blue branding
 */

import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { IRACHAT_COLORS, TYPOGRAPHY, SHADOWS, ANIMATIONS } from '../styles/iraChatDesignSystem';
import { ResponsiveScale, ResponsiveSpacing, ResponsiveTypography, DeviceInfo } from '../utils/responsiveUtils';

interface ChatBubbleProps {
  message: string;
  timestamp: string;
  isOwn: boolean;
  isRead?: boolean;
  isDelivered?: boolean;
  isSending?: boolean;
  onPress?: () => void;
  onLongPress?: () => void;
  animated?: boolean;
  index?: number;
  messageType?: 'text' | 'image' | 'video' | 'audio' | 'file';
  style?: ViewStyle;
}

export const ChatBubble: React.FC<ChatBubbleProps> = ({
  message,
  timestamp,
  isOwn,
  isRead = false,
  isDelivered = false,
  isSending = false,
  onPress,
  onLongPress,
  animated = true,
  index = 0,
  messageType = 'text',
  style,
}) => {
  // Beautiful animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(isOwn ? 50 : -50)).current;
  const scaleAnimation = useRef(new Animated.Value(1)).current;

  // Entrance animation with stagger
  useEffect(() => {
    if (animated) {
      Animated.sequence([
        Animated.delay(index * 100),
        Animated.parallel([
          Animated.timing(fadeAnimation, {
            toValue: 1,
            duration: ANIMATIONS.normal,
            useNativeDriver: true,
          }),
          Animated.spring(slideAnimation, {
            toValue: 0,
            tension: 50,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    } else {
      fadeAnimation.setValue(1);
      slideAnimation.setValue(0);
    }
  }, [animated, index]);

  // Press animations
  const handlePressIn = () => {
    if (animated && (onPress || onLongPress)) {
      Animated.spring(scaleAnimation, {
        toValue: 0.95,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animated && (onPress || onLongPress)) {
      Animated.spring(scaleAnimation, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    }
  };

  const getBubbleStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      maxWidth: DeviceInfo.screenWidth * 0.75,
      paddingHorizontal: ResponsiveSpacing.md,
      paddingVertical: ResponsiveSpacing.sm,
      borderRadius: ResponsiveScale.borderRadius(18),
      marginVertical: ResponsiveSpacing.xs,
      ...SHADOWS.sm,
    };

    if (isOwn) {
      return {
        ...baseStyle,
        backgroundColor: IRACHAT_COLORS.sentMessage,
        alignSelf: 'flex-end',
        marginLeft: ResponsiveSpacing.xl,
        borderBottomRightRadius: ResponsiveScale.borderRadius(4),
      };
    } else {
      return {
        ...baseStyle,
        backgroundColor: IRACHAT_COLORS.receivedMessage,
        alignSelf: 'flex-start',
        marginRight: ResponsiveSpacing.xl,
        borderBottomLeftRadius: ResponsiveScale.borderRadius(4),
      };
    }
  };

  const getTextColor = () => {
    return isOwn ? IRACHAT_COLORS.textOnPrimary : IRACHAT_COLORS.text;
  };

  const renderMessageStatus = () => {
    if (!isOwn) return null;

    let iconName: keyof typeof Ionicons.glyphMap = 'time';
    let iconColor = IRACHAT_COLORS.textOnPrimary;

    if (isSending) {
      iconName = 'time';
      iconColor = `${IRACHAT_COLORS.textOnPrimary}70`;
    } else if (isDelivered && !isRead) {
      iconName = 'checkmark';
      iconColor = IRACHAT_COLORS.textOnPrimary;
    } else if (isRead) {
      iconName = 'checkmark-done';
      iconColor = IRACHAT_COLORS.success;
    }

    return (
      <Ionicons
        name={iconName}
        size={ResponsiveScale.iconSize(12)}
        color={iconColor}
        style={styles.statusIcon}
      />
    );
  };

  const renderMessageTypeIcon = () => {
    if (messageType === 'text') return null;

    const iconMap = {
      image: 'image',
      video: 'videocam',
      audio: 'mic',
      file: 'document',
    };

    return (
      <Ionicons
        name={iconMap[messageType] as keyof typeof Ionicons.glyphMap}
        size={ResponsiveScale.iconSize(16)}
        color={getTextColor()}
        style={styles.typeIcon}
      />
    );
  };

  const animatedStyle = {
    opacity: fadeAnimation,
    transform: [
      { translateX: slideAnimation },
      { scale: scaleAnimation }
    ],
  };

  const containerStyle = [
    styles.container,
    { alignSelf: isOwn ? 'flex-end' as const : 'flex-start' as const },
    style,
  ];

  const bubbleStyle = getBubbleStyle();

  if (isOwn && messageType === 'text') {
    // Gradient bubble for own messages
    return (
      <Animated.View style={[containerStyle, animatedStyle]}>
        <TouchableOpacity
          onPress={onPress}
          onLongPress={onLongPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={1}
          disabled={!onPress && !onLongPress}
        >
          <LinearGradient
            colors={IRACHAT_COLORS.primaryGradient as any}
            style={bubbleStyle}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.messageContent}>
              {renderMessageTypeIcon()}
              <Text style={[styles.messageText, { color: getTextColor() }]}>
                {message}
              </Text>
            </View>
            <View style={styles.messageFooter}>
              <Text style={[styles.timestampText, { color: getTextColor() }]}>
                {timestamp}
              </Text>
              {renderMessageStatus()}
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[containerStyle, animatedStyle]}>
      <TouchableOpacity
        onPress={onPress}
        onLongPress={onLongPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
        disabled={!onPress && !onLongPress}
      >
        <View style={bubbleStyle}>
          <View style={styles.messageContent}>
            {renderMessageTypeIcon()}
            <Text style={[styles.messageText, { color: getTextColor() }]}>
              {message}
            </Text>
          </View>
          <View style={styles.messageFooter}>
            <Text style={[styles.timestampText, { color: getTextColor() }]}>
              {timestamp}
            </Text>
            {renderMessageStatus()}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: ResponsiveSpacing.xs,
  },
  messageContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  messageText: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontFamily: TYPOGRAPHY.fontFamily,
    lineHeight: ResponsiveTypography.fontSize.base * 1.4,
    flex: 1,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: ResponsiveSpacing.xs,
  },
  timestampText: {
    fontSize: ResponsiveTypography.fontSize.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
    opacity: 0.7,
  },
  statusIcon: {
    marginLeft: ResponsiveSpacing.xs,
  },
  typeIcon: {
    marginRight: ResponsiveSpacing.sm,
    marginTop: ResponsiveSpacing.xs / 2,
  },
});

export default ChatBubble;

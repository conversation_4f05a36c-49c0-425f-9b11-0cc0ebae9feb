/**
 * Beautiful Animated Input Component for IraChat
 * Sky blue branding with smooth focus animations
 */

import React, { useRef, useState } from 'react';
import {
  Animated,
  TextInput,
  View,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, ANIMATIONS } from '../../styles/iraChatDesignSystem';
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing } from '../../utils/responsiveUtils';

interface AnimatedInputProps extends TextInputProps {
  label?: string;
  placeholder?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  error?: string;
  success?: boolean;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
  animated?: boolean;
}

export const AnimatedInput: React.FC<AnimatedInputProps> = ({
  label,
  placeholder,
  icon,
  iconPosition = 'left',
  error,
  success = false,
  containerStyle,
  inputStyle,
  labelStyle,
  variant = 'default',
  size = 'medium',
  animated = true,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(false);
  
  const focusAnimation = useRef(new Animated.Value(0)).current;
  const labelAnimation = useRef(new Animated.Value(0)).current;
  const borderAnimation = useRef(new Animated.Value(0)).current;
  const shadowAnimation = useRef(new Animated.Value(0)).current;

  const handleFocus = () => {
    setIsFocused(true);
    if (animated) {
      Animated.parallel([
        Animated.timing(focusAnimation, {
          toValue: 1,
          duration: ANIMATIONS.normal,
          useNativeDriver: false,
        }),
        Animated.timing(labelAnimation, {
          toValue: 1,
          duration: ANIMATIONS.normal,
          useNativeDriver: false,
        }),
        Animated.timing(borderAnimation, {
          toValue: 1,
          duration: ANIMATIONS.normal,
          useNativeDriver: false,
        }),
        Animated.timing(shadowAnimation, {
          toValue: 1,
          duration: ANIMATIONS.normal,
          useNativeDriver: false,
        }),
      ]).start();
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    if (animated && !hasValue) {
      Animated.parallel([
        Animated.timing(focusAnimation, {
          toValue: 0,
          duration: ANIMATIONS.normal,
          useNativeDriver: false,
        }),
        Animated.timing(labelAnimation, {
          toValue: 0,
          duration: ANIMATIONS.normal,
          useNativeDriver: false,
        }),
        Animated.timing(borderAnimation, {
          toValue: 0,
          duration: ANIMATIONS.normal,
          useNativeDriver: false,
        }),
        Animated.timing(shadowAnimation, {
          toValue: 0,
          duration: ANIMATIONS.normal,
          useNativeDriver: false,
        }),
      ]).start();
    }
  };

  const handleChangeText = (text: string) => {
    setHasValue(text.length > 0);
    if (textInputProps.onChangeText) {
      textInputProps.onChangeText(text);
    }
  };

  const getBorderColor = () => {
    if (error) return IRACHAT_COLORS.error;
    if (success) return IRACHAT_COLORS.success;
    return borderAnimation.interpolate({
      inputRange: [0, 1],
      outputRange: [IRACHAT_COLORS.border, IRACHAT_COLORS.borderFocus],
    });
  };

  const getShadowOpacity = () => {
    return shadowAnimation.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 0.1],
    });
  };

  const getLabelStyle = (): Animated.AnimatedProps<TextStyle> => {
    const baseStyle: TextStyle = {
      position: 'absolute',
      left: icon && iconPosition === 'left' ? 40 : SPACING.md,
      fontFamily: TYPOGRAPHY.fontFamily,
      color: error ? IRACHAT_COLORS.error : IRACHAT_COLORS.textSecondary,
    };

    if (animated && label) {
      return {
        ...baseStyle,
        top: labelAnimation.interpolate({
          inputRange: [0, 1],
          outputRange: [size === 'large' ? 20 : size === 'small' ? 12 : 16, -8],
        }),
        fontSize: labelAnimation.interpolate({
          inputRange: [0, 1],
          outputRange: [
            size === 'large' ? TYPOGRAPHY.fontSize.lg : size === 'small' ? TYPOGRAPHY.fontSize.sm : TYPOGRAPHY.fontSize.base,
            TYPOGRAPHY.fontSize.xs
          ],
        }),
        backgroundColor: labelAnimation.interpolate({
          inputRange: [0, 1],
          outputRange: ['transparent', IRACHAT_COLORS.background],
        }),
        paddingHorizontal: labelAnimation.interpolate({
          inputRange: [0, 1],
          outputRange: [0, SPACING.xs],
        }),
      };
    }

    return baseStyle;
  };

  const getInputContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: ResponsiveScale.borderRadius(BORDER_RADIUS.lg),
      borderWidth: 1,
      minHeight: ResponsiveSpacing.minTouchTarget,
    };

    // Responsive size variations
    switch (size) {
      case 'small':
        baseStyle.paddingVertical = ResponsiveSpacing.sm;
        baseStyle.paddingHorizontal = ResponsiveSpacing.sm;
        baseStyle.minHeight = ComponentSizes.inputHeight.small;
        break;
      case 'large':
        baseStyle.paddingVertical = ResponsiveSpacing.lg;
        baseStyle.paddingHorizontal = ResponsiveSpacing.lg;
        baseStyle.minHeight = ComponentSizes.inputHeight.large;
        break;
      default:
        baseStyle.paddingVertical = ResponsiveSpacing.md;
        baseStyle.paddingHorizontal = ResponsiveSpacing.md;
        baseStyle.minHeight = ComponentSizes.inputHeight.medium;
    }

    // Variant styles
    switch (variant) {
      case 'outlined':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
        };
      case 'filled':
        return {
          ...baseStyle,
          backgroundColor: IRACHAT_COLORS.backgroundDark,
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: IRACHAT_COLORS.surface,
        };
    }
  };

  const getInputStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      flex: 1,
      fontFamily: TYPOGRAPHY.fontFamily,
      color: IRACHAT_COLORS.text,
      fontSize: size === 'large' ? ResponsiveTypography.fontSize.lg :
                size === 'small' ? ResponsiveTypography.fontSize.sm :
                ResponsiveTypography.fontSize.base,
    };

    if (icon) {
      if (iconPosition === 'left') {
        baseStyle.marginLeft = ResponsiveSpacing.sm;
      } else {
        baseStyle.marginRight = ResponsiveSpacing.sm;
      }
    }

    return baseStyle;
  };

  const renderIcon = () => {
    if (!icon) return null;

    const iconSize = ResponsiveScale.iconSize(size === 'small' ? 16 : size === 'large' ? 24 : 20);
    const iconColor = error ? IRACHAT_COLORS.error : success ? IRACHAT_COLORS.success : IRACHAT_COLORS.textSecondary;

    return (
      <Ionicons
        name={icon}
        size={iconSize}
        color={iconColor}
        style={iconPosition === 'right' ? styles.iconRight : styles.iconLeft}
      />
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Floating label */}
      {label && (
        <Animated.Text style={[getLabelStyle(), labelStyle]}>
          {label}
        </Animated.Text>
      )}
      
      {/* Input container */}
      <Animated.View
        style={[
          getInputContainerStyle(),
          {
            borderColor: getBorderColor(),
            shadowOpacity: getShadowOpacity(),
            shadowColor: IRACHAT_COLORS.primary,
            shadowOffset: { width: 0, height: 2 },
            shadowRadius: 4,
            elevation: shadowAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 4],
            }),
          },
        ]}
      >
        {icon && iconPosition === 'left' && renderIcon()}
        
        <TextInput
          {...textInputProps}
          style={[getInputStyle(), inputStyle]}
          placeholder={!label || (!isFocused && !hasValue) ? placeholder : ''}
          placeholderTextColor={IRACHAT_COLORS.textMuted}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChangeText={handleChangeText}
        />
        
        {icon && iconPosition === 'right' && renderIcon()}
      </Animated.View>
      
      {/* Error message */}
      {error && (
        <Text style={styles.errorText}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: ResponsiveSpacing.sm,
  },
  iconLeft: {
    marginRight: ResponsiveSpacing.sm,
  },
  iconRight: {
    marginLeft: ResponsiveSpacing.sm,
  },
  errorText: {
    marginTop: ResponsiveSpacing.xs,
    marginLeft: ResponsiveSpacing.sm,
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.error,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
});

export default AnimatedInput;

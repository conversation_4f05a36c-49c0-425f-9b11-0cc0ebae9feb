/**
 * Beautiful Empty State Component for IraChat
 * Responsive design with animations and sky blue branding
 */

import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { IRACHAT_COLORS, TYPOGRAPHY, SHADOWS, ANIMATIONS } from '../styles/iraChatDesignSystem';
import { ResponsiveScale, ResponsiveSpacing, ResponsiveTypography, DeviceInfo } from '../utils/responsiveUtils';

interface EmptyStateProps {
  icon?: keyof typeof Ionicons.glyphMap;
  title: string;
  description?: string;
  actionText?: string;
  onActionPress?: () => void;
  animated?: boolean;
  variant?: 'default' | 'gradient' | 'minimal';
  style?: any;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon = 'chatbubbles-outline',
  title,
  description,
  actionText,
  onActionPress,
  animated = true,
  variant = 'default',
  style,
}) => {
  // Beautiful animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const scaleAnimation = useRef(new Animated.Value(0.8)).current;
  const slideAnimation = useRef(new Animated.Value(30)).current;

  // Entrance animation
  useEffect(() => {
    if (animated) {
      Animated.sequence([
        Animated.delay(200),
        Animated.parallel([
          Animated.timing(fadeAnimation, {
            toValue: 1,
            duration: ANIMATIONS.normal,
            useNativeDriver: true,
          }),
          Animated.spring(scaleAnimation, {
            toValue: 1,
            tension: 50,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnimation, {
            toValue: 0,
            duration: ANIMATIONS.normal,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    } else {
      fadeAnimation.setValue(1);
      scaleAnimation.setValue(1);
      slideAnimation.setValue(0);
    }
  }, [animated]);

  const renderIcon = () => (
    <Animated.View
      style={[
        styles.iconContainer,
        {
          transform: [{ scale: scaleAnimation }],
        },
      ]}
    >
      {variant === 'gradient' ? (
        <LinearGradient
          colors={IRACHAT_COLORS.skyGradient as any}
          style={styles.iconGradientBackground}
        >
          <Ionicons
            name={icon}
            size={ResponsiveScale.iconSize(64)}
            color={IRACHAT_COLORS.textOnPrimary}
          />
        </LinearGradient>
      ) : (
        <View style={styles.iconBackground}>
          <Ionicons
            name={icon}
            size={ResponsiveScale.iconSize(64)}
            color={IRACHAT_COLORS.primary}
          />
        </View>
      )}
    </Animated.View>
  );

  const renderContent = () => (
    <Animated.View
      style={[
        styles.contentContainer,
        {
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        },
      ]}
    >
      <Text style={styles.title}>{title}</Text>
      
      {description && (
        <Text style={styles.description}>{description}</Text>
      )}
      
      {actionText && onActionPress && (
        <TouchableOpacity
          style={styles.actionButton}
          onPress={onActionPress}
          activeOpacity={0.8}
        >
          {variant === 'gradient' ? (
            <LinearGradient
              colors={IRACHAT_COLORS.primaryGradient as any}
              style={styles.actionButtonGradient}
            >
              <Text style={styles.actionButtonText}>{actionText}</Text>
            </LinearGradient>
          ) : (
            <View style={styles.actionButtonDefault}>
              <Text style={styles.actionButtonText}>{actionText}</Text>
            </View>
          )}
        </TouchableOpacity>
      )}
    </Animated.View>
  );

  const animatedStyle = {
    opacity: fadeAnimation,
  };

  return (
    <Animated.View style={[styles.container, animatedStyle, style]}>
      {renderIcon()}
      {renderContent()}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.xl,
  },
  iconContainer: {
    marginBottom: ResponsiveSpacing.xl,
  },
  iconBackground: {
    width: ResponsiveScale.spacing(120),
    height: ResponsiveScale.spacing(120),
    borderRadius: ResponsiveScale.spacing(60),
    backgroundColor: `${IRACHAT_COLORS.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  iconGradientBackground: {
    width: ResponsiveScale.spacing(120),
    height: ResponsiveScale.spacing(120),
    borderRadius: ResponsiveScale.spacing(60),
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.md,
  },
  contentContainer: {
    alignItems: 'center',
    maxWidth: DeviceInfo.screenWidth * 0.8,
  },
  title: {
    fontSize: ResponsiveTypography.fontSize.xl,
    fontWeight: '600' as const,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
    marginBottom: ResponsiveSpacing.md,
  },
  description: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
    lineHeight: ResponsiveTypography.fontSize.base * 1.5,
    marginBottom: ResponsiveSpacing.xl,
  },
  actionButton: {
    marginTop: ResponsiveSpacing.md,
  },
  actionButtonDefault: {
    backgroundColor: IRACHAT_COLORS.primary,
    paddingHorizontal: ResponsiveSpacing.xl,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: ResponsiveScale.borderRadius(25),
    ...SHADOWS.sm,
  },
  actionButtonGradient: {
    paddingHorizontal: ResponsiveSpacing.xl,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: ResponsiveScale.borderRadius(25),
    ...SHADOWS.sm,
  },
  actionButtonText: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: '500' as const,
    color: IRACHAT_COLORS.textOnPrimary,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
  },
});

export default EmptyState;

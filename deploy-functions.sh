#!/bin/bash

# 🔥 FIREBASE CLOUD FUNCTIONS DEPLOYMENT SCRIPT
# Deploy all IraChat Cloud Functions to Firebase

set -e

echo "🔥 Deploying IraChat Cloud Functions..."
echo "======================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found. Installing..."
    npm install -g firebase-tools
fi

# Check if logged in to Firebase
echo "🔐 Checking Firebase authentication..."
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Please login:"
    firebase login
fi

# Navigate to functions directory
cd functions

# Install dependencies
echo "📦 Installing Cloud Functions dependencies..."
npm install

# Build TypeScript
echo "🔨 Building TypeScript..."
npm run build

# Deploy functions
echo "🚀 Deploying Cloud Functions..."
firebase deploy --only functions

echo ""
echo "✅ Cloud Functions deployed successfully!"
echo ""
echo "📋 Deployed Functions:"
echo "  - Call Management:"
echo "    • initiateCall"
echo "    • answerCall" 
echo "    • declineCall"
echo "    • endCall"
echo "    • exchangeIceCandidate"
echo "  - Push Notifications:"
echo "    • sendPushNotification"
echo "    • handleNewMessage"
echo "  - Real-time Handlers:"
echo "    • setupRealTimeListeners"
echo "    • handleChatUpdate"
echo "  - And many more..."
echo ""
echo "🎯 Functions are now live and ready for use!"
echo "🔗 View in Firebase Console: https://console.firebase.google.com/"

// 🔥 REAL-TIME SIGNALING SERVICE - CO<PERSON>LETE WEBRTC SIGNALING
// No mockups, no fake data - 100% real WebRTC signaling through Firebase

import {
  doc,
  onSnapshot,
  updateDoc,
  serverTimestamp,
  collection,
  query,
  orderBy,
  Unsubscribe
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { RTCSessionDescription, RTCIceCandidate } from 'react-native-webrtc';

// Real Signaling Interfaces
interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'call-status';
  data: any;
  timestamp: Date;
  senderId: string;
  receiverId: string;
}

interface CallSignalingState {
  callId: string;
  offer?: RTCSessionDescription;
  answer?: RTCSessionDescription;
  iceCandidates: RTCIceCandidate[];
  status: string;
}

class RealTimeSignalingService {
  private signalingListeners: Map<string, Unsubscribe> = new Map();
  private callStateListeners: Map<string, ((_state: CallSignalingState) => void)[]> = new Map();

  // ==================== REAL SIGNALING SETUP ====================

  /**
   * Set up real-time signaling for a call
   */
  setupCallSignaling(
    callId: string,
    onSignalingMessage: (_message: SignalingMessage) => void
  ): () => void {
    try {
      console.log('🔥 Setting up real-time signaling for call:', callId);

      // Listen to call document changes
      const callDocRef = doc(db, 'calls', callId);
      const unsubscribe = onSnapshot(callDocRef, (snapshot) => {
        if (snapshot.exists()) {
          const callData = snapshot.data();
          console.log('🔥 Call signaling data updated:', callData);

          // Handle offer
          if (callData.offer && callData.offer !== null) {
            onSignalingMessage({
              type: 'offer',
              data: callData.offer,
              timestamp: new Date(),
              senderId: callData.callerId,
              receiverId: callData.receiverId,
            });
          }

          // Handle answer
          if (callData.answer && callData.answer !== null) {
            onSignalingMessage({
              type: 'answer',
              data: callData.answer,
              timestamp: new Date(),
              senderId: callData.receiverId,
              receiverId: callData.callerId,
            });
          }

          // Handle ICE candidates
          if (callData.iceCandidates && Array.isArray(callData.iceCandidates)) {
            callData.iceCandidates.forEach((candidate: any) => {
              onSignalingMessage({
                type: 'ice-candidate',
                data: candidate,
                timestamp: new Date(),
                senderId: candidate.senderId || callData.callerId,
                receiverId: candidate.receiverId || callData.receiverId,
              });
            });
          }

          // Handle status changes
          if (callData.status) {
            onSignalingMessage({
              type: 'call-status',
              data: { status: callData.status },
              timestamp: new Date(),
              senderId: callData.callerId,
              receiverId: callData.receiverId,
            });
          }
        }
      }, (error) => {
        console.error('❌ Error in call signaling listener:', error);
      });

      this.signalingListeners.set(callId, unsubscribe);

      console.log('✅ Real-time signaling set up successfully');
      return () => {
        unsubscribe();
        this.signalingListeners.delete(callId);
        console.log('🧹 Signaling listener cleaned up for call:', callId);
      };
    } catch (error) {
      console.error('❌ Error setting up call signaling:', error);
      return () => {};
    }
  }

  // ==================== REAL OFFER/ANSWER EXCHANGE ====================

  /**
   * Send real WebRTC offer
   */
  async sendOffer(callId: string, offer: RTCSessionDescription): Promise<void> {
    try {
      console.log('🔥 Sending real WebRTC offer:', callId);

      const callDocRef = doc(db, 'calls', callId);
      await updateDoc(callDocRef, {
        offer: {
          type: offer.type,
          sdp: offer.sdp,
        },
        status: 'ringing',
        updatedAt: serverTimestamp(),
      });

      console.log('✅ WebRTC offer sent successfully');
    } catch (error) {
      console.error('❌ Error sending offer:', error);
      throw error;
    }
  }

  /**
   * Send real WebRTC answer
   */
  async sendAnswer(callId: string, answer: RTCSessionDescription): Promise<void> {
    try {
      console.log('🔥 Sending real WebRTC answer:', callId);

      const callDocRef = doc(db, 'calls', callId);
      await updateDoc(callDocRef, {
        answer: {
          type: answer.type,
          sdp: answer.sdp,
        },
        status: 'connecting',
        updatedAt: serverTimestamp(),
      });

      console.log('✅ WebRTC answer sent successfully');
    } catch (error) {
      console.error('❌ Error sending answer:', error);
      throw error;
    }
  }

  // ==================== REAL ICE CANDIDATE EXCHANGE ====================

  /**
   * Send real ICE candidate
   */
  async sendIceCandidate(
    callId: string, 
    candidate: RTCIceCandidate,
    senderId: string,
    receiverId: string
  ): Promise<void> {
    try {
      console.log('🔥 Sending real ICE candidate:', callId);

      const candidateData = {
        candidate: candidate.candidate,
        sdpMLineIndex: candidate.sdpMLineIndex,
        sdpMid: candidate.sdpMid,
        senderId,
        receiverId,
        timestamp: Date.now(),
      };

      const callDocRef = doc(db, 'calls', callId);
      await updateDoc(callDocRef, {
        iceCandidates: candidateData,
        updatedAt: serverTimestamp(),
      });

      console.log('✅ ICE candidate sent successfully');
    } catch (error) {
      console.error('❌ Error sending ICE candidate:', error);
      throw error;
    }
  }

  /**
   * Listen for real ICE candidates
   */
  listenForIceCandidates(
    callId: string,
    onIceCandidate: (_candidate: RTCIceCandidate) => void
  ): () => void {
    try {
      console.log('🔥 Listening for real ICE candidates:', callId);

      const candidatesRef = collection(db, 'calls', callId, 'ice_candidates');
      const candidatesQuery = query(candidatesRef, orderBy('timestamp', 'asc'));

      const unsubscribe = onSnapshot(candidatesQuery, (snapshot) => {
        snapshot.docChanges().forEach((change) => {
          if (change.type === 'added') {
            const candidateData = change.doc.data();
            console.log('🔥 New ICE candidate received:', candidateData);

            const candidate = new RTCIceCandidate({
              candidate: candidateData.candidate,
              sdpMLineIndex: candidateData.sdpMLineIndex,
              sdpMid: candidateData.sdpMid,
            });

            onIceCandidate(candidate);
          }
        });
      }, (error) => {
        console.error('❌ Error listening for ICE candidates:', error);
      });

      return () => {
        unsubscribe();
        console.log('🧹 ICE candidates listener cleaned up');
      };
    } catch (error) {
      console.error('❌ Error setting up ICE candidates listener:', error);
      return () => {};
    }
  }

  // ==================== REAL CALL STATUS UPDATES ====================

  /**
   * Update real call status
   */
  async updateCallStatus(
    callId: string,
    status: string,
    additionalData?: any
  ): Promise<void> {
    try {
      console.log('🔥 Updating real call status:', { callId, status });

      const updateData: any = {
        status,
        updatedAt: serverTimestamp(),
      };

      if (status === 'connected') {
        updateData.connectedAt = serverTimestamp();
      } else if (status === 'ended' || status === 'declined' || status === 'failed') {
        updateData.endTime = serverTimestamp();
      }

      if (additionalData) {
        Object.assign(updateData, additionalData);
      }

      const callDocRef = doc(db, 'calls', callId);
      await updateDoc(callDocRef, updateData);

      console.log('✅ Call status updated successfully');
    } catch (error) {
      console.error('❌ Error updating call status:', error);
      throw error;
    }
  }

  /**
   * Listen for real call status changes
   */
  listenForCallStatus(
    callId: string,
    onStatusChange: (_status: string, _callData: any) => void
  ): () => void {
    try {
      console.log('🔥 Listening for real call status changes:', callId);

      const callDocRef = doc(db, 'calls', callId);
      const unsubscribe = onSnapshot(callDocRef, (snapshot) => {
        if (snapshot.exists()) {
          const callData = snapshot.data();
          console.log('🔥 Call status changed:', callData.status);
          onStatusChange(callData.status, callData);
        }
      }, (error) => {
        console.error('❌ Error listening for call status:', error);
      });

      return () => {
        unsubscribe();
        console.log('🧹 Call status listener cleaned up');
      };
    } catch (error) {
      console.error('❌ Error setting up call status listener:', error);
      return () => {};
    }
  }

  // ==================== REAL CALL STATE MANAGEMENT ====================

  /**
   * Add call state listener
   */
  addCallStateListener(
    callId: string,
    listener: (_state: CallSignalingState) => void
  ): () => void {
    if (!this.callStateListeners.has(callId)) {
      this.callStateListeners.set(callId, []);
    }

    this.callStateListeners.get(callId)!.push(listener);

    return () => {
      const listeners = this.callStateListeners.get(callId);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
        if (listeners.length === 0) {
          this.callStateListeners.delete(callId);
        }
      }
    };
  }

  /**
   * Notify call state listeners
   */
  private notifyCallStateListeners(callId: string, state: CallSignalingState): void {
    const listeners = this.callStateListeners.get(callId);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(state);
        } catch (error) {
          console.error('❌ Error in call state listener:', error);
        }
      });
    }
  }

  // ==================== CLEANUP ====================

  /**
   * Clean up all signaling listeners
   */
  cleanup(): void {
    console.log('🧹 Cleaning up all signaling listeners...');

    this.signalingListeners.forEach((unsubscribe, callId) => {
      unsubscribe();
      console.log('🧹 Cleaned up signaling for call:', callId);
    });

    this.signalingListeners.clear();
    this.callStateListeners.clear();

    console.log('✅ All signaling listeners cleaned up');
  }

  /**
   * Clean up signaling for specific call
   */
  cleanupCall(callId: string): void {
    console.log('🧹 Cleaning up signaling for call:', callId);

    const unsubscribe = this.signalingListeners.get(callId);
    if (unsubscribe) {
      unsubscribe();
      this.signalingListeners.delete(callId);
    }

    this.callStateListeners.delete(callId);

    console.log('✅ Signaling cleaned up for call:', callId);
  }
}

// Export singleton instance
export const realTimeSignalingService = new RealTimeSignalingService();
export default realTimeSignalingService;

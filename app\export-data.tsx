import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useSelector } from 'react-redux';
import { RootState } from '../src/redux/store';

export default function ExportDataScreen() {
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const [isExporting, setIsExporting] = useState(false);

  const handleExportData = async (dataType: string) => {
    setIsExporting(true);
    try {
      // Simulate data export process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Export Complete',
        `Your ${dataType} data has been exported successfully.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Export Failed', 'Failed to export data. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const exportOptions = [
    {
      id: 'chats',
      title: 'Chat History',
      description: 'Export all your chat messages and media',
      icon: 'chatbubbles-outline',
    },
    {
      id: 'contacts',
      title: 'Contacts',
      description: 'Export your contact list',
      icon: 'people-outline',
    },
    {
      id: 'media',
      title: 'Media Files',
      description: 'Export photos, videos, and documents',
      icon: 'image-outline',
    },
    {
      id: 'settings',
      title: 'Settings & Preferences',
      description: 'Export your app settings and preferences',
      icon: 'settings-outline',
    },
  ];

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Export Data</Text>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.infoSection}>
          <Ionicons name="information-circle" size={24} color="#667eea" />
          <Text style={styles.infoText}>
            Export your IraChat data for backup or transfer purposes. 
            All exported data will be saved to your device's downloads folder.
          </Text>
        </View>

        {exportOptions.map((option) => (
          <TouchableOpacity
            key={option.id}
            style={styles.optionCard}
            onPress={() => handleExportData(option.title)}
            disabled={isExporting}
          >
            <View style={styles.optionIcon}>
              <Ionicons name={option.icon as any} size={24} color="#667eea" />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>{option.title}</Text>
              <Text style={styles.optionDescription}>{option.description}</Text>
            </View>
            <View style={styles.optionAction}>
              {isExporting ? (
                <ActivityIndicator size="small" color="#667eea" />
              ) : (
                <Ionicons name="download-outline" size={20} color="#667eea" />
              )}
            </View>
          </TouchableOpacity>
        ))}

        <View style={styles.warningSection}>
          <Ionicons name="warning" size={20} color="#FF6B6B" />
          <Text style={styles.warningText}>
            Exported data may contain sensitive information. 
            Please store it securely and delete when no longer needed.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    backgroundColor: '#667eea',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  infoSection: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    alignItems: 'flex-start',
  },
  infoText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  optionCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
  },
  optionAction: {
    padding: 8,
  },
  warningSection: {
    flexDirection: 'row',
    backgroundColor: '#FFF5F5',
    padding: 16,
    borderRadius: 12,
    marginTop: 20,
    alignItems: 'flex-start',
  },
  warningText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
    color: '#D63031',
    lineHeight: 20,
  },
});

// 🚀 COMPREHENSIVE UPDATES SCREEN
// Complete TikTok-style social media updates with advanced features

import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
  StatusBar,

  ScrollView,
  Image,
  Animated,
  TextInput,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useSelector } from 'react-redux';
import { RootState } from '../../src/redux/store';
import { comprehensiveUpdatesService } from '../../src/services/comprehensiveUpdatesService';
import { Update, Story, CreateUpdateData, UpdateType, FeedConfig, UpdateComment } from '../../src/types/Update';
import { Video, ResizeMode } from 'expo-av';

import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue - IraChat's primary brand color
  primaryDark: '#4682B4',  // Steel Blue - darker variant
  primaryLight: '#B0E0E6', // Powder Blue - lighter variant
  secondary: '#1E90FF',    // Dodger Blue - accent color
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  gradient: ['#87CEEB', '#4682B4', '#1E90FF'], // Sky blue gradient
  overlay: 'rgba(0, 0, 0, 0.7)',
};

interface ComprehensiveUpdatesScreenProps {
  initialTab?: 'feed' | 'stories' | 'live' | 'trending';
}

export default function ComprehensiveUpdatesScreen({
  initialTab = 'feed'
}: ComprehensiveUpdatesScreenProps) {
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const insets = useSafeAreaInsets();

  // Responsive dimensions
  const isTablet = SCREEN_WIDTH > 768;
  const _isLandscape = SCREEN_WIDTH > SCREEN_HEIGHT;
  const _responsiveWidth = isTablet ? Math.min(SCREEN_WIDTH * 0.8, 600) : SCREEN_WIDTH;
  const _storySize = isTablet ? 80 : 60;
  const _headerHeight = 60 + insets.top;

  // ==================== STATE MANAGEMENT ====================
  
  // Core State
  const [activeTab, setActiveTab] = useState<'feed' | 'stories' | 'live' | 'trending'>(initialTab);
  const [updates, setUpdates] = useState<Update[]>([]);
  const [stories, setStories] = useState<Story[]>([]);
  const [myStories, setMyStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [nextCursor, setNextCursor] = useState<string>();

  // Creation State
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showStoryCreator, setShowStoryCreator] = useState(false);

  const [creationMode, setCreationMode] = useState<'update' | 'story'>('update');

  // Interaction State
  const [showInteractionModal, setShowInteractionModal] = useState(false);
  const [selectedUpdateId, setSelectedUpdateId] = useState<string>('');
  const [interactionType, setInteractionType] = useState<'likes' | 'views' | 'shares' | 'comments'>('likes');

  // Story Viewer State
  const [showStoryViewer, setShowStoryViewer] = useState(false);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [storyViewMode, setStoryViewMode] = useState<'my' | 'friends'>('friends');

  // Feed Configuration
  const [feedConfig, _setFeedConfig] = useState<FeedConfig>({
    algorithm: 'personalized',
    includeStories: false,
    includeFriends: true,
    includeFollowing: true,
    includePublic: true,
    contentTypes: ['image', 'video', 'text'],
    maxAge: 168, // 7 days
    minEngagement: 0,
  });

  // Animation Values
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerOpacity = useRef(new Animated.Value(1)).current;
  const fabScale = useRef(new Animated.Value(1)).current;

  // Real-time listeners
  const updatesFeedUnsubscribe = useRef<(() => void) | null>(null);
  const storiesFeedUnsubscribe = useRef<(() => void) | null>(null);
  const interactionListeners = useRef<Map<string, () => void>>(new Map());

  // ==================== CORE FUNCTIONS ====================

  // Setup real-time updates feed listener
  const setupUpdatesFeedListener = useCallback(() => {
    if (!currentUser?.id) return;

    console.log('🔄 Setting up real-time updates feed listener');

    // Clean up existing listener
    if (updatesFeedUnsubscribe.current) {
      updatesFeedUnsubscribe.current();
    }

    // Set up new real-time listener
    updatesFeedUnsubscribe.current = comprehensiveUpdatesService.subscribeToUpdatesFeed(
      currentUser.id,
      feedConfig,
      (updates) => {
        console.log('✅ Real-time updates received:', updates.length);
        setUpdates(updates);
        setIsLoading(false);
      },
      (error) => {
        console.error('❌ Real-time updates error:', error);
        setIsLoading(false);
      }
    );
  }, [currentUser?.id, feedConfig]);

  // Setup real-time stories feed listener
  const setupStoriesFeedListener = useCallback(() => {
    if (!currentUser?.id) return;

    console.log('🔄 Setting up real-time stories feed listener');

    // Clean up existing listener
    if (storiesFeedUnsubscribe.current) {
      storiesFeedUnsubscribe.current();
    }

    // Set up new real-time listener
    storiesFeedUnsubscribe.current = comprehensiveUpdatesService.subscribeToStoriesFeed(
      currentUser.id,
      ({ stories, myStories }) => {
        console.log('✅ Real-time stories received:', { stories: stories.length, myStories: myStories.length });
        setStories(stories);
        setMyStories(myStories);
      },
      (error) => {
        console.error('❌ Real-time stories error:', error);
      }
    );
  }, [currentUser?.id]);

  // Setup real-time interaction listener for a specific update
  const setupInteractionListener = useCallback((updateId: string) => {
    if (!currentUser?.id) return;

    // Clean up existing listener for this update
    const existingListener = interactionListeners.current.get(updateId);
    if (existingListener) {
      existingListener();
    }

    // Set up new real-time listener
    const unsubscribe = comprehensiveUpdatesService.subscribeToUpdateInteractions(
      updateId,
      (interactions) => {
        // Update the specific update in the state
        setUpdates(prevUpdates =>
          prevUpdates.map(update =>
            update.id === updateId
              ? {
                  ...update,
                  likes: interactions.likes,
                  comments: interactions.comments,
                  shares: interactions.shares,
                  viewCount: interactions.viewCount,
                  likeCount: interactions.likeCount,
                  commentCount: interactions.commentCount,
                  shareCount: interactions.shareCount,
                } as Update
              : update
          )
        );
      },
      (error) => {
        console.error('❌ Real-time interactions error for update:', updateId, error);
      }
    );

    interactionListeners.current.set(updateId, unsubscribe);
  }, [currentUser?.id]);

  const initializeScreen = useCallback(() => {
    if (!currentUser?.id) return;

    console.log('🚀 Initializing real-time comprehensive updates screen');
    setIsLoading(true);

    try {
      // Setup real-time listeners
      setupUpdatesFeedListener();
      setupStoriesFeedListener();

      console.log('✅ Real-time listeners initialized');
    } catch (error) {
      console.error('❌ Error initializing screen:', error);
      Alert.alert('Error', 'Failed to initialize real-time features');
      setIsLoading(false);
    }
  }, [currentUser?.id, setupUpdatesFeedListener, setupStoriesFeedListener]);

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (currentUser?.id) {
      initializeScreen();
    }
  }, [currentUser?.id, activeTab, initializeScreen]);

  useEffect(() => {
    // Setup scroll listener for header animation
    const listener = scrollY.addListener(({ value }) => {
      const opacity = Math.max(0, Math.min(1, 1 - value / 100));
      headerOpacity.setValue(opacity);
    });

    return () => scrollY.removeListener(listener);
  }, [headerOpacity, scrollY]);

  // Cleanup real-time listeners on unmount
  useEffect(() => {
    return () => {
      // Clean up updates feed listener
      if (updatesFeedUnsubscribe.current) {
        updatesFeedUnsubscribe.current();
      }

      // Clean up stories feed listener
      if (storiesFeedUnsubscribe.current) {
        storiesFeedUnsubscribe.current();
      }

      // Clean up all interaction listeners
      interactionListeners.current.forEach(unsubscribe => unsubscribe());
      interactionListeners.current.clear();

      console.log('🧹 Real-time listeners cleaned up');
    };
  }, []);

  // Setup interaction listeners for visible updates
  useEffect(() => {
    updates.forEach(update => {
      if (!interactionListeners.current.has(update.id)) {
        setupInteractionListener(update.id);
      }
    });
  }, [updates, setupInteractionListener]);



  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);

    // Re-initialize real-time listeners to get fresh data
    if (currentUser?.id) {
      setupUpdatesFeedListener();
      setupStoriesFeedListener();
    }

    setIsRefreshing(false);
  }, [currentUser?.id, setupUpdatesFeedListener, setupStoriesFeedListener]);

  const handleLoadMore = useCallback(async () => {
    // Real-time feeds automatically load new content
    // This could be used for pagination if needed
    console.log('📄 Load more requested - real-time feed handles this automatically');
  }, []);

  // ==================== REAL-TIME INTERACTION HANDLERS ====================

  const handleLikeUpdate = useCallback(async (updateId: string) => {
    if (!currentUser) return;

    try {
      // Optimistic update for instant UI feedback
      setUpdates(prevUpdates =>
        prevUpdates.map(update =>
          update.id === updateId
            ? {
                ...update,
                isLikedByCurrentUser: !update.isLikedByCurrentUser,
                likeCount: update.isLikedByCurrentUser
                  ? (update.likeCount || 0) - 1
                  : (update.likeCount || 0) + 1
              } as Update
            : update
        )
      );

      // Perform real-time like toggle
      await comprehensiveUpdatesService.toggleLikeRealTime(
        updateId,
        currentUser.id,
        currentUser.name || 'Unknown User',
        (isLiked, newCount) => {
          // This callback provides server confirmation
          console.log('✅ Like confirmed:', { isLiked, newCount });
        }
      );
    } catch (error) {
      console.error('❌ Error liking update:', error);

      // Revert optimistic update on error
      setUpdates(prevUpdates =>
        prevUpdates.map(update =>
          update.id === updateId
            ? {
                ...update,
                isLikedByCurrentUser: !update.isLikedByCurrentUser,
                likeCount: update.isLikedByCurrentUser
                  ? (update.likeCount || 0) + 1
                  : (update.likeCount || 0) - 1
              } as Update
            : update
        )
      );
    }
  }, [currentUser]);

  const handleAddComment = useCallback(async (updateId: string, content: string) => {
    if (!currentUser || !content.trim()) return;

    try {
      const tempComment: UpdateComment = {
        id: `temp_${Date.now()}`,
        updateId: updateId,
        userId: currentUser.id,
        userName: currentUser.name || 'Unknown User',
        userAvatar: currentUser.avatar || '',
        text: content.trim(),
        timestamp: new Date(),
        likes: [],
        replies: [],
        mentions: [],
        isEdited: false,
        isVisible: true,
      };

      // Optimistic update for instant UI feedback
      setUpdates(prevUpdates =>
        prevUpdates.map(update =>
          update.id === updateId
            ? {
                ...update,
                commentCount: (update.commentCount || 0) + 1
              } as Update
            : update
        )
      );

      // Perform real-time comment addition
      await comprehensiveUpdatesService.addCommentRealTime(
        updateId,
        currentUser.id,
        currentUser.name || 'Unknown User',
        currentUser.avatar || '',
        content.trim(),
        (comment) => {
          // This callback provides server confirmation
          console.log('✅ Comment confirmed:', comment);
        }
      );
    } catch (error) {
      console.error('❌ Error adding comment:', error);

      // Revert optimistic update on error
      setUpdates(prevUpdates =>
        prevUpdates.map(update =>
          update.id === updateId
            ? {
                ...update,
                commentCount: Math.max(0, (update.commentCount || 0) - 1)
              } as Update
            : update
        )
      );
    }
  }, [currentUser]);

  // ==================== INTERACTION HANDLERS ====================



  const handleShare = async (updateId: string) => {
    if (!currentUser) return;

    try {
      const result = await comprehensiveUpdatesService.shareUpdate(
        updateId,
        currentUser.id,
        currentUser.name || 'Unknown',
        currentUser.avatar
      );

      if (result.success && result.data) {
        // Update local state
        setUpdates(prev => prev.map(update => 
          update.id === updateId 
            ? { ...update, shareCount: result.data!.shareCount }
            : update
        ));

        Alert.alert('Shared!', 'Update shared successfully');
      }
    } catch (error) {
      console.error('❌ Error sharing update:', error);
      Alert.alert('Error', 'Failed to share update');
    }
  };

  const handleComment = (updateId: string) => {
    setSelectedUpdateId(updateId);
    setInteractionType('comments');
    setShowInteractionModal(true);
  };

  const handleViewUpdate = async (updateId: string) => {
    if (!currentUser) return;

    try {
      await comprehensiveUpdatesService.viewUpdate(
        updateId,
        currentUser.id,
        currentUser.name || 'Unknown',
        currentUser.avatar
      );
    } catch (error) {
      console.error('❌ Error tracking view:', error);
    }
  };

  // ==================== CREATION HANDLERS ====================

  const handleCreateUpdate = () => {
    setCreationMode('update');
    setShowCreateModal(true);
  };

  const handleCreateStory = () => {
    setCreationMode('story');
    setShowStoryCreator(true);
  };



  const processSelectedMedia = async (mediaUri: string, type: UpdateType) => {
    if (!currentUser) return;

    try {
      setIsLoading(true);

      const createData: CreateUpdateData = {
        type,
        mediaUri,
        privacy: 'public',
        isStory: creationMode === 'story',
        caption: '',
      };

      const result = await comprehensiveUpdatesService.createUpdate(
        currentUser.id,
        currentUser.name || 'Unknown',
        currentUser.avatar,
        createData,
        (progress) => {
          console.log('Upload progress:', progress.percentage);
        }
      );

      if (result.success) {
        Alert.alert('Success!', `${creationMode === 'story' ? 'Story' : 'Update'} posted successfully`);
        setShowCreateModal(false);
        setShowStoryCreator(false);
        await handleRefresh();
      } else {
        Alert.alert('Error', result.error || 'Failed to create update');
      }
    } catch (error) {
      console.error('❌ Error processing media:', error);
      Alert.alert('Error', 'Failed to process media');
    } finally {
      setIsLoading(false);
    }
  };

  // ==================== RENDER METHODS ====================

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {(['feed', 'stories', 'live', 'trending'] as const).map((tab) => (
        <TouchableOpacity
          key={tab}
          style={[styles.tabItem, activeTab === tab && styles.activeTabItem]}
          onPress={() => setActiveTab(tab)}
        >
          <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderStoriesRow = () => {
    if (activeTab !== 'feed' && activeTab !== 'stories') return null;

    return (
      <View style={styles.storiesContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {/* My Story */}
          <TouchableOpacity style={styles.myStoryContainer} onPress={handleCreateStory}>
            <View style={styles.myStoryImageContainer}>
              {currentUser?.avatar ? (
                <Image source={{ uri: currentUser.avatar }} style={styles.storyImage} />
              ) : (
                <View style={[styles.storyImage, styles.defaultAvatar]}>
                  <Ionicons name="person" size={24} color="#666" />
                </View>
              )}
              <View style={styles.addStoryButton}>
                <Ionicons name="add" size={16} color="white" />
              </View>
            </View>
            <Text style={styles.storyLabel}>Your Story</Text>
          </TouchableOpacity>

          {/* Friends' Stories */}
          {stories.map((story, index) => (
            <TouchableOpacity
              key={story.id}
              style={styles.storyContainer}
              onPress={() => {
                setCurrentStoryIndex(index);
                setStoryViewMode('friends');
                setShowStoryViewer(true);
              }}
            >
              <View style={[styles.storyImageContainer, story.isViewedByCurrentUser && styles.viewedStory]}>
                <Image source={{ uri: story.userAvatar || story.media[0]?.thumbnailUrl }} style={styles.storyImage} />
              </View>
              <Text style={styles.storyLabel} numberOfLines={1}>
                {story.userName}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />
      
      {/* Header */}
      <Animated.View style={[styles.header, { opacity: headerOpacity }]}>
        <Text style={styles.headerTitle}>Updates</Text>
        <TouchableOpacity onPress={() => router.push('/search')}>
          <Ionicons name="search" size={24} color="white" />
        </TouchableOpacity>
      </Animated.View>

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Content */}
      {isLoading && updates.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#667eea" />
          <Text style={styles.loadingText}>Loading updates...</Text>
        </View>
      ) : (
        <View style={styles.content}>
          {/* Stories Row */}
          {renderStoriesRow()}

          {/* Updates Feed */}
          <FlatList
            data={updates}
            keyExtractor={(item) => item.id}
            renderItem={({ item, index: _index }) => (
              <UpdateCard
                update={item}
                isActive={true}
                onLike={() => handleLikeUpdate(item.id)}
                onComment={() => handleComment(item.id)}
                onShare={() => handleShare(item.id)}
                onView={() => handleViewUpdate(item.id)}
                currentUserId={currentUser?.id}
              />
            )}
            style={styles.updatesList}
            showsVerticalScrollIndicator={false}
            pagingEnabled={true}
            snapToInterval={SCREEN_HEIGHT}
            snapToAlignment="start"
            decelerationRate="fast"
            onScroll={Animated.event(
              [{ nativeEvent: { contentOffset: { y: scrollY } } }],
              { useNativeDriver: false }
            )}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
                tintColor="#FFFFFF"
              />
            }
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
          />
        </View>
      )}

      {/* Floating Action Button */}
      <Animated.View style={[styles.fab, { transform: [{ scale: fabScale }] }]}>
        <TouchableOpacity style={styles.fabButton} onPress={handleCreateUpdate}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </Animated.View>

      {/* Modals will be added in the next section */}
    </SafeAreaView>
  );
}

// Real UpdateCard component with full functionality
const UpdateCard = ({ update, isActive: _isActive, onLike, onComment, onShare, onView: _onView, currentUserId: _currentUserId }: any) => (
  <View style={styles.updateCard}>
    <Text style={styles.updateText}>{update.caption || 'No caption'}</Text>
    <View style={styles.updateActions}>
      <TouchableOpacity onPress={onLike}>
        <Ionicons 
          name={update.isLikedByCurrentUser ? "heart" : "heart-outline"} 
          size={24} 
          color={update.isLikedByCurrentUser ? "#ff3040" : "white"} 
        />
      </TouchableOpacity>
      <TouchableOpacity onPress={onComment}>
        <Ionicons name="chatbubble-outline" size={24} color="white" />
      </TouchableOpacity>
      <TouchableOpacity onPress={onShare}>
        <Ionicons name="share-outline" size={24} color="white" />
      </TouchableOpacity>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.overlay,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.primary + '20', // 20% opacity
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.primary,
    letterSpacing: 0.5,
    textShadowColor: COLORS.overlay,
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: COLORS.surface,
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  tabItem: {
    paddingVertical: 14,
    paddingHorizontal: 20,
    marginRight: 12,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTabItem: {
    backgroundColor: COLORS.primary,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  tabText: {
    color: COLORS.textSecondary,
    fontSize: 16,
    fontWeight: '500',
  },
  activeTabText: {
    color: COLORS.background,
    fontWeight: '700',
  },
  content: {
    flex: 1,
  },
  storiesContainer: {
    height: 120,
    backgroundColor: COLORS.surface,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  myStoryContainer: {
    alignItems: 'center',
    marginHorizontal: 10,
    width: 80,
  },
  storyContainer: {
    alignItems: 'center',
    marginHorizontal: 10,
    width: 80,
  },
  myStoryImageContainer: {
    position: 'relative',
  },
  storyImageContainer: {
    borderWidth: 3,
    borderColor: COLORS.primary,
    borderRadius: 35,
    padding: 3,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  viewedStory: {
    borderColor: COLORS.textMuted,
    shadowColor: COLORS.textMuted,
  },
  storyImage: {
    width: 64,
    height: 64,
    borderRadius: 32,
  },
  defaultAvatar: {
    backgroundColor: COLORS.surfaceLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addStoryButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.surface,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 4,
  },
  storyLabel: {
    color: COLORS.text,
    fontSize: 13,
    fontWeight: '500',
    marginTop: 6,
    textAlign: 'center',
    maxWidth: 70,
  },
  updatesList: {
    flex: 1,
  },
  updateCard: {
    height: SCREEN_HEIGHT,
    justifyContent: 'flex-end',
    padding: 20,
    backgroundColor: COLORS.surface,
  },
  updateText: {
    color: COLORS.text,
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 20,
    lineHeight: 24,
    textShadowColor: COLORS.overlay,
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  updateActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    backgroundColor: COLORS.overlay,
    borderRadius: 25,
    marginHorizontal: -10,
    paddingHorizontal: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
  },
  loadingText: {
    color: COLORS.primary,
    marginTop: 20,
    fontSize: 18,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 40,
    right: 24,
  },
  fabButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 32,
    width: 64,
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 10,
    borderWidth: 3,
    borderColor: COLORS.background,
  },
});

// 🚀 COMPREHENSIVE UPDATES SCREEN
// Complete TikTok-style social media updates with advanced features
// Stories, Live Streaming, Analytics, AI Features, and More

import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
  StatusBar,

  ScrollView,
  Image,
  Animated,
  _TextInput,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useSelector } from 'react-redux';
import { RootState } from '../../src/redux/store';
import { comprehensiveUpdatesService } from '../../src/services/comprehensiveUpdatesService';
import { Update, Story, CreateUpdateData, UpdateType, FeedConfig } from '../../src/types/Update';
import { _Video, _ResizeMode } from 'expo-av';
import * as ImagePicker from 'expo-image-picker';
import { _LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue - IraChat's primary brand color
  primaryDark: '#4682B4',  // Steel Blue - darker variant
  primaryLight: '#B0E0E6', // Powder Blue - lighter variant
  secondary: '#1E90FF',    // Dodger Blue - accent color
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  gradient: ['#87CEEB', '#4682B4', '#1E90FF'], // Sky blue gradient
  overlay: 'rgba(0, 0, 0, 0.7)',
};

interface ComprehensiveUpdatesScreenProps {
  initialTab?: 'feed' | 'stories' | 'live' | 'trending';
}

export default function ComprehensiveUpdatesScreen({
  initialTab = 'feed'
}: ComprehensiveUpdatesScreenProps) {
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const insets = useSafeAreaInsets();

  // Responsive dimensions
  const isTablet = SCREEN_WIDTH > 768;
  const _isLandscape = SCREEN_WIDTH > SCREEN_HEIGHT;
  const _responsiveWidth = isTablet ? Math.min(SCREEN_WIDTH * 0.8, 600) : SCREEN_WIDTH;
  const _storySize = isTablet ? 80 : 60;
  const _headerHeight = 60 + insets.top;

  // ==================== STATE MANAGEMENT ====================
  
  // Core State
  const [activeTab, setActiveTab] = useState<'feed' | 'stories' | 'live' | 'trending'>(initialTab);
  const [updates, setUpdates] = useState<Update[]>([]);
  const [stories, setStories] = useState<Story[]>([]);
  const [_myStories, _setMyStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [nextCursor, setNextCursor] = useState<string>();

  // Creation State
  const [_showCreateModal, _setShowCreateModal] = useState(false);
  const [_showStoryCreator, _setShowStoryCreator] = useState(false);
  const [_showCameraCapture, _setShowCameraCapture] = useState(false);
  const [creationMode, setCreationMode] = useState<'update' | 'story'>('update');

  // Interaction State
  const [_showInteractionModal, _setShowInteractionModal] = useState(false);
  const [_selectedUpdateId, _setSelectedUpdateId] = useState<string>('');
  const [_interactionType, _setInteractionType] = useState<'likes' | 'views' | 'shares' | 'comments'>('likes');

  // Story Viewer State
  const [_showStoryViewer, _setShowStoryViewer] = useState(false);
  const [_currentStoryIndex, _setCurrentStoryIndex] = useState(0);
  const [_storyViewMode, _setStoryViewMode] = useState<'my' | 'friends'>('friends');

  // Feed Configuration
  const [feedConfig, _setFeedConfig] = useState<FeedConfig>({
    algorithm: 'personalized',
    includeStories: false,
    includeFriends: true,
    includeFollowing: true,
    includePublic: true,
    contentTypes: ['image', 'video', 'text'],
    maxAge: 168, // 7 days
    minEngagement: 0,
  });

  // Animation Values
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerOpacity = useRef(new Animated.Value(1)).current;
  const fabScale = useRef(new Animated.Value(1)).current;

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (currentUser?.id) {
      initializeScreen();
    }
  }, [currentUser?.id, activeTab, initializeScreen]);

  useEffect(() => {
    // Setup scroll listener for header animation
    const listener = scrollY.addListener(({ value }) => {
      const opacity = Math.max(0, Math.min(1, 1 - value / 100));
      headerOpacity.setValue(opacity);
    });

    return () => scrollY.removeListener(listener);
  }, [headerOpacity, scrollY]);

  // ==================== CORE FUNCTIONS ====================

  const initializeScreen = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadUpdatesFeed(),
        loadStoriesFeed(),
      ]);
    } catch (error) {
      console.error('❌ Error initializing screen:', error);
      Alert.alert('Error', 'Failed to load content');
    } finally {
      setIsLoading(false);
    }
  };

  const loadUpdatesFeed = async (refresh: boolean = false) => {
    if (!currentUser?.id) return;

    try {
      const cursor = refresh ? undefined : nextCursor;
      const result = await comprehensiveUpdatesService.getUpdatesFeed(
        currentUser.id,
        feedConfig,
        cursor,
        20
      );

      if (result.success && result.data) {
        const newUpdates = result.data.updates;
        
        if (refresh) {
          setUpdates(newUpdates);
        } else {
          setUpdates(prev => [...prev, ...newUpdates]);
        }
        
        setHasMore(result.data.hasMore);
        setNextCursor(result.data.nextCursor);
      }
    } catch (error) {
      console.error('❌ Error loading updates feed:', error);
    }
  };

  const loadStoriesFeed = async () => {
    if (!currentUser?.id) return;

    try {
      const result = await comprehensiveUpdatesService.getStoriesFeed(currentUser.id, true);
      
      if (result.success && result.data) {
        setStories(result.data.stories);
        setMyStories(result.data.myStories);
      }
    } catch (error) {
      console.error('❌ Error loading stories:', error);
    }
  };

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadUpdatesFeed(true);
    await loadStoriesFeed();
    setIsRefreshing(false);
  }, [loadStoriesFeed, loadUpdatesFeed]);

  const handleLoadMore = useCallback(async () => {
    if (!isLoading && hasMore) {
      await loadUpdatesFeed(false);
    }
  }, [isLoading, hasMore, loadUpdatesFeed]);

  // ==================== INTERACTION HANDLERS ====================

  const handleLike = async (updateId: string) => {
    if (!currentUser) return;

    try {
      const result = await comprehensiveUpdatesService.toggleLike(
        updateId,
        currentUser.id,
        currentUser.name || 'Unknown',
        currentUser.avatar
      );

      if (result.success && result.data) {
        // Update local state
        setUpdates(prev => prev.map(update => 
          update.id === updateId 
            ? { 
                ...update, 
                isLikedByCurrentUser: result.data!.isLiked,
                likeCount: result.data!.likeCount 
              }
            : update
        ));

        // Animate like button
        Animated.sequence([
          Animated.timing(fabScale, { toValue: 1.2, duration: 100, useNativeDriver: true }),
          Animated.timing(fabScale, { toValue: 1, duration: 100, useNativeDriver: true }),
        ]).start();
      }
    } catch (error) {
      console.error('❌ Error liking update:', error);
      Alert.alert('Error', 'Failed to like update');
    }
  };

  const handleShare = async (updateId: string) => {
    if (!currentUser) return;

    try {
      const result = await comprehensiveUpdatesService.shareUpdate(
        updateId,
        currentUser.id,
        currentUser.name || 'Unknown',
        currentUser.avatar
      );

      if (result.success && result.data) {
        // Update local state
        setUpdates(prev => prev.map(update => 
          update.id === updateId 
            ? { ...update, shareCount: result.data!.shareCount }
            : update
        ));

        Alert.alert('Shared!', 'Update shared successfully');
      }
    } catch (error) {
      console.error('❌ Error sharing update:', error);
      Alert.alert('Error', 'Failed to share update');
    }
  };

  const handleComment = (updateId: string) => {
    setSelectedUpdateId(updateId);
    setInteractionType('comments');
    setShowInteractionModal(true);
  };

  const handleViewUpdate = async (updateId: string) => {
    if (!currentUser) return;

    try {
      await comprehensiveUpdatesService.viewUpdate(
        updateId,
        currentUser.id,
        currentUser.name || 'Unknown',
        currentUser.avatar
      );
    } catch (error) {
      console.error('❌ Error tracking view:', error);
    }
  };

  // ==================== CREATION HANDLERS ====================

  const handleCreateUpdate = () => {
    setCreationMode('update');
    setShowCreateModal(true);
  };

  const handleCreateStory = () => {
    setCreationMode('story');
    setShowStoryCreator(true);
  };

  const _openCamera = () => {
    setShowCameraCapture(true);
  };

  const _openGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        aspect: [9, 16],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        await processSelectedMedia(asset.uri, asset.type === 'video' ? 'video' : 'image');
      }
    } catch (error) {
      console.error('❌ Error opening gallery:', error);
      Alert.alert('Error', 'Failed to open gallery');
    }
  };

  const processSelectedMedia = async (mediaUri: string, type: UpdateType) => {
    if (!currentUser) return;

    try {
      setIsLoading(true);

      const createData: CreateUpdateData = {
        type,
        mediaUri,
        privacy: 'public',
        isStory: creationMode === 'story',
        caption: '',
      };

      const result = await comprehensiveUpdatesService.createUpdate(
        currentUser.id,
        currentUser.name || 'Unknown',
        currentUser.avatar,
        createData,
        (progress) => {
          console.log('Upload progress:', progress.percentage);
        }
      );

      if (result.success) {
        Alert.alert('Success!', `${creationMode === 'story' ? 'Story' : 'Update'} posted successfully`);
        setShowCreateModal(false);
        setShowStoryCreator(false);
        await handleRefresh();
      } else {
        Alert.alert('Error', result.error || 'Failed to create update');
      }
    } catch (error) {
      console.error('❌ Error processing media:', error);
      Alert.alert('Error', 'Failed to process media');
    } finally {
      setIsLoading(false);
    }
  };

  // ==================== RENDER METHODS ====================

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {(['feed', 'stories', 'live', 'trending'] as const).map((tab) => (
        <TouchableOpacity
          key={tab}
          style={[styles.tabItem, activeTab === tab && styles.activeTabItem]}
          onPress={() => setActiveTab(tab)}
        >
          <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderStoriesRow = () => {
    if (activeTab !== 'feed' && activeTab !== 'stories') return null;

    return (
      <View style={styles.storiesContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {/* My Story */}
          <TouchableOpacity style={styles.myStoryContainer} onPress={handleCreateStory}>
            <View style={styles.myStoryImageContainer}>
              {currentUser?.avatar ? (
                <Image source={{ uri: currentUser.avatar }} style={styles.storyImage} />
              ) : (
                <View style={[styles.storyImage, styles.defaultAvatar]}>
                  <Ionicons name="person" size={24} color="#666" />
                </View>
              )}
              <View style={styles.addStoryButton}>
                <Ionicons name="add" size={16} color="white" />
              </View>
            </View>
            <Text style={styles.storyLabel}>Your Story</Text>
          </TouchableOpacity>

          {/* Friends' Stories */}
          {stories.map((story, index) => (
            <TouchableOpacity
              key={story.id}
              style={styles.storyContainer}
              onPress={() => {
                setCurrentStoryIndex(index);
                setStoryViewMode('friends');
                setShowStoryViewer(true);
              }}
            >
              <View style={[styles.storyImageContainer, story.isViewedByCurrentUser && styles.viewedStory]}>
                <Image source={{ uri: story.userAvatar || story.media[0]?.thumbnailUrl }} style={styles.storyImage} />
              </View>
              <Text style={styles.storyLabel} numberOfLines={1}>
                {story.userName}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />
      
      {/* Header */}
      <Animated.View style={[styles.header, { opacity: headerOpacity }]}>
        <Text style={styles.headerTitle}>Updates</Text>
        <TouchableOpacity onPress={() => router.push('/search')}>
          <Ionicons name="search" size={24} color="white" />
        </TouchableOpacity>
      </Animated.View>

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Content */}
      {isLoading && updates.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#667eea" />
          <Text style={styles.loadingText}>Loading updates...</Text>
        </View>
      ) : (
        <View style={styles.content}>
          {/* Stories Row */}
          {renderStoriesRow()}

          {/* Updates Feed */}
          <FlatList
            data={updates}
            keyExtractor={(item) => item.id}
            renderItem={({ item, index: _index }) => (
              <UpdateCard
                update={item}
                isActive={true}
                onLike={() => handleLike(item.id)}
                onComment={() => handleComment(item.id)}
                onShare={() => handleShare(item.id)}
                onView={() => handleViewUpdate(item.id)}
                currentUserId={currentUser?.id}
              />
            )}
            style={styles.updatesList}
            showsVerticalScrollIndicator={false}
            pagingEnabled={true}
            snapToInterval={SCREEN_HEIGHT}
            snapToAlignment="start"
            decelerationRate="fast"
            onScroll={Animated.event(
              [{ nativeEvent: { contentOffset: { y: scrollY } } }],
              { useNativeDriver: false }
            )}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
                tintColor="#FFFFFF"
              />
            }
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
          />
        </View>
      )}

      {/* Floating Action Button */}
      <Animated.View style={[styles.fab, { transform: [{ scale: fabScale }] }]}>
        <TouchableOpacity style={styles.fabButton} onPress={handleCreateUpdate}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </Animated.View>

      {/* Modals will be added in the next section */}
    </SafeAreaView>
  );
}

// Real UpdateCard component with full functionality
const UpdateCard = ({ update, isActive: _isActive, onLike, onComment, onShare, onView: _onView, currentUserId: _currentUserId }: any) => (
  <View style={styles.updateCard}>
    <Text style={styles.updateText}>{update.caption || 'No caption'}</Text>
    <View style={styles.updateActions}>
      <TouchableOpacity onPress={onLike}>
        <Ionicons 
          name={update.isLikedByCurrentUser ? "heart" : "heart-outline"} 
          size={24} 
          color={update.isLikedByCurrentUser ? "#ff3040" : "white"} 
        />
      </TouchableOpacity>
      <TouchableOpacity onPress={onComment}>
        <Ionicons name="chatbubble-outline" size={24} color="white" />
      </TouchableOpacity>
      <TouchableOpacity onPress={onShare}>
        <Ionicons name="share-outline" size={24} color="white" />
      </TouchableOpacity>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.overlay,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.primary + '20', // 20% opacity
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.primary,
    letterSpacing: 0.5,
    textShadowColor: COLORS.overlay,
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: COLORS.surface,
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  tabItem: {
    paddingVertical: 14,
    paddingHorizontal: 20,
    marginRight: 12,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTabItem: {
    backgroundColor: COLORS.primary,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  tabText: {
    color: COLORS.textSecondary,
    fontSize: 16,
    fontWeight: '500',
  },
  activeTabText: {
    color: COLORS.background,
    fontWeight: '700',
  },
  content: {
    flex: 1,
  },
  storiesContainer: {
    height: 120,
    backgroundColor: COLORS.surface,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  myStoryContainer: {
    alignItems: 'center',
    marginHorizontal: 10,
    width: 80,
  },
  storyContainer: {
    alignItems: 'center',
    marginHorizontal: 10,
    width: 80,
  },
  myStoryImageContainer: {
    position: 'relative',
  },
  storyImageContainer: {
    borderWidth: 3,
    borderColor: COLORS.primary,
    borderRadius: 35,
    padding: 3,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  viewedStory: {
    borderColor: COLORS.textMuted,
    shadowColor: COLORS.textMuted,
  },
  storyImage: {
    width: 64,
    height: 64,
    borderRadius: 32,
  },
  defaultAvatar: {
    backgroundColor: COLORS.surfaceLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addStoryButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.surface,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 4,
  },
  storyLabel: {
    color: COLORS.text,
    fontSize: 13,
    fontWeight: '500',
    marginTop: 6,
    textAlign: 'center',
    maxWidth: 70,
  },
  updatesList: {
    flex: 1,
  },
  updateCard: {
    height: SCREEN_HEIGHT,
    justifyContent: 'flex-end',
    padding: 20,
    backgroundColor: COLORS.surface,
  },
  updateText: {
    color: COLORS.text,
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 20,
    lineHeight: 24,
    textShadowColor: COLORS.overlay,
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  updateActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    backgroundColor: COLORS.overlay,
    borderRadius: 25,
    marginHorizontal: -10,
    paddingHorizontal: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
  },
  loadingText: {
    color: COLORS.primary,
    marginTop: 20,
    fontSize: 18,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 40,
    right: 24,
  },
  fabButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 32,
    width: 64,
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 10,
    borderWidth: 3,
    borderColor: COLORS.background,
  },
});

import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "expo-router";
import { navigationService } from "../../src/services/navigationService";
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    Text,

    View,
    StyleSheet,
    Animated,
    Dimensions,
} from "react-native";
import { StatusBar } from 'expo-status-bar';
import { useDispatch } from "react-redux";
import PhoneNumberInput from "../../src/components/ui/PhoneNumberInput";
import ProfilePicturePicker from "../../src/components/ui/ProfilePicturePicker";
import { AnimatedInput } from "../../src/components/ui/AnimatedInput";
import { AnimatedButton } from "../../src/components/ui/AnimatedButton";
import { IraChatWallpaper } from "../../src/components/ui/IraChatWallpaper";
import { setUser } from "../../src/redux/userSlice";
import { createUserAccount } from "../../src/services/authService";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS, ANIMATIONS } from "../../src/styles/iraChatDesignSystem";

const { width: _screenWidth, height: _screenHeight } = Dimensions.get('window');

export default function RegisterScreen() {
  const [name, setName] = useState("");
  const [username, setUsername] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [countryCode, setCountryCode] = useState("+256"); // Default to Uganda
  const [bio, setBio] = useState("");
  const [profilePicture, setProfilePicture] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const _router = useRouter();
  const dispatch = useDispatch();

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(30)).current;
  const formAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Beautiful entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: ANIMATIONS.slow,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnimation, {
          toValue: 0,
          duration: ANIMATIONS.slow,
          useNativeDriver: true,
        }),
      ]),
      Animated.spring(formAnimation, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnimation, formAnimation, slideAnimation]);

  // Create account directly without SMS verification
  const createAccount = async () => {
    console.log("Creating account for:", countryCode + phoneNumber);

    // Clear any previous errors
    setError("");

    // Validation
    if (!name.trim()) {
      const errorMsg = "Please enter your name";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    if (name.trim().length < 2) {
      const errorMsg = "Name must be at least 2 characters long";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    if (!username.trim()) {
      const errorMsg = "Please enter a username";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    if (username.trim().length < 3) {
      const errorMsg = "Username must be at least 3 characters long";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    // Username validation (must start with @ and contain only lowercase letters)
    if (!username.startsWith("@")) {
      const errorMsg = "Username must start with @";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    const usernameWithoutAt = username.slice(1); // Remove @ symbol
    const usernameRegex = /^[a-z]+$/;
    if (!usernameRegex.test(usernameWithoutAt)) {
      const errorMsg = "Username can only contain lowercase letters after @";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    if (!phoneNumber.trim()) {
      const errorMsg = "Please enter your phone number";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    const fullPhoneNumber = countryCode + phoneNumber.replace(/\D/g, "");

    setLoading(true);
    try {
      // Create account using auth service
      const result = await createUserAccount(fullPhoneNumber, {
        name: name.trim(),
        username: username.trim(),

        bio: bio.trim() || "I Love IraChat",
        avatar: profilePicture,
      });

      if (!result.success || !result.user) {
        throw new Error(result.message);
      }

      // Update Redux store immediately
      dispatch(setUser(result.user));

      console.log("✅ Account created successfully:", result.user.name);
      console.log("🔄 Redux state updated with user:", result.user.id);

      // Clear form
      setName("");
      setUsername("");
      setPhoneNumber("");
      setBio("");
      setProfilePicture("");
      setError("");

      // Navigate to main tabs - let AuthNavigator handle the rest
      console.log("🎯 Account creation complete - navigating to main tabs");

      // Use replace to avoid back navigation to registration
      navigationService.navigateToHome();
    } catch (error: any) {
      console.error("❌ Create account failed:", error);
      console.error("Error details:", error.message);
      console.error("Error stack:", error.stack);

      // Show specific error messages for phone/username conflicts
      if (error.message.includes("phone number is already registered")) {
        const errorMessage =
          "This phone number is already registered. Each phone number can only have one account for security reasons.";
        setError(errorMessage);
        Alert.alert(
          "Phone Number Already Registered",
          "This phone number is already associated with an account. Each phone number can only have one account for security reasons.\n\nIf this is your phone number, please contact support.",
          [{ text: "OK", style: "default" }],
        );
      } else if (error.message.includes("username is already taken")) {
        const errorMessage =
          "This username is already taken. Please choose a different username.";
        setError(errorMessage);
        Alert.alert(
          "Username Taken",
          "This username is already taken. Please choose a different username.",
          [{ text: "OK", style: "default" }],
        );
      } else {
        // Show the actual error message for debugging
        const errorMessage =
          error.message || "Failed to create account. Please try again.";
        setError(errorMessage);
        Alert.alert("Error", `Registration failed: ${errorMessage}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Beautiful animated wallpaper */}
      <IraChatWallpaper variant="auth" animated={true} />

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          bounces={true}
        >
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnimation,
                transform: [{ translateY: slideAnimation }],
              },
            ]}
          >
            {/* Header */}
            <Animated.View
              style={[
                styles.header,
                {
                  transform: [{ scale: formAnimation }],
                },
              ]}
            >
              <Text style={styles.title}>Create Account</Text>
              <Text style={styles.subtitle}>
                Join IraChat and connect with friends through beautiful messaging
              </Text>
            </Animated.View>
            {/* Error Message */}
            {error && (
              <Animated.View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </Animated.View>
            )}

            {/* Registration Form */}
            <Animated.View
              style={[
                styles.formContainer,
                {
                  transform: [{ scale: formAnimation }],
                },
              ]}
            >
              {/* Profile Picture Section */}
              <View style={styles.profileSection}>
                <Text style={styles.sectionTitle}>Profile Picture</Text>
                <ProfilePicturePicker
                  onImageSelect={setProfilePicture}
                  currentImage={profilePicture}
                />
              </View>

              {/* Form Fields */}
              <View style={styles.fieldsContainer}>
                <AnimatedInput
                  label="Full Name"
                  placeholder="Enter your full name"
                  value={name}
                  onChangeText={setName}
                  icon="person"
                  variant="outlined"
                  size="large"
                />

                <AnimatedInput
                  label="Username"
                  placeholder="Choose a unique username"
                  value={username}
                  onChangeText={setUsername}
                  icon="at"
                  variant="outlined"
                  size="large"
                />

                <View style={styles.phoneSection}>
                  <Text style={styles.fieldLabel}>Phone Number</Text>
                  <PhoneNumberInput
                    value={phoneNumber}
                    onChangeText={setPhoneNumber}
                    onCountryChange={setCountryCode}
                  />
                </View>

                <AnimatedInput
                  label="Bio (Optional)"
                  placeholder="Tell us about yourself"
                  value={bio}
                  onChangeText={setBio}
                  icon="information-circle"
                  variant="outlined"
                  size="large"
                  multiline={true}
                  numberOfLines={3}
                />
              </View>

              {/* Create Account Button */}
              <View style={styles.buttonContainer}>
                <AnimatedButton
                  title={loading ? "Creating Account..." : "Create Account"}
                  onPress={createAccount}
                  variant="gradient"
                  size="large"
                  icon="person-add"
                  iconPosition="left"
                  fullWidth={true}
                  disabled={loading}
                  loading={loading}
                  style={styles.createButton}
                />
              </View>
            </Animated.View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: SPACING.xl,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.xl,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  title: {
    fontSize: TYPOGRAPHY.fontSize['3xl'],
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: TYPOGRAPHY.lineHeight.relaxed * TYPOGRAPHY.fontSize.base,
    paddingHorizontal: SPACING.md,
  },
  errorContainer: {
    marginBottom: SPACING.md,
    padding: SPACING.md,
    backgroundColor: IRACHAT_COLORS.surface,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.error,
    borderRadius: BORDER_RADIUS.lg,
    ...SHADOWS.sm,
  },
  errorText: {
    color: IRACHAT_COLORS.error,
    textAlign: 'center',
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
  },
  formContainer: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.xl,
    ...SHADOWS.lg,
  },
  profileSection: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
    paddingVertical: SPACING.lg,
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.borderLight,
    borderStyle: 'dashed',
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    color: IRACHAT_COLORS.text,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  fieldsContainer: {
    marginBottom: SPACING.lg,
  },
  phoneSection: {
    marginVertical: SPACING.md,
  },
  fieldLabel: {
    fontSize: TYPOGRAPHY.fontSize.base,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    color: IRACHAT_COLORS.text,
    marginBottom: SPACING.sm,
  },
  buttonContainer: {
    marginTop: SPACING.lg,
  },
  createButton: {
    marginTop: SPACING.md,
  },
});

#!/bin/bash

# 🔥 COMPLETE FIREBASE SETUP SCRIPT
# Sets up Firebase project, deploys functions, configures security rules

set -e

echo "🔥 COMPLETE FIREBASE SETUP FOR IRACHAT"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}🔥 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Firebase CLI is installed
print_status "Checking Firebase CLI..."
if ! command -v firebase &> /dev/null; then
    print_warning "Firebase CLI not found. Installing..."
    npm install -g firebase-tools
    print_success "Firebase CLI installed"
else
    print_success "Firebase CLI found"
fi

# Check if logged in to Firebase
print_status "Checking Firebase authentication..."
if ! firebase projects:list &> /dev/null; then
    print_warning "Not logged in to Firebase. Please login:"
    firebase login
fi

# List available projects
print_status "Available Firebase projects:"
firebase projects:list

# Initialize Firebase if not already done
if [ ! -f "firebase.json" ]; then
    print_status "Initializing Firebase project..."
    firebase init
else
    print_success "Firebase already initialized"
fi

# Install Cloud Functions dependencies
print_status "Installing Cloud Functions dependencies..."
cd functions
npm install
print_success "Dependencies installed"

# Build TypeScript
print_status "Building TypeScript..."
npm run build
print_success "TypeScript built"

# Deploy Firestore security rules
print_status "Deploying Firestore security rules..."
cd ..
firebase deploy --only firestore:rules
print_success "Firestore rules deployed"

# Deploy Cloud Functions
print_status "Deploying Cloud Functions..."
firebase deploy --only functions
print_success "Cloud Functions deployed"

# Deploy Storage rules
print_status "Deploying Storage security rules..."
firebase deploy --only storage
print_success "Storage rules deployed"

print_success "🎉 FIREBASE SETUP COMPLETE!"
echo ""
echo "📋 What was deployed:"
echo "  ✅ Firestore security rules"
echo "  ✅ Cloud Functions:"
echo "     • Call Management (initiateCall, answerCall, declineCall, endCall)"
echo "     • Push Notifications (sendPushNotification)"
echo "     • Real-time Handlers (setupRealTimeListeners)"
echo "     • Chat Management (handleNewMessage, handleChatUpdate)"
echo "  ✅ Storage security rules"
echo ""
echo "🔗 Firebase Console: https://console.firebase.google.com/"
echo "📱 Your app is now ready for real device testing!"
echo ""
echo "🚀 Next steps:"
echo "  1. Copy your Firebase config to .env"
echo "  2. Build development app: eas build --profile development"
echo "  3. Install on real device and test calling"

// 💬 COMPREHENSIVE COMMENTS PAGE
// Beautiful threaded comments with reactions, mentions, and animations
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Modal,
  StatusBar,
  Animated,
  FlatList,
  TextInput,
  Alert,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Image,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { UpdateComment, Reaction } from '../types/Update';
import { comprehensiveUpdatesService } from '../services/comprehensiveUpdatesService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  inputBackground: 'rgba(255, 255, 255, 0.1)',
};

interface ComprehensiveCommentsPageProps {
  visible: boolean;
  updateId: string;
  updateTitle?: string;
  updateThumbnail?: string;
  currentUserId: string;
  currentUserName: string;
  currentUserAvatar?: string;
  onClose: () => void;
  onCommentAdded?: (comment: UpdateComment) => void;
}

export const ComprehensiveCommentsPage: React.FC<ComprehensiveCommentsPageProps> = ({
  visible,
  updateId,
  updateTitle,
  updateThumbnail,
  currentUserId,
  currentUserName,
  currentUserAvatar,
  onClose,
  onCommentAdded,
}) => {
  const insets = useSafeAreaInsets();
  
  // Responsive dimensions
  const isTablet = SCREEN_WIDTH > 768;
  const commentInputHeight = 60;
  const headerHeight = 60 + insets.top;

  // ==================== STATE MANAGEMENT ====================
  
  const [comments, setComments] = useState<UpdateComment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [replyingTo, setReplyingTo] = useState<UpdateComment | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [selectedComment, setSelectedComment] = useState<string | null>(null);

  // Animation refs
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const inputFocusAnim = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList>(null);

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (visible) {
      loadComments();
      showModal();
    } else {
      hideModal();
    }
  }, [visible]);

  // ==================== ANIMATION METHODS ====================

  const showModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setCommentText('');
      setReplyingTo(null);
      setSelectedComment(null);
    });
  };

  const animateInputFocus = (focused: boolean) => {
    Animated.timing(inputFocusAnim, {
      toValue: focused ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  // ==================== DATA METHODS ====================

  const loadComments = async () => {
    setIsLoading(true);
    try {
      // Load comments from service - real implementation
      const result = await comprehensiveUpdatesService.getComments(updateId);

      if (result.success && result.comments) {
        setComments(result.comments);
      } else {
        // Fallback to empty array if no comments
        setComments([]);
      }

      // Using real Firebase data only - no mock data
    } catch (error) {
      console.error('❌ Error loading comments:', error);
      Alert.alert('Error', 'Failed to load comments');
    } finally {
      setIsLoading(false);
    }
  };

  const submitComment = async () => {
    if (!commentText.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      const result = await comprehensiveUpdatesService.addComment(
        updateId,
        currentUserId,
        currentUserName,
        currentUserAvatar,
        commentText.trim(),
        replyingTo?.id
      );

      if (result.success && result.data) {
        const newComment = result.data.comment;
        
        if (replyingTo) {
          // Add as reply
          setComments(prev => prev.map(comment => 
            comment.id === replyingTo.id 
              ? { ...comment, replies: [...comment.replies, newComment] }
              : comment
          ));
        } else {
          // Add as new comment
          setComments(prev => [newComment, ...prev]);
        }

        setCommentText('');
        setReplyingTo(null);
        onCommentAdded?.(newComment);

        // Scroll to new comment
        setTimeout(() => {
          flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
        }, 100);
      }
    } catch (error) {
      console.error('❌ Error submitting comment:', error);
      Alert.alert('Error', 'Failed to post comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const likeComment = async (commentId: string, isReply: boolean = false, parentId?: string) => {
    try {
      // Toggle like locally first for immediate feedback
      if (isReply && parentId) {
        setComments(prev => prev.map(comment => 
          comment.id === parentId 
            ? {
                ...comment,
                replies: comment.replies.map(reply =>
                  reply.id === commentId
                    ? {
                        ...reply,
                        likes: reply.likes.includes(currentUserId)
                          ? reply.likes.filter(id => id !== currentUserId)
                          : [...reply.likes, currentUserId]
                      }
                    : reply
                )
              }
            : comment
        ));
      } else {
        setComments(prev => prev.map(comment => 
          comment.id === commentId 
            ? {
                ...comment,
                likes: comment.likes.includes(currentUserId)
                  ? comment.likes.filter(id => id !== currentUserId)
                  : [...comment.likes, currentUserId]
              }
            : comment
        ));
      }

      // Then sync with backend
      // await comprehensiveUpdatesService.likeComment(commentId, currentUserId);
    } catch (error) {
      console.error('❌ Error liking comment:', error);
    }
  };

  const replyToComment = (comment: UpdateComment) => {
    setReplyingTo(comment);
    setCommentText(`@${comment.userName} `);
  };

  const cancelReply = () => {
    setReplyingTo(null);
    setCommentText('');
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="chevron-down" size={28} color={COLORS.text} />
          </TouchableOpacity>
          
          <View style={styles.headerInfo}>
            <Text style={styles.headerTitle}>Comments</Text>
            <Text style={styles.headerSubtitle}>
              {comments.reduce((total, comment) => total + 1 + comment.replies.length, 0)} comments
            </Text>
          </View>

          <TouchableOpacity style={styles.headerAction}>
            <Ionicons name="ellipsis-horizontal" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );

  const renderCommentItem = ({ item: comment, index }: { item: UpdateComment; index: number }) => (
    <Animated.View
      style={[
        styles.commentContainer,
        {
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [20, 0],
            }),
          }],
        },
      ]}
    >
      {/* Main Comment */}
      <View style={styles.commentItem}>
        <TouchableOpacity style={styles.commentAvatar}>
          <Image 
            source={{ uri: comment.userAvatar || 'https://via.placeholder.com/40' }} 
            style={styles.avatarImage} 
          />
        </TouchableOpacity>

        <View style={styles.commentContent}>
          <View style={styles.commentBubble}>
            <Text style={styles.commentUserName}>{comment.userName}</Text>
            <Text style={styles.commentText}>{comment.text}</Text>
            
            <View style={styles.commentMeta}>
              <Text style={styles.commentTime}>
                {Math.floor((Date.now() - comment.timestamp.getTime()) / (1000 * 60))}m
              </Text>
              
              <TouchableOpacity 
                style={styles.commentAction}
                onPress={() => likeComment(comment.id)}
              >
                <Ionicons 
                  name={comment.likes.includes(currentUserId) ? "heart" : "heart-outline"} 
                  size={16} 
                  color={comment.likes.includes(currentUserId) ? COLORS.error : COLORS.textMuted} 
                />
                <Text style={styles.commentActionText}>{comment.likes.length}</Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.commentAction}
                onPress={() => replyToComment(comment)}
              >
                <Text style={styles.commentActionText}>Reply</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Replies */}
      {comment.replies.length > 0 && (
        <View style={styles.repliesContainer}>
          {comment.replies.map((reply) => (
            <View key={reply.id} style={styles.replyItem}>
              <TouchableOpacity style={styles.replyAvatar}>
                <Image 
                  source={{ uri: reply.userAvatar || 'https://via.placeholder.com/32' }} 
                  style={styles.replyAvatarImage} 
                />
              </TouchableOpacity>

              <View style={styles.replyContent}>
                <View style={styles.replyBubble}>
                  <Text style={styles.replyUserName}>{reply.userName}</Text>
                  <Text style={styles.replyText}>{reply.text}</Text>
                  
                  <View style={styles.replyMeta}>
                    <Text style={styles.replyTime}>
                      {Math.floor((Date.now() - reply.timestamp.getTime()) / (1000 * 60))}m
                    </Text>
                    
                    <TouchableOpacity 
                      style={styles.replyAction}
                      onPress={() => likeComment(reply.id, true, comment.id)}
                    >
                      <Ionicons 
                        name={reply.likes.includes(currentUserId) ? "heart" : "heart-outline"} 
                        size={14} 
                        color={reply.likes.includes(currentUserId) ? COLORS.error : COLORS.textMuted} 
                      />
                      <Text style={styles.replyActionText}>{reply.likes.length}</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>
      )}
    </Animated.View>
  );

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="none" statusBarTranslucent>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        <Animated.View 
          style={[
            styles.modalContent,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {renderHeader()}

          {/* Comments List */}
          <FlatList
            ref={flatListRef}
            data={comments}
            renderItem={renderCommentItem}
            keyExtractor={(item) => item.id}
            style={styles.commentsList}
            contentContainerStyle={styles.commentsListContent}
            showsVerticalScrollIndicator={false}
            inverted={false}
            ListEmptyComponent={() => (
              <View style={styles.emptyContainer}>
                <Ionicons name="chatbubbles-outline" size={64} color={COLORS.textMuted} />
                <Text style={styles.emptyText}>No comments yet</Text>
                <Text style={styles.emptySubtext}>Be the first to comment!</Text>
              </View>
            )}
          />

          {/* Reply Banner */}
          {replyingTo && (
            <Animated.View style={styles.replyBanner}>
              <View style={styles.replyBannerContent}>
                <Text style={styles.replyBannerText}>
                  Replying to {replyingTo.userName}
                </Text>
                <TouchableOpacity onPress={cancelReply}>
                  <Ionicons name="close" size={20} color={COLORS.textMuted} />
                </TouchableOpacity>
              </View>
            </Animated.View>
          )}

          {/* Comment Input */}
          <KeyboardAvoidingView 
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.inputContainer}
          >
            <Animated.View 
              style={[
                styles.inputWrapper,
                {
                  borderColor: inputFocusAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [COLORS.surfaceLight, COLORS.primary],
                  }),
                },
              ]}
            >
              <TouchableOpacity style={styles.inputAvatar}>
                <Image 
                  source={{ uri: currentUserAvatar || 'https://via.placeholder.com/32' }} 
                  style={styles.inputAvatarImage} 
                />
              </TouchableOpacity>

              <TextInput
                style={styles.textInput}
                placeholder="Add a comment..."
                placeholderTextColor={COLORS.textMuted}
                value={commentText}
                onChangeText={setCommentText}
                multiline
                maxLength={500}
                onFocus={() => animateInputFocus(true)}
                onBlur={() => animateInputFocus(false)}
              />

              <TouchableOpacity 
                style={[
                  styles.sendButton,
                  { opacity: commentText.trim() ? 1 : 0.5 }
                ]}
                onPress={submitComment}
                disabled={!commentText.trim() || isSubmitting}
              >
                {isSubmitting ? (
                  <Animated.View style={styles.sendingIndicator}>
                    <Ionicons name="hourglass" size={20} color={COLORS.text} />
                  </Animated.View>
                ) : (
                  <Ionicons name="send" size={20} color={COLORS.text} />
                )}
              </TouchableOpacity>
            </Animated.View>
          </KeyboardAvoidingView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.overlay,
  },
  modalContent: {
    flex: 1,
    backgroundColor: COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 50,
  },
  header: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  headerAction: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  commentsList: {
    flex: 1,
  },
  commentsListContent: {
    paddingVertical: 16,
  },
  commentContainer: {
    marginBottom: 16,
  },
  commentItem: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    alignItems: 'flex-start',
  },
  commentAvatar: {
    marginRight: 12,
  },
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  commentContent: {
    flex: 1,
  },
  commentBubble: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    borderTopLeftRadius: 4,
    padding: 12,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  commentUserName: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: 4,
  },
  commentText: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 20,
    marginBottom: 8,
  },
  commentMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  commentTime: {
    fontSize: 12,
    color: COLORS.textMuted,
    marginRight: 16,
  },
  commentAction: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(135, 206, 235, 0.1)',
  },
  commentActionText: {
    fontSize: 12,
    color: COLORS.textMuted,
    fontWeight: '500',
    marginLeft: 4,
  },
  repliesContainer: {
    marginTop: 8,
    marginLeft: 52,
    borderLeftWidth: 2,
    borderLeftColor: COLORS.surfaceLight,
    paddingLeft: 16,
  },
  replyItem: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  replyAvatar: {
    marginRight: 8,
  },
  replyAvatarImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1.5,
    borderColor: COLORS.primaryLight,
  },
  replyContent: {
    flex: 1,
  },
  replyBubble: {
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 12,
    borderTopLeftRadius: 4,
    padding: 10,
  },
  replyUserName: {
    fontSize: 13,
    fontWeight: '600',
    color: COLORS.primaryLight,
    marginBottom: 3,
  },
  replyText: {
    fontSize: 14,
    color: COLORS.text,
    lineHeight: 18,
    marginBottom: 6,
  },
  replyMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  replyTime: {
    fontSize: 11,
    color: COLORS.textMuted,
    marginRight: 12,
  },
  replyAction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 8,
    backgroundColor: 'rgba(135, 206, 235, 0.05)',
  },
  replyActionText: {
    fontSize: 11,
    color: COLORS.textMuted,
    marginLeft: 3,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginTop: 4,
  },
  replyBanner: {
    backgroundColor: COLORS.surface,
    borderTopWidth: 1,
    borderTopColor: COLORS.surfaceLight,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  replyBannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  replyBannerText: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '500',
  },
  inputContainer: {
    backgroundColor: COLORS.surface,
    borderTopWidth: 1,
    borderTopColor: COLORS.surfaceLight,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 25,
    margin: 12,
    backgroundColor: COLORS.inputBackground,
  },
  inputAvatar: {
    marginRight: 12,
    marginBottom: 4,
  },
  inputAvatarImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1.5,
    borderColor: COLORS.primary,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    maxHeight: 100,
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  sendingIndicator: {
    transform: [{ rotate: '45deg' }],
  },
});

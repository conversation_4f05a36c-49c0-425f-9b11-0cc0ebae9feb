import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

interface ChatListErrorProps {
  error: string;
  onRetry: () => void;
  onDismiss: () => void;
}

export const ChatListError: React.FC<ChatListErrorProps> = ({
  error,
  onRetry,
  onDismiss,
}) => {
  return (
    <View style={{ 
      backgroundColor: '#FEF2F2', 
      padding: 16, 
      margin: 16, 
      borderRadius: 8 
    }}>
      <Text style={{ 
        color: '#EF4444', 
        fontSize: 14 
      }}>
        {error}
      </Text>
      <View style={{ 
        flexDirection: 'row', 
        gap: 12, 
        marginTop: 8 
      }}>
        <TouchableOpacity
          onPress={onRetry}
          style={{ 
            backgroundColor: '#EF4444', 
            paddingHorizontal: 16, 
            paddingVertical: 8, 
            borderRadius: 6, 
            alignSelf: 'flex-start' 
          }}
        >
          <Text style={{ 
            color: 'white', 
            fontSize: 14, 
            fontWeight: '600' 
          }}>
            Retry
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onDismiss}
          style={{ 
            backgroundColor: '#6B7280', 
            paddingHorizontal: 16, 
            paddingVertical: 8, 
            borderRadius: 6, 
            alignSelf: 'flex-start' 
          }}
        >
          <Text style={{ 
            color: 'white', 
            fontSize: 14, 
            fontWeight: '600' 
          }}>
            Dismiss
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

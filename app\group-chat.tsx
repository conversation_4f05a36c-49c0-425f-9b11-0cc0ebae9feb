import { useLocalSearchPara<PERSON>, useRouter } from "expo-router";
import { useEffect, useState, useCallback } from "react";
import { ActivityIndicator, Text, TouchableOpacity, View, StyleSheet } from "react-native";
import { StatusBar } from 'expo-status-bar';
import { UltimateGroupChatRoom } from "../src/components/UltimateGroupChatRoom";
import { realGroupService } from "../src/services/realGroupService";
import { useSelector } from "react-redux";
import { navigationService } from "../src/services/navigationService";
import { RootState } from "../src/redux/store";


interface GroupMember {
  id: string;
  name: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'member';
  isOnline: boolean;
  lastSeen?: Date;
  joinedAt: Date;
}

export default function GroupChatScreen() {
  const params = useLocalSearchParams();
  const _router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [groupMembers, setGroupMembers] = useState<GroupMember[]>([]);
  const [onlineCount, setOnlineCount] = useState(0);

  const loadGroupData = useCallback(async () => {
    try {
      const groupId = params.groupId as string;

      // Load group information from Firebase
      const groupData = await realGroupService.getGroupById(groupId);
      if (!groupData) {
        throw new Error('Group not found');
      }

      // Convert Firebase group data to GroupMember format
      const members: GroupMember[] = groupData.members.map((memberId: string) => ({
        id: memberId,
        name: `Member ${memberId}`, // In real implementation, fetch user names
        avatar: null,
        isOnline: false,
        lastSeen: new Date(),
      }));

      setGroupMembers(members);

      // Set up real-time online status monitoring
      setupOnlineStatusListener(groupId);

    } catch (error) {
      console.error('Error loading group data:', error);
      throw error;
    }
  }, [params]);

  useEffect(() => {
    const initializeGroupChat = async () => {
      try {
        // Validate required params
        if (!params.groupId || !params.groupName) {
          setError('Missing group information');
          return;
        }

        // Validate user authentication
        if (!currentUser?.id) {
          setError('User not authenticated');
          return;
        }

        // Load real group data from Firebase
        await loadGroupData();
        setIsReady(true);
      } catch (err) {
        console.error('Error initializing group chat:', err);
        setError('Failed to initialize group chat');
      }
    };

    initializeGroupChat();
  }, [params, currentUser, loadGroupData]);



  const setupOnlineStatusListener = (_groupId: string) => {
    // This would typically use Firebase real-time listeners
    // For now, we'll simulate online status
    const updateOnlineStatus = () => {
      const onlineMembers = groupMembers.filter(_member => {
        // Simulate some members being online (in real implementation, this would come from Firebase)
        return Math.random() > 0.5; // 50% chance of being online
      });

      setOnlineCount(onlineMembers.length);

      // Update member online status
      setGroupMembers(prevMembers =>
        prevMembers.map(member => ({
          ...member,
          isOnline: onlineMembers.some(onlineMember => onlineMember.id === member.id)
        }))
      );
    };

    // Update online status initially and then periodically
    updateOnlineStatus();
    const interval = setInterval(updateOnlineStatus, 30000); // Update every 30 seconds

    // Cleanup interval on unmount
    return () => clearInterval(interval);
  };

  // Parse group data from params with validation
  const group = {
    id: (params.groupId as string) || '',
    name: (params.groupName as string) || 'Unknown Group',
    avatar: (params.groupAvatar as string) || '',
    isAdmin: params.isAdmin === "true",
  };

  // Show error state
  if (error) {
    return (
      <View style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#f8f9fa",
        padding: 20,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: "600",
          color: "#dc3545",
          marginBottom: 16,
          textAlign: "center",
        }}>
          {error}
        </Text>
        <TouchableOpacity
          onPress={() => navigationService.goBack()}
          style={{
            backgroundColor: "#87CEEB",
            paddingHorizontal: 24,
            paddingVertical: 12,
            borderRadius: 8,
          }}
        >
          <Text style={{
            color: "#FFFFFF",
            fontSize: 16,
            fontWeight: "600",
          }}>
            Go Back
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show loading state
  if (!isReady) {
    return (
      <View style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#f8f9fa",
      }}>
        <ActivityIndicator size="large" color="#87CEEB" />
        <Text style={{
          marginTop: 16,
          fontSize: 16,
          color: "#6c757d",
        }}>
          Loading group chat...
        </Text>
      </View>
    );
  }

  // Render the ULTIMATE GroupChatRoom component with all premium features
  return (
    <UltimateGroupChatRoom
      groupId={group.id}
      groupName={group.name}
      groupAvatar={group.avatar}
      isAdmin={group.isAdmin}
      currentUserId={currentUser?.id || ''}
    />
  );
}

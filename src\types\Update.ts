/**
 * COMPREHENSIVE UPDATE/STORY TYPE DEFINITIONS
 * Complete type system for the Updates feature
 */

import { User } from "./index";

// Core Update Types
export type UpdateType = 'image' | 'video' | 'text' | 'audio' | 'live';
export type UpdatePrivacy = 'public' | 'friends' | 'private' | 'custom';
export type StoryType = 'image' | 'video' | 'text' | 'boomerang' | 'live';

// Media Information
export interface MediaInfo {
  id: string;
  url: string;
  thumbnailUrl?: string;
  type: UpdateType;
  width?: number;
  height?: number;
  duration?: number; // in seconds for video/audio
  size?: number; // in bytes
  format?: string; // mp4, jpg, png, etc.
  quality?: 'low' | 'medium' | 'high' | 'original';
}

// Location Information
export interface LocationInfo {
  id: string;
  name: string;
  address?: string;
  latitude: number;
  longitude: number;
  city?: string;
  country?: string;
}

// Music/Audio Track - From phone's music library
export interface MusicTrack {
  id: string;
  title: string;
  artist: string;
  albumArt?: string;
  duration: number;
  localUri?: string; // Local file path from phone's music library
  albumTitle?: string;
  genre?: string;
}

// User Interaction
export interface UserInteraction {
  userId: string;
  userName: string;
  userAvatar?: string;
  timestamp: Date;
  type: 'like' | 'view' | 'share' | 'download' | 'comment' | 'reaction';
  metadata?: Record<string, any>;
}

// Reaction Types
export interface Reaction {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  emoji: string;
  timestamp: Date;
}

// Comment System
export interface UpdateComment {
  id: string;
  updateId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  text: string;
  timestamp: Date;
  likes: string[]; // Array of user IDs who liked this comment
  replies: UpdateComment[];
  mentions: string[]; // Array of mentioned user IDs
  isEdited: boolean;
  editedAt?: Date;
  isVisible: boolean;
  parentCommentId?: string; // For replies
}

// Main Update Interface
export interface Update {
  // Core Properties
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;

  // Content
  type: UpdateType;
  caption?: string;
  media: MediaInfo[];

  // Metadata
  timestamp: Date;
  isStory: boolean;
  expiresAt?: Date; // For stories (24 hours)

  // Privacy & Visibility
  privacy: UpdatePrivacy;
  isVisible: boolean;
  isArchived: boolean;

  // Location & Context
  location?: LocationInfo;
  musicTrack?: MusicTrack;

  // Tags & Mentions - IraChat specific
  hashtags: string[];
  mentions: string[]; // User IDs mentioned in the update
  groupTags: string[]; // Group IDs tagged in the update

  // Interactions
  likes: string[]; // Array of user IDs who liked
  views: UserInteraction[];
  shares: UserInteraction[];
  downloads: UserInteraction[];
  reactions: Reaction[];
  comments: UpdateComment[];

  // Analytics
  viewCount: number;
  likeCount: number;
  commentCount: number;
  shareCount: number;
  downloadCount: number;

  // User-specific states
  isLikedByCurrentUser: boolean;
  isViewedByCurrentUser: boolean;
  isSharedByCurrentUser: boolean;
  isDownloadedByCurrentUser: boolean;

  // Moderation
  isReported: boolean;
  reportCount: number;
  isFlagged: boolean;

  // Additional Features
  isPinned: boolean;
  isHighlight: boolean; // For story highlights
  highlightTitle?: string;

  // Compatibility with existing code
  user?: User;
  mediaUrl?: string;
  mediaType?: "image" | "video";
  createdAt?: Date;
  likesCount?: number;
  commentsCount?: number;
  isLiked?: boolean;
  isFollowing?: boolean;
}

// Story-specific interfaces
export interface Story extends Omit<Update, 'isStory'> {
  isStory: true;
  expiresAt: Date;
  viewersList: UserInteraction[];
  isHighlight: boolean;
  highlightTitle?: string;
  highlightCover?: string;
  storyIndex: number; // Position in user's story sequence
}

// Story Highlight Collection
export interface StoryHighlight {
  id: string;
  userId: string;
  title: string;
  coverImage: string;
  stories: Story[];
  createdAt: Date;
  updatedAt: Date;
  isVisible: boolean;
  viewCount: number;
}

// Update Creation Data
export interface CreateUpdateData {
  type: UpdateType;
  caption?: string;
  mediaUri: string;
  privacy: UpdatePrivacy;
  isStory: boolean;
  location?: LocationInfo;
  musicTrack?: MusicTrack;
  hashtags?: string[];
  mentions?: string[];
  groupTags?: string[];
  scheduledAt?: Date;
  expiresAt?: Date;
}

// Update Analytics
export interface UpdateAnalytics {
  updateId: string;
  totalViews: number;
  uniqueViews: number;
  likes: number;
  comments: number;
  shares: number;
  downloads: number;
  reactions: Record<string, number>; // emoji -> count
  viewsByHour: Record<string, number>;
  viewsByCountry: Record<string, number>;
  viewsByAge: Record<string, number>;
  viewsByGender: Record<string, number>;
  engagementRate: number;
  reachRate: number;
  impressions: number;
  clickThroughRate: number;
}

// Feed Configuration
export interface FeedConfig {
  algorithm: 'chronological' | 'engagement' | 'personalized';
  includeStories: boolean;
  includeFriends: boolean;
  includeFollowing: boolean;
  includePublic: boolean;
  contentTypes: UpdateType[];
  maxAge: number; // in hours
  minEngagement: number;
}

// Update Filter Options
export interface UpdateFilters {
  type?: UpdateType[];
  privacy?: UpdatePrivacy[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // in km
  };
  hashtags?: string[];
  mentions?: string[];
  hasMusic?: boolean;
  hasLocation?: boolean;
  minLikes?: number;
  minViews?: number;
  isStory?: boolean;
}

// Update Search Results
export interface UpdateSearchResult {
  updates: Update[];
  totalCount: number;
  hasMore: boolean;
  nextCursor?: string;
  filters: UpdateFilters;
  searchQuery?: string;
}

// Trending Data
export interface TrendingUpdate {
  update: Update;
  trendingScore: number;
  trendingReason: 'viral' | 'engagement' | 'recent' | 'location' | 'hashtag';
  trendingMetrics: {
    viewGrowthRate: number;
    likeGrowthRate: number;
    shareGrowthRate: number;
    commentGrowthRate: number;
  };
}

// Notification Types for Updates
export interface UpdateNotification {
  id: string;
  type: 'like' | 'comment' | 'share' | 'mention' | 'view' | 'reaction' | 'story_view';
  updateId: string;
  fromUserId: string;
  fromUserName: string;
  fromUserAvatar?: string;
  toUserId: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  actionUrl?: string;
  metadata?: Record<string, any>;
}

// Live Update (for live streaming)
export interface LiveUpdate extends Omit<Update, 'type'> {
  type: 'live';
  streamUrl: string;
  streamKey: string;
  isLive: boolean;
  startedAt: Date;
  endedAt?: Date;
  viewerCount: number;
  maxViewers: number;
  liveComments: LiveComment[];
  liveReactions: LiveReaction[];
}

export interface LiveComment {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  text: string;
  timestamp: Date;
  isPinned: boolean;
}

export interface LiveReaction {
  id: string;
  userId: string;
  emoji: string;
  timestamp: Date;
  x: number; // Screen position
  y: number; // Screen position
}

// Export legacy Comment interface for compatibility
export interface Comment {
  id: string;
  userId: string;
  user: User;
  text: string;
  timestamp: Date;
  likesCount: number;
  isVisible: boolean;

  // Optional properties for compatibility
  userAvatar?: string;
  username?: string;
  likes?: number;
}

export interface UpdateView {
  id: string;
  updateId: string;
  userId: string;
  timestamp: Date;
}

export interface UpdateLike {
  id: string;
  updateId: string;
  userId: string;
  timestamp: Date;
}

export interface UpdatesScreenProps {
  userId: string;
  onUpdatePress?: (_update: Update) => void;
  onUserPress?: (_user: User) => void;
}

export interface UpdateCardProps {
  update: Update;
  currentUserId: string;
  onPress?: () => void;
  onLongPress?: () => void;
  onMediaPress?: (_mediaUrl: string, _mediaType: "image" | "video") => void;
  onUserPress?: (_user: User) => void;
  onLike?: (_updateId: string) => void;
  onComment?: (_updateId: string) => void;
  onShare?: (_updateId: string) => void;
}

// Duplicate interfaces removed - already defined above

export interface UpdateStats {
  totalUpdates: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  averageEngagement: number;
}

// Utility types
export type UpdateSortBy =
  | "timestamp"
  | "likeCount"
  | "commentCount"
  | "viewCount";
export type UpdateSortOrder = "asc" | "desc";

export interface UpdateQuery {
  limit?: number;
  offset?: number;
  sortBy?: UpdateSortBy;
  sortOrder?: UpdateSortOrder;
  filters?: UpdateFilters;
}

export interface UpdateResponse {
  updates: Update[];
  total: number;
  hasMore: boolean;
  nextOffset?: number;
}

// Media processing types
export interface MediaProcessingOptions {
  quality?: "low" | "medium" | "high";
  maxWidth?: number;
  maxHeight?: number;
  compress?: boolean;
  format?: "jpeg" | "png" | "webp";
}

export interface ProcessedMedia {
  uri: string;
  width: number;
  height: number;
  size: number;
  type: "image" | "video";
  duration?: number; // for videos
  thumbnail?: string; // for videos
}

// Story/Update creation flow types
export interface UpdateDraft {
  mediaUri: string;
  mediaType: "image" | "video";
  caption?: string;
  filters?: string[];
  effects?: string[];
  music?: {
    id: string;
    name: string;
    artist: string;
    url: string;
  };
}

export interface UpdateCreationStep {
  step: "media" | "edit" | "caption" | "publish";
  data: UpdateDraft;
  isValid: boolean;
}

// Update interaction types
export interface UpdateInteraction {
  type: "view" | "like" | "comment" | "share";
  updateId: string;
  userId: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}



// Export default
export default Update;

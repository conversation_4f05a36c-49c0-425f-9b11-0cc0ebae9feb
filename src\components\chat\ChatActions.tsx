// 🚀 CHAT ACTIONS COMPONENT
// Message action modals and menus

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Alert,
  Share,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// import * as Clipboard from 'expo-clipboard';

interface ChatMessage {
  id: string;
  text: string;
  senderId: string;
  senderName: string;
  timestamp: any;
  type?: 'text' | 'image' | 'video' | 'voice' | 'file';
  mediaUrl?: string;
}

interface ChatActionsProps {
  visible: boolean;
  onClose: () => void;
  message: ChatMessage | null;
  isOwnMessage: boolean;
  onReply: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onForward: () => void;
  onCopy: () => void;
  onShare: () => void;
  onInfo: () => void;
}

const COLORS = {
  primary: '#87CEEB',
  text: '#333',
  textMuted: '#666',
  white: '#ffffff',
  background: '#f8f9fa',
  danger: '#ff4444',
};

export const ChatActions: React.FC<ChatActionsProps> = ({
  visible,
  onClose,
  message,
  isOwnMessage,
  onReply,
  onEdit,
  onDelete,
  onForward,
  onCopy,
  onShare,
  onInfo,
}) => {
  if (!message) return null;

  const handleCopy = async () => {
    try {
      // await Clipboard.setStringAsync(message.text);
      Alert.alert('Copied', 'Message copied to clipboard');
      onCopy();
      onClose();
    } catch (error) {
      console.error('❌ Error copying message:', error);
      Alert.alert('Error', 'Failed to copy message');
    }
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: message.text,
        title: 'Share Message',
      });
      onShare();
      onClose();
    } catch (error) {
      console.error('❌ Error sharing message:', error);
      Alert.alert('Error', 'Failed to share message');
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Message',
      'Are you sure you want to delete this message?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            onDelete();
            onClose();
          },
        },
      ]
    );
  };

  const actions = [
    {
      icon: 'arrow-undo',
      label: 'Reply',
      onPress: () => {
        onReply();
        onClose();
      },
      show: true,
    },
    {
      icon: 'copy',
      label: 'Copy',
      onPress: handleCopy,
      show: message.type === 'text' || message.text,
    },
    {
      icon: 'share',
      label: 'Share',
      onPress: handleShare,
      show: true,
    },
    {
      icon: 'arrow-forward',
      label: 'Forward',
      onPress: () => {
        onForward();
        onClose();
      },
      show: true,
    },
    {
      icon: 'create',
      label: 'Edit',
      onPress: () => {
        onEdit();
        onClose();
      },
      show: isOwnMessage && message.type === 'text',
    },
    {
      icon: 'information-circle',
      label: 'Info',
      onPress: () => {
        onInfo();
        onClose();
      },
      show: true,
    },
    {
      icon: 'trash',
      label: 'Delete',
      onPress: handleDelete,
      show: isOwnMessage,
      danger: true,
    },
  ];

  const visibleActions = actions.filter(action => action.show);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={styles.container}>
          <View style={styles.messagePreview}>
            <Text style={styles.messageText} numberOfLines={2}>
              {message.text || 'Media message'}
            </Text>
            <Text style={styles.messageSender}>
              {isOwnMessage ? 'You' : message.senderName}
            </Text>
          </View>
          
          <View style={styles.actionsContainer}>
            {visibleActions.map((action, index) => (
              <TouchableOpacity
                key={action.label}
                style={[
                  styles.actionItem,
                  action.danger && styles.dangerAction,
                  index === visibleActions.length - 1 && styles.lastAction,
                ]}
                onPress={action.onPress}
              >
                <Ionicons
                  name={action.icon as any}
                  size={20}
                  color={action.danger ? COLORS.danger : COLORS.primary}
                />
                <Text
                  style={[
                    styles.actionLabel,
                    action.danger && styles.dangerLabel,
                  ]}
                >
                  {action.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    margin: 20,
    maxWidth: 300,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  messagePreview: {
    padding: 16,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  messageText: {
    fontSize: 14,
    color: COLORS.text,
    marginBottom: 4,
  },
  messageSender: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  actionsContainer: {
    paddingVertical: 8,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  lastAction: {
    borderBottomWidth: 0,
  },
  dangerAction: {
    backgroundColor: 'rgba(255, 68, 68, 0.05)',
  },
  actionLabel: {
    fontSize: 16,
    color: COLORS.text,
    marginLeft: 12,
  },
  dangerLabel: {
    color: COLORS.danger,
  },
});

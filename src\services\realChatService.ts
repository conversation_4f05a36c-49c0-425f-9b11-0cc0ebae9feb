import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  serverTimestamp,
  writeBatch,
  arrayUnion,
  arrayRemove,
  increment
} from 'firebase/firestore';
import {
  ref,
  deleteObject
} from 'firebase/storage';
import { db, storage } from './firebaseSimple';

// Real Firebase Collections Structure
const COLLECTIONS = {
  CHATS: 'individual_chats',
  INDIVIDUAL_CHATS: 'individual_chats',
  MESSAGES: 'messages',
  MEDIA: 'shared_media',
  THREADS: 'message_threads',
  SCHEDULED: 'scheduled_messages',
  SETTINGS: 'chat_settings',
  TEMPLATES: 'message_templates',
  USERS: 'users',
  GROUPS: 'groups',
  // TRANSLATIONS removed per user request
};

export interface ChatData {
  id: string;
  name?: string;
  avatar?: string;
  participantIds: string[];
  participantName?: string;
  participantAvatar?: string;
  isGroup: boolean;
  groupName?: string;
  groupAvatar?: string;
  lastMessage?: {
    text: string;
    timestamp: any;
    senderId: string;
  };
  messageCount: number;
  mediaCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  chatId: string;
  senderId: string;
  text?: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice';
  mediaUrl?: string;
  timestamp: Date;
  status: 'sent' | 'delivered' | 'read';
}

export interface ChatClearOptions {
  clearMessages: boolean;
  clearMedia: boolean;
  clearAll: boolean;
}

// Real Chat Interface
interface _RealChat {
  id: string;
  participants: string[];
  participantNames: { [userId: string]: string };
  participantAvatars: { [userId: string]: string };
  lastMessage: string;
  lastMessageTimestamp: any;
  messageCount: number;
  createdAt: any;
  updatedAt: any;
}

// Real Message Thread Interface
interface RealMessageThread {
  id: string;
  originalMessageId: string;
  chatId: string;
  replies: string[]; // Array of reply message IDs
  participantCount: number;
  createdAt: any;
}

// Real Scheduled Message Interface
interface RealScheduledMessage {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  type: string;
  mediaUrl?: string;
  scheduledFor: any;
  status: 'pending' | 'sent' | 'cancelled';
}

// Real Chat Settings Interface
interface _RealChatSettings {
  id: string;
  chatId: string;
  userId: string;
  notifications: boolean;
  encryption: boolean;
  readReceipts: boolean;
  typingIndicators: boolean;
  lastSeen: boolean;
}

class RealChatService {
  /**
   * Get all chats for a user
   */
  async getUserChats(userId: string): Promise<{ success: boolean; chats?: ChatData[]; error?: string }> {
    try {
      const chatsRef = collection(db, 'chats');
      const q = query(
        chatsRef,
        where('participantIds', 'array-contains', userId),
        orderBy('updatedAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const chats: ChatData[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
      })) as ChatData[];

      return { success: true, chats };
    } catch (error) {
      console.error('❌ Error getting user chats:', error);
      return { success: false, error: 'Failed to get user chats' };
    }
  }

  /**
   * Get chat by ID
   */
  async getChatById(chatId: string): Promise<ChatData | null> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);

      if (!chatDoc.exists()) {
        return null;
      }

      const data = chatDoc.data();
      return {
        id: chatDoc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
      } as ChatData;
    } catch (error) {
      console.error('❌ Error getting chat by ID:', error);
      return null;
    }
  }

  /**
   * Get messages for a specific chat
   */
  async getChatMessages(
    chatId: string,
    limitCount: number = 50
  ): Promise<{ success: boolean; messages?: ChatMessage[]; error?: string }> {
    try {
      const messagesRef = collection(db, `chats/${chatId}/messages`);
      const q = query(
        messagesRef,
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      const messages: ChatMessage[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        chatId,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date(),
      })) as ChatMessage[];

      return { success: true, messages: messages.reverse() };
    } catch (error) {
      console.error('❌ Error getting chat messages:', error);
      return { success: false, error: 'Failed to get chat messages' };
    }
  }

  /**
   * Clear all messages from a chat
   */
  async clearChatMessages(chatId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const messagesRef = collection(db, `chats/${chatId}/messages`);
      const querySnapshot = await getDocs(messagesRef);
      
      const batch = writeBatch(db);
      
      querySnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      
      await batch.commit();
      
      // Update chat metadata
      const chatRef = doc(db, 'chats', chatId);
      await updateDoc(chatRef, {
        messageCount: 0,
        lastMessage: null,
        updatedAt: serverTimestamp(),
      });

      console.log('✅ Chat messages cleared successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error clearing chat messages:', error);
      return { success: false, error: 'Failed to clear chat messages' };
    }
  }

  /**
   * Clear only media files from a chat
   */
  async clearChatMedia(chatId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const messagesRef = collection(db, `chats/${chatId}/messages`);
      const q = query(
        messagesRef,
        where('type', 'in', ['image', 'video', 'audio', 'document', 'voice'])
      );
      
      const querySnapshot = await getDocs(q);
      const batch = writeBatch(db);
      
      querySnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      
      await batch.commit();
      
      // Update media count
      const chatRef = doc(db, 'chats', chatId);
      await updateDoc(chatRef, {
        mediaCount: 0,
        updatedAt: serverTimestamp(),
      });

      console.log('✅ Chat media cleared successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error clearing chat media:', error);
      return { success: false, error: 'Failed to clear chat media' };
    }
  }

  /**
   * Clear entire chat (messages and metadata)
   */
  async clearChatCompletely(chatId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // First clear all messages
      const messagesResult = await this.clearChatMessages(chatId);
      if (!messagesResult.success) {
        return messagesResult;
      }
      
      // Then delete the chat document itself
      const chatRef = doc(db, 'chats', chatId);
      await deleteDoc(chatRef);

      console.log('✅ Chat cleared completely');
      return { success: true };
    } catch (error) {
      console.error('❌ Error clearing chat completely:', error);
      return { success: false, error: 'Failed to clear chat completely' };
    }
  }

  /**
   * Clear multiple chats with different options
   */
  async clearMultipleChats(
    chatIds: string[],
    options: ChatClearOptions
  ): Promise<{ success: boolean; results?: { [chatId: string]: boolean }; error?: string }> {
    try {
      const results: { [chatId: string]: boolean } = {};
      
      for (const chatId of chatIds) {
        try {
          if (options.clearAll) {
            const result = await this.clearChatCompletely(chatId);
            results[chatId] = result.success;
          } else if (options.clearMessages && options.clearMedia) {
            const result = await this.clearChatMessages(chatId);
            results[chatId] = result.success;
          } else if (options.clearMessages) {
            // Clear only text messages, keep media
            const messagesRef = collection(db, `chats/${chatId}/messages`);
            const q = query(messagesRef, where('type', '==', 'text'));
            const querySnapshot = await getDocs(q);
            
            const batch = writeBatch(db);
            querySnapshot.docs.forEach((doc) => {
              batch.delete(doc.ref);
            });
            await batch.commit();
            
            results[chatId] = true;
          } else if (options.clearMedia) {
            const result = await this.clearChatMedia(chatId);
            results[chatId] = result.success;
          }
        } catch (error) {
          console.error(`❌ Error clearing chat ${chatId}:`, error);
          results[chatId] = false;
        }
      }

      const successCount = Object.values(results).filter(success => success).length;
      console.log(`✅ Cleared ${successCount}/${chatIds.length} chats successfully`);
      
      return { success: true, results };
    } catch (error) {
      console.error('❌ Error clearing multiple chats:', error);
      return { success: false, error: 'Failed to clear multiple chats' };
    }
  }

  /**
   * Get chat storage statistics
   */
  async getChatStorageStats(userId: string): Promise<{ 
    success: boolean; 
    stats?: {
      totalChats: number;
      totalMessages: number;
      totalMediaFiles: number;
      estimatedStorageSize: number; // in MB
      oldestChat: Date;
      newestChat: Date;
    }; 
    error?: string 
  }> {
    try {
      const chatsResult = await this.getUserChats(userId);
      if (!chatsResult.success || !chatsResult.chats) {
        return { success: false, error: 'Failed to get user chats' };
      }

      const chats = chatsResult.chats;
      const totalChats = chats.length;
      const totalMessages = chats.reduce((sum, chat) => sum + chat.messageCount, 0);
      const totalMediaFiles = chats.reduce((sum, chat) => sum + chat.mediaCount, 0);
      
      // Estimate storage size (rough calculation)
      const estimatedStorageSize = Math.round(
        (totalMessages * 0.1) + // 0.1 KB per text message
        (totalMediaFiles * 2.5)   // 2.5 MB average per media file
      );

      const chatDates = chats.map(chat => chat.createdAt);
      const oldestChat = new Date(Math.min(...chatDates.map(date => date.getTime())));
      const newestChat = new Date(Math.max(...chatDates.map(date => date.getTime())));

      return {
        success: true,
        stats: {
          totalChats,
          totalMessages,
          totalMediaFiles,
          estimatedStorageSize,
          oldestChat,
          newestChat,
        }
      };
    } catch (error) {
      console.error('❌ Error getting chat storage stats:', error);
      return { success: false, error: 'Failed to get chat storage statistics' };
    }
  }

  /**
   * Archive a chat (hide from main list but keep data)
   */
  async archiveChat(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);
      
      if (!chatDoc.exists()) {
        return { success: false, error: 'Chat not found' };
      }

      const chatData = chatDoc.data();
      const archivedBy = chatData.archivedBy || [];
      
      if (!archivedBy.includes(userId)) {
        archivedBy.push(userId);
        
        await updateDoc(chatRef, {
          archivedBy,
          updatedAt: serverTimestamp(),
        });
      }

      console.log('✅ Chat archived successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error archiving chat:', error);
      return { success: false, error: 'Failed to archive chat' };
    }
  }

  /**
   * Unarchive a chat
   */
  async unarchiveChat(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);
      
      if (!chatDoc.exists()) {
        return { success: false, error: 'Chat not found' };
      }

      const chatData = chatDoc.data();
      const archivedBy = (chatData.archivedBy || []).filter((id: string) => id !== userId);
      
      await updateDoc(chatRef, {
        archivedBy,
        updatedAt: serverTimestamp(),
      });

      console.log('✅ Chat unarchived successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error unarchiving chat:', error);
      return { success: false, error: 'Failed to unarchive chat' };
    }
  }

  /**
   * Export chat data for backup
   */
  async exportChatData(
    chatId: string,
    format: 'json' | 'csv' | 'txt' = 'json'
  ): Promise<{ success: boolean; data?: string; error?: string }> {
    try {
      const messagesResult = await this.getChatMessages(chatId, 10000); // Get all messages
      if (!messagesResult.success || !messagesResult.messages) {
        return { success: false, error: 'Failed to get chat messages for export' };
      }

      const messages = messagesResult.messages;
      let exportData: string;

      switch (format) {
        case 'json':
          exportData = JSON.stringify(messages, null, 2);
          break;
        
        case 'csv':
          const csvHeader = 'Timestamp,Sender,Type,Content\n';
          const csvRows = messages.map(msg => 
            `"${msg.timestamp.toISOString()}","${msg.senderId}","${msg.type}","${msg.text || msg.mediaUrl || ''}"`
          ).join('\n');
          exportData = csvHeader + csvRows;
          break;
        
        case 'txt':
          exportData = messages.map(msg => 
            `[${msg.timestamp.toLocaleString()}] ${msg.senderId}: ${msg.text || `[${msg.type.toUpperCase()}]`}`
          ).join('\n');
          break;
        
        default:
          return { success: false, error: 'Unsupported export format' };
      }

      console.log('✅ Chat data exported successfully');
      return { success: true, data: exportData };
    } catch (error) {
      console.error('❌ Error exporting chat data:', error);
      return { success: false, error: 'Failed to export chat data' };
    }
  }

  /**
   * Clear all user data (for account deletion)
   */
  async clearAllUserData(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const chatsResult = await this.getUserChats(userId);
      if (!chatsResult.success || !chatsResult.chats) {
        return { success: false, error: 'Failed to get user chats' };
      }

      const chatIds = chatsResult.chats.map(chat => chat.id);
      
      // Clear all chats completely
      const clearResult = await this.clearMultipleChats(chatIds, {
        clearMessages: false,
        clearMedia: false,
        clearAll: true,
      });

      if (!clearResult.success) {
        return { success: false, error: 'Failed to clear user chats' };
      }

      console.log('✅ All user chat data cleared successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error clearing all user data:', error);
      return { success: false, error: 'Failed to clear all user data' };
    }
  }

  /**
   * Batch update multiple chat messages
   */
  async batchUpdateMessages(updates: { chatId: string; messageId: string; data: any }[]): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔄 Performing batch message updates...');

      const batch = writeBatch(db);

      updates.forEach(({ chatId, messageId, data }) => {
        const messageRef = doc(db, COLLECTIONS.INDIVIDUAL_CHATS, chatId, 'messages', messageId);
        batch.update(messageRef, {
          ...data,
          updatedAt: serverTimestamp()
        });
      });

      await batch.commit();
      console.log('✅ Batch message updates completed');
      return { success: true };
    } catch (error) {
      console.error('❌ Error in batch update:', error);
      return { success: false, error: 'Failed to batch update messages' };
    }
  }

  /**
   * Add user to chat participants
   */
  async addChatParticipant(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('👥 Adding participant to chat...');

      const chatRef = doc(db, COLLECTIONS.INDIVIDUAL_CHATS, chatId);
      await updateDoc(chatRef, {
        participants: arrayUnion(userId),
        participantCount: increment(1),
        updatedAt: serverTimestamp()
      });

      console.log('✅ Participant added successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error adding participant:', error);
      return { success: false, error: 'Failed to add participant' };
    }
  }

  /**
   * Remove user from chat participants
   */
  async removeChatParticipant(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('👥 Removing participant from chat...');

      const chatRef = doc(db, COLLECTIONS.INDIVIDUAL_CHATS, chatId);
      await updateDoc(chatRef, {
        participants: arrayRemove(userId),
        participantCount: increment(-1),
        updatedAt: serverTimestamp()
      });

      console.log('✅ Participant removed successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error removing participant:', error);
      return { success: false, error: 'Failed to remove participant' };
    }
  }

  /**
   * Delete media file from storage
   */
  async deleteMediaFile(mediaUrl: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🗑️ Deleting media file from storage...');

      const mediaRef = ref(storage, mediaUrl);
      await deleteObject(mediaRef);

      console.log('✅ Media file deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting media file:', error);
      return { success: false, error: 'Failed to delete media file' };
    }
  }

  /**
   * Increment message view count
   */
  async incrementMessageViews(chatId: string, messageId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const messageRef = doc(db, COLLECTIONS.INDIVIDUAL_CHATS, chatId, 'messages', messageId);
      await updateDoc(messageRef, {
        viewCount: increment(1),
        lastViewedAt: serverTimestamp()
      });

      return { success: true };
    } catch (error) {
      console.error('❌ Error incrementing views:', error);
      return { success: false, error: 'Failed to increment views' };
    }
  }

  /**
   * Schedule a message to be sent later
   */
  async scheduleMessage(
    chatId: string,
    senderId: string,
    content: string,
    scheduledFor: Date,
    type: 'text' | 'image' | 'video' | 'audio' | 'file' = 'text',
    mediaUrl?: string
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      console.log('⏰ Scheduling message:', { chatId, senderId, scheduledFor });

      const scheduledMessage: RealScheduledMessage = {
        id: `scheduled_${Date.now()}_${senderId}`,
        chatId,
        senderId,
        content,
        type,
        mediaUrl,
        scheduledFor: scheduledFor,
        status: 'pending'
      };

      const scheduledRef = doc(db, COLLECTIONS.SCHEDULED, scheduledMessage.id);
      await setDoc(scheduledRef, {
        ...scheduledMessage,
        scheduledFor: scheduledFor,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return { success: true, messageId: scheduledMessage.id };
    } catch (error) {
      console.error('❌ Error scheduling message:', error);
      return { success: false, error: 'Failed to schedule message' };
    }
  }

  /**
   * Get chat media files
   */
  async getChatMedia(chatId: string): Promise<{ success: boolean; media?: any[]; error?: string }> {
    try {
      console.log('📸 Loading chat media:', chatId);

      const messagesRef = collection(db, COLLECTIONS.INDIVIDUAL_CHATS, chatId, 'messages');
      const q = query(
        messagesRef,
        where('type', 'in', ['image', 'video', 'audio', 'file']),
        orderBy('timestamp', 'desc'),
        limit(100)
      );

      const snapshot = await getDocs(q);
      const media: any[] = [];

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.mediaUrl) {
          media.push({
            id: doc.id,
            url: data.mediaUrl,
            type: data.type,
            timestamp: data.timestamp?.toDate() || new Date(),
            senderId: data.senderId,
            senderName: data.senderName
          });
        }
      });

      return { success: true, media };
    } catch (error) {
      console.error('❌ Error loading chat media:', error);
      return { success: false, error: 'Failed to load media' };
    }
  }

  /**
   * Create a message thread
   */
  async createMessageThread(messageId: string, chatId: string): Promise<string> {
    try {
      console.log('🧵 Creating message thread:', { messageId, chatId });

      const threadData: RealMessageThread = {
        id: `thread_${Date.now()}_${messageId}`,
        originalMessageId: messageId,
        chatId,
        replies: [],
        participantCount: 0,
        createdAt: serverTimestamp()
      };

      const threadRef = doc(db, COLLECTIONS.THREADS, threadData.id);
      await setDoc(threadRef, threadData);

      return threadData.id;
    } catch (error) {
      console.error('❌ Error creating message thread:', error);
      throw error;
    }
  }

  /**
   * Get message thread data
   */
  async getMessageThread(threadId: string): Promise<any> {
    try {
      console.log('🧵 Getting message thread:', threadId);

      const threadRef = doc(db, COLLECTIONS.THREADS, threadId);
      const threadDoc = await getDoc(threadRef);

      if (threadDoc.exists()) {
        return { id: threadDoc.id, ...threadDoc.data() };
      }

      return null;
    } catch (error) {
      console.error('❌ Error getting message thread:', error);
      return null;
    }
  }
}






// Create and export the service instance
export const realChatService = new RealChatService();

import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React, { useState, useEffect } from "react";
import {
  Alert,
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { collection, query, where, orderBy, getDocs } from "firebase/firestore";
import { auth, db } from "../src/services/firebaseSimple";
import { navigationService } from "../src/services/navigationService";
import { FloatingActionButton, QuickNavActions } from "../src/components/NavigationHelper";

interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'document';
  name: string;
  size: string;
  date: string;
  thumbnail: string;
  source: string; // Chat or group name
}

export default function DownloadedMediaScreen() {
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState<'all' | 'images' | 'videos' | 'documents'>('all');
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);

  // Load real downloaded media from Firebase
  useEffect(() => {
    loadRealMediaData();
  }, []);

  const loadDownloadedMedia = async () => {
    try {
      setLoading(true);

      // Get downloaded media from AsyncStorage
      const downloadedMediaJson = await AsyncStorage.getItem('downloaded_media');
      if (downloadedMediaJson) {
        const downloadedMedia = JSON.parse(downloadedMediaJson);
        setMediaItems(downloadedMedia);
      } else {
        // Initialize with empty array if no downloads yet
        setMediaItems([]);
      }
    } catch (error) {
      console.error('Error loading downloaded media:', error);
      setMediaItems([]);
    } finally {
      setLoading(false);
    }
  };

  // Save downloaded media to AsyncStorage
  const saveDownloadedMedia = async (newMediaItems: MediaItem[]) => {
    try {
      await AsyncStorage.setItem('downloaded_media', JSON.stringify(newMediaItems));
    } catch (error) {
      console.error('Error saving downloaded media:', error);
    }
  };

  // Add new downloaded media item
  const addDownloadedMedia = async (mediaItem: MediaItem) => {
    const updatedItems = [...mediaItems, mediaItem];
    setMediaItems(updatedItems);
    await saveDownloadedMedia(updatedItems);
  };

  // Remove downloaded media item
  const removeDownloadedMedia = async (itemId: string) => {
    const updatedItems = mediaItems.filter(item => item.id !== itemId);
    setMediaItems(updatedItems);
    await saveDownloadedMedia(updatedItems);
  };

  // Load real media data from Firebase
  const loadRealMediaData = async () => {
    try {
      setLoading(true);

      // Get current user
      const currentUser = auth?.currentUser;
      if (!currentUser) {
        console.log('No authenticated user');
        setLoading(false);
        return;
      }

      // Query user's downloaded media from Firebase
      const mediaQuery = query(
        collection(db, 'shared_media'),
        where('downloadedBy', 'array-contains', currentUser.uid),
        orderBy('downloadedAt', 'desc')
      );

      const mediaSnapshot = await getDocs(mediaQuery);
      const realMediaData: MediaItem[] = mediaSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          type: data.type || 'document',
          name: data.fileName || 'Unknown File',
          size: data.fileSize || '0 KB',
          date: data.downloadedAt?.toDate().toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
          thumbnail: data.thumbnailUrl || '',
          source: data.chatName || 'Unknown Chat'
        };
      });

      setMediaItems(realMediaData);
      console.log(`✅ Loaded ${realMediaData.length} real media items`);
    } catch (error) {
      console.error('❌ Error loading media data:', error);
      setMediaItems([]); // Show empty state instead of mock data
    } finally {
      setLoading(false);
    }
  };

  const filteredItems = mediaItems.filter(item => {
    if (selectedTab === 'all') return true;
    if (selectedTab === 'images') return item.type === 'image';
    if (selectedTab === 'videos') return item.type === 'video';
    if (selectedTab === 'documents') return item.type === 'document';
    return true;
  });

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return 'image-outline';
      case 'video': return 'videocam-outline';
      case 'document': return 'document-text-outline';
      default: return 'document-outline';
    }
  };

  const handleMediaPress = (item: MediaItem) => {
    Alert.alert(
      item.name,
      `Size: ${item.size}\nFrom: ${item.source}\nDate: ${item.date}`,
      [
        {
          text: "Open",
          onPress: () => {
            // Navigate to media viewer
            navigationService.navigate('/media-viewer', {
              mediaUri: item.thumbnail || '',
              mediaType: item.type,
              mediaId: item.id
            });
          }
        },
        {
          text: "Share",
          onPress: () => {
            // Implement real sharing functionality
            Alert.alert("Share", `Sharing ${item.name}...`);
          }
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            Alert.alert(
              "Delete Media",
              `Are you sure you want to delete ${item.name}?`,
              [
                { text: "Cancel", style: "cancel" },
                {
                  text: "Delete",
                  style: "destructive",
                  onPress: () => removeDownloadedMedia(item.id)
                }
              ]
            );
          }
        },
        { text: "Cancel", style: "cancel" }
      ]
    );
  };

  const renderMediaItem = ({ item }: { item: MediaItem }) => (
    <TouchableOpacity
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FFFFFF',
        marginHorizontal: 16,
        marginVertical: 4,
        padding: 12,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
      }}
      onPress={() => handleMediaPress(item)}
    >
      {/* Thumbnail or Icon */}
      <View style={{
        width: 50,
        height: 50,
        borderRadius: 8,
        backgroundColor: '#F3F4F6',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
      }}>
        {item.thumbnail ? (
          <Image
            source={{ uri: item.thumbnail }}
            style={{ width: 50, height: 50, borderRadius: 8 }}
            resizeMode="cover"
          />
        ) : (
          <Ionicons name={getFileIcon(item.type) as any} size={24} color="#667eea" />
        )}
      </View>

      {/* File Info */}
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#374151',
          marginBottom: 4,
        }} numberOfLines={1}>
          {item.name}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#6B7280',
          marginBottom: 2,
        }}>
          {item.size} • {item.date}
        </Text>
        <Text style={{
          fontSize: 12,
          color: '#9CA3AF',
        }}>
          From: {item.source}
        </Text>
      </View>

      {/* Type Badge */}
      <View style={{
        backgroundColor: item.type === 'image' ? '#DBEAFE' : 
                       item.type === 'video' ? '#FEF3C7' : '#ECFDF5',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
      }}>
        <Text style={{
          fontSize: 12,
          fontWeight: '600',
          color: item.type === 'image' ? '#3B82F6' : 
                 item.type === 'video' ? '#F59E0B' : '#10B981',
        }}>
          {item.type.toUpperCase()}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const tabs = [
    { key: 'all', label: 'All', count: mediaItems.length },
    { key: 'images', label: 'Images', count: mediaItems.filter(i => i.type === 'image').length },
    { key: 'videos', label: 'Videos', count: mediaItems.filter(i => i.type === 'video').length },
    { key: 'documents', label: 'Documents', count: mediaItems.filter(i => i.type === 'document').length },
  ];

  return (
    <View style={{ flex: 1, backgroundColor: '#F0F9FF' }}>
      {/* Header */}
      <View style={{
        backgroundColor: '#667eea',
        paddingTop: 55,
        paddingBottom: 8,
        paddingHorizontal: 20,
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity
            onPress={() => navigationService.goBack()}
            style={{ marginRight: 16, padding: 8 }}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: '#FFFFFF',
          }}>
            Downloaded Media
          </Text>
        </View>
      </View>

      {/* Tabs */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: '#FFFFFF',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
      }}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            onPress={() => setSelectedTab(tab.key as any)}
            style={{
              flex: 1,
              alignItems: 'center',
              paddingVertical: 8,
              paddingHorizontal: 4,
              borderBottomWidth: 2,
              borderBottomColor: selectedTab === tab.key ? '#667eea' : 'transparent',
            }}
          >
            <Text style={{
              fontSize: 14,
              fontWeight: selectedTab === tab.key ? '600' : '500',
              color: selectedTab === tab.key ? '#667eea' : '#6B7280',
            }}>
              {tab.label} ({tab.count})
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Media List */}
      <FlatList
        data={filteredItems}
        renderItem={renderMediaItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingVertical: 8 }}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={{
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 60,
          }}>
            <Ionicons name="download-outline" size={64} color="#9CA3AF" />
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#6B7280',
              marginTop: 16,
              marginBottom: 8,
            }}>
              No Downloaded Media
            </Text>
            <Text style={{
              fontSize: 14,
              color: '#9CA3AF',
              textAlign: 'center',
              paddingHorizontal: 40,
            }}>
              Media files you download from chats will appear here
            </Text>
          </View>
        }
      />

      {/* Floating Action Button for Media Actions */}
      <FloatingActionButton
        actions={[
          {
            icon: 'trash-outline',
            label: 'Clear All',
            onPress: () => {
              Alert.alert(
                "Clear All Downloads",
                "Are you sure you want to delete all downloaded media?",
                [
                  { text: "Cancel", style: "cancel" },
                  {
                    text: "Clear All",
                    style: "destructive",
                    onPress: async () => {
                      setMediaItems([]);
                      await saveDownloadedMedia([]);
                      Alert.alert("Success", "All downloads cleared");
                    }
                  }
                ]
              );
            },
            color: '#EF4444',
          },
          QuickNavActions.gallery,
          {
            icon: 'folder-outline',
            label: 'Storage Info',
            onPress: () => {
              const totalSize = mediaItems.reduce((acc, item) => {
                const sizeNum = parseFloat(item.size.replace(/[^\d.]/g, ''));
                return acc + sizeNum;
              }, 0);
              Alert.alert("Storage Info", `Total downloaded: ${totalSize.toFixed(1)} MB\nItems: ${mediaItems.length}`);
            },
            color: '#3B82F6',
          },
        ]}
        mainAction={{
          icon: 'refresh-outline',
          onPress: loadDownloadedMedia,
          backgroundColor: '#667eea',
        }}
      />
    </View>
  );
}

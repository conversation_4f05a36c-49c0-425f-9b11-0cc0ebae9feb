"use strict";
// 🔥 REAL CALL MANAGEMENT CLOUD FUNCTIONS - COMPLETE WEBRTC BACKEND
// No mockups, no fake data - 100% real WebRTC call management
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.exchangeIceCandidate = exports.endCall = exports.declineCall = exports.answerCall = exports.initiateCall = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const db = admin.firestore();
// ==================== REAL CALL INITIATION ====================
exports.initiateCall = functions.https.onCall(async (data, context) => {
    var _a, _b, _c, _d;
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { receiverId, receiverName, type, chatId, offer } = data;
        const callerId = context.auth.uid;
        console.log('🔥 Initiating real call:', { callerId, receiverId, type });
        // Validate input
        if (!receiverId || !receiverName || !type) {
            throw new functions.https.HttpsError('invalid-argument', 'Missing required call data');
        }
        // Get caller info
        const callerDoc = await db.collection('users').doc(callerId).get();
        if (!callerDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Caller not found');
        }
        const callerData = callerDoc.data();
        // Check if receiver exists and is available
        const receiverDoc = await db.collection('users').doc(receiverId).get();
        if (!receiverDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Receiver not found');
        }
        const receiverData = receiverDoc.data();
        // Check if receiver is online
        const receiverPresence = await db.collection('user_presence').doc(receiverId).get();
        if (!receiverPresence.exists || ((_a = receiverPresence.data()) === null || _a === void 0 ? void 0 : _a.status) !== 'online') {
            throw new functions.https.HttpsError('unavailable', 'User is not available for calls');
        }
        // Check for existing active calls
        const existingCallsQuery = await db.collection('calls')
            .where('receiverId', '==', receiverId)
            .where('status', 'in', ['ringing', 'connecting', 'connected'])
            .limit(1)
            .get();
        if (!existingCallsQuery.empty) {
            throw new functions.https.HttpsError('resource-exhausted', 'User is already in a call');
        }
        // Create call document
        const callId = `call_${Date.now()}_${callerId}_${receiverId}`;
        const callData = {
            id: callId,
            callerId,
            callerName: callerData.displayName || callerData.username || 'Unknown',
            callerAvatar: callerData.photoURL,
            receiverId,
            receiverName,
            receiverAvatar: receiverData.photoURL,
            type,
            status: 'ringing',
            direction: 'outgoing',
            startTime: admin.firestore.FieldValue.serverTimestamp(),
            chatId,
            offer,
            deviceInfo: {
                platform: ((_b = data.deviceInfo) === null || _b === void 0 ? void 0 : _b.platform) || 'unknown',
                version: ((_c = data.deviceInfo) === null || _c === void 0 ? void 0 : _c.version) || 'unknown',
                network: ((_d = data.deviceInfo) === null || _d === void 0 ? void 0 : _d.network) || 'unknown',
            },
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        };
        // Save call to Firestore
        await db.collection('calls').doc(callId).set(callData);
        // Send push notification to receiver
        await sendCallNotification(receiverId, callData);
        // Set call timeout
        setTimeout(async () => {
            var _a;
            try {
                const callDoc = await db.collection('calls').doc(callId).get();
                if (callDoc.exists && ((_a = callDoc.data()) === null || _a === void 0 ? void 0 : _a.status) === 'ringing') {
                    await db.collection('calls').doc(callId).update({
                        status: 'missed',
                        endTime: admin.firestore.FieldValue.serverTimestamp(),
                        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                    });
                    console.log('⏰ Call timed out:', callId);
                }
            }
            catch (error) {
                console.error('❌ Error handling call timeout:', error);
            }
        }, 45000); // 45 seconds timeout
        console.log('✅ Call initiated successfully:', callId);
        return { success: true, callId, call: callData };
    }
    catch (error) {
        console.error('❌ Error initiating call:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to initiate call');
    }
});
// ==================== REAL CALL ANSWERING ====================
exports.answerCall = functions.https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { callId, answer } = data;
        const userId = context.auth.uid;
        console.log('🔥 Answering real call:', { callId, userId });
        if (!callId) {
            throw new functions.https.HttpsError('invalid-argument', 'Call ID is required');
        }
        // Get call document
        const callDoc = await db.collection('calls').doc(callId).get();
        if (!callDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Call not found');
        }
        const callData = callDoc.data();
        // Verify user is the receiver
        if (callData.receiverId !== userId) {
            throw new functions.https.HttpsError('permission-denied', 'Not authorized to answer this call');
        }
        // Check call status
        if (callData.status !== 'ringing') {
            throw new functions.https.HttpsError('failed-precondition', 'Call is not in ringing state');
        }
        // Update call status
        await db.collection('calls').doc(callId).update({
            status: 'connecting',
            answer,
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        console.log('✅ Call answered successfully:', callId);
        return { success: true, callId };
    }
    catch (error) {
        console.error('❌ Error answering call:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to answer call');
    }
});
// ==================== REAL CALL DECLINING ====================
exports.declineCall = functions.https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { callId, reason } = data;
        const userId = context.auth.uid;
        console.log('🔥 Declining real call:', { callId, userId, reason });
        if (!callId) {
            throw new functions.https.HttpsError('invalid-argument', 'Call ID is required');
        }
        // Get call document
        const callDoc = await db.collection('calls').doc(callId).get();
        if (!callDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Call not found');
        }
        const callData = callDoc.data();
        // Verify user is the receiver or caller
        if (callData.receiverId !== userId && callData.callerId !== userId) {
            throw new functions.https.HttpsError('permission-denied', 'Not authorized to decline this call');
        }
        // Update call status
        await db.collection('calls').doc(callId).update({
            status: 'declined',
            endTime: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        // Log call history
        await logCallHistory(callData, 'declined');
        console.log('✅ Call declined successfully:', callId);
        return { success: true, callId };
    }
    catch (error) {
        console.error('❌ Error declining call:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to decline call');
    }
});
// ==================== REAL CALL ENDING ====================
exports.endCall = functions.https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { callId, reason, duration, quality } = data;
        const userId = context.auth.uid;
        console.log('🔥 Ending real call:', { callId, userId, reason, duration });
        if (!callId) {
            throw new functions.https.HttpsError('invalid-argument', 'Call ID is required');
        }
        // Get call document
        const callDoc = await db.collection('calls').doc(callId).get();
        if (!callDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Call not found');
        }
        const callData = callDoc.data();
        // Verify user is participant
        if (callData.receiverId !== userId && callData.callerId !== userId) {
            throw new functions.https.HttpsError('permission-denied', 'Not authorized to end this call');
        }
        // Update call status
        const updateData = {
            status: 'ended',
            endTime: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        };
        if (duration) {
            updateData.duration = duration;
        }
        if (quality) {
            updateData.quality = quality;
        }
        await db.collection('calls').doc(callId).update(updateData);
        // Log call history
        await logCallHistory(Object.assign(Object.assign({}, callData), updateData), 'ended');
        console.log('✅ Call ended successfully:', callId);
        return { success: true, callId };
    }
    catch (error) {
        console.error('❌ Error ending call:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to end call');
    }
});
// ==================== REAL ICE CANDIDATE EXCHANGE ====================
exports.exchangeIceCandidate = functions.https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { callId, candidate } = data;
        const userId = context.auth.uid;
        console.log('🔥 Exchanging ICE candidate:', { callId, userId });
        if (!callId || !candidate) {
            throw new functions.https.HttpsError('invalid-argument', 'Call ID and candidate are required');
        }
        // Get call document
        const callDoc = await db.collection('calls').doc(callId).get();
        if (!callDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Call not found');
        }
        const callData = callDoc.data();
        // Verify user is participant
        if (callData.receiverId !== userId && callData.callerId !== userId) {
            throw new functions.https.HttpsError('permission-denied', 'Not authorized to exchange candidates');
        }
        // Add ICE candidate
        await db.collection('calls').doc(callId).update({
            iceCandidates: admin.firestore.FieldValue.arrayUnion(candidate),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        console.log('✅ ICE candidate exchanged successfully');
        return { success: true };
    }
    catch (error) {
        console.error('❌ Error exchanging ICE candidate:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to exchange ICE candidate');
    }
});
// ==================== HELPER FUNCTIONS ====================
async function sendCallNotification(receiverId, callData) {
    try {
        console.log('🔥 Sending real-time call signal to:', receiverId);
        // Use real-time database signaling instead of push notifications
        // Update the receiver's call status in Firestore for real-time signaling
        await db.collection('users').doc(receiverId).update({
            incomingCall: {
                callId: callData.id,
                callerId: callData.callerId,
                callerName: callData.callerName,
                callType: callData.type,
                timestamp: admin.firestore.FieldValue.serverTimestamp(),
                status: 'incoming'
            },
            lastUpdated: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log('✅ Real-time call signal sent successfully');
    }
    catch (error) {
        console.error('❌ Error sending call signal:', error);
    }
}
async function logCallHistory(callData, finalStatus) {
    try {
        console.log('🔥 Logging call history:', callData.id);
        const callLog = {
            id: callData.id,
            callerId: callData.callerId,
            callerName: callData.callerName,
            receiverId: callData.receiverId,
            receiverName: callData.receiverName,
            type: callData.type,
            status: finalStatus,
            startTime: callData.startTime,
            endTime: callData.endTime || admin.firestore.FieldValue.serverTimestamp(),
            duration: callData.duration || 0,
            quality: callData.quality,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
        };
        // Log for caller
        await db.collection('users').doc(callData.callerId)
            .collection('call_history').doc(callData.id).set(Object.assign(Object.assign({}, callLog), { direction: 'outgoing', contactId: callData.receiverId, contactName: callData.receiverName }));
        // Log for receiver
        await db.collection('users').doc(callData.receiverId)
            .collection('call_history').doc(callData.id).set(Object.assign(Object.assign({}, callLog), { direction: 'incoming', contactId: callData.callerId, contactName: callData.callerName }));
        console.log('✅ Call history logged');
    }
    catch (error) {
        console.error('❌ Error logging call history:', error);
    }
}
//# sourceMappingURL=callManagement.js.map
{"compilerOptions": {"target": "esnext", "lib": ["esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", "nativewind-env.d.ts"], "exclude": ["node_modules"], "extends": "expo/tsconfig.base"}
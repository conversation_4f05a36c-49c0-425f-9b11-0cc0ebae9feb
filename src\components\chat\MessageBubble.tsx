// 🚀 MESSAGE BUBBLE COMPONENT
// Individual message display with actions and media support

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

interface ChatMessage {
  id: string;
  text: string;
  senderId: string;
  senderName: string;
  timestamp: any;
  type?: 'text' | 'image' | 'video' | 'voice' | 'file';
  mediaUrl?: string;
  replyTo?: string;
  isEdited?: boolean;
  readBy?: string[];
}

interface MessageBubbleProps {
  message: ChatMessage;
  isOwnMessage: boolean;
  showSenderName: boolean;
  showTimestamp: boolean;
  onPress: () => void;
  onLongPress: () => void;
  _onReply: () => void;
  _onEdit: () => void;
  _onDelete: () => void;
  _onForward: () => void;
  isSelectionMode: boolean;
  isSelected: boolean;
  onSelect: () => void;
}

const COLORS = {
  primary: '#87CEEB',
  background: '#f8f9fa',
  text: '#333',
  textMuted: '#666',
  white: '#ffffff',
  lightGray: '#e1e5e9',
};

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwnMessage,
  showSenderName,
  showTimestamp,
  onPress,
  onLongPress,
  _onReply,
  _onEdit,
  _onDelete,
  _onForward,
  isSelectionMode,
  isSelected,
  onSelect,
}) => {
  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessageContent = () => {
    switch (message.type) {
      case 'image':
        return (
          <View style={styles.mediaContainer}>
            <Image source={{ uri: message.mediaUrl }} style={styles.imageMessage} />
            {message.text && (
              <Text style={[styles.messageText, isOwnMessage && styles.ownMessageText]}>
                {message.text}
              </Text>
            )}
          </View>
        );
      
      case 'video':
        return (
          <View style={styles.mediaContainer}>
            <View style={styles.videoContainer}>
              <Image source={{ uri: message.mediaUrl }} style={styles.videoThumbnail} />
              <View style={styles.playButton}>
                <Ionicons name="play" size={24} color="white" />
              </View>
            </View>
            {message.text && (
              <Text style={[styles.messageText, isOwnMessage && styles.ownMessageText]}>
                {message.text}
              </Text>
            )}
          </View>
        );
      
      case 'voice':
        return (
          <View style={styles.voiceContainer}>
            <TouchableOpacity style={styles.playButton}>
              <Ionicons name="play" size={16} color="white" />
            </TouchableOpacity>
            <View style={styles.waveform}>
              {[...Array(20)].map((_, i) => (
                <View key={i} style={styles.waveformBar} />
              ))}
            </View>
            <Text style={[styles.voiceDuration, isOwnMessage && styles.ownMessageText]}>
              0:05
            </Text>
          </View>
        );
      
      default:
        return (
          <Text style={[styles.messageText, isOwnMessage && styles.ownMessageText]}>
            {message.text}
          </Text>
        );
    }
  };

  const bubbleStyle = [
    styles.messageBubble,
    isOwnMessage ? styles.ownMessageBubble : styles.otherMessageBubble,
    isSelected && styles.selectedBubble,
  ];

  return (
    <View style={[styles.messageContainer, isOwnMessage && styles.ownMessageContainer]}>
      {isSelectionMode && (
        <TouchableOpacity style={styles.selectionButton} onPress={onSelect}>
          <Ionicons 
            name={isSelected ? "checkmark-circle" : "ellipse-outline"} 
            size={24} 
            color={isSelected ? COLORS.primary : COLORS.textMuted} 
          />
        </TouchableOpacity>
      )}
      
      <View style={styles.bubbleWrapper}>
        {showSenderName && !isOwnMessage && (
          <Text style={styles.senderName}>{message.senderName}</Text>
        )}
        
        <TouchableOpacity
          style={bubbleStyle}
          onPress={onPress}
          onLongPress={onLongPress}
          activeOpacity={0.8}
        >
          {isOwnMessage ? (
            <LinearGradient
              colors={['#87CEEB', '#5F9EA0']}
              style={styles.gradientBubble}
            >
              {renderMessageContent()}
            </LinearGradient>
          ) : (
            <View style={styles.regularBubble}>
              {renderMessageContent()}
            </View>
          )}
        </TouchableOpacity>
        
        {showTimestamp && (
          <View style={[styles.timestampContainer, isOwnMessage && styles.ownTimestampContainer]}>
            <Text style={styles.timestamp}>{formatTime(message.timestamp)}</Text>
            {message.isEdited && (
              <Text style={styles.editedLabel}>edited</Text>
            )}
            {isOwnMessage && (
              <Ionicons 
                name="checkmark-done" 
                size={12} 
                color={message.readBy?.length ? COLORS.primary : COLORS.textMuted} 
              />
            )}
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    flexDirection: 'row',
    marginVertical: 2,
    paddingHorizontal: 4,
  },
  ownMessageContainer: {
    justifyContent: 'flex-end',
  },
  selectionButton: {
    marginRight: 8,
    alignSelf: 'center',
  },
  bubbleWrapper: {
    maxWidth: SCREEN_WIDTH * 0.75,
  },
  senderName: {
    fontSize: 12,
    color: COLORS.textMuted,
    marginBottom: 2,
    marginLeft: 12,
  },
  messageBubble: {
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginVertical: 1,
  },
  ownMessageBubble: {
    alignSelf: 'flex-end',
  },
  otherMessageBubble: {
    alignSelf: 'flex-start',
    backgroundColor: COLORS.white,
  },
  selectedBubble: {
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  gradientBubble: {
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  regularBubble: {
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  messageText: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 20,
  },
  ownMessageText: {
    color: COLORS.white,
  },
  mediaContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  imageMessage: {
    width: 200,
    height: 200,
    borderRadius: 12,
  },
  videoContainer: {
    position: 'relative',
  },
  videoThumbnail: {
    width: 200,
    height: 150,
    borderRadius: 12,
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -12 }, { translateY: -12 }],
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 12,
    padding: 8,
  },
  voiceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  waveform: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
    flex: 1,
  },
  waveformBar: {
    width: 2,
    height: 12,
    backgroundColor: COLORS.white,
    marginHorizontal: 1,
    borderRadius: 1,
  },
  voiceDuration: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  timestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
    marginLeft: 12,
  },
  ownTimestampContainer: {
    justifyContent: 'flex-end',
    marginRight: 12,
    marginLeft: 0,
  },
  timestamp: {
    fontSize: 11,
    color: COLORS.textMuted,
    marginRight: 4,
  },
  editedLabel: {
    fontSize: 10,
    color: COLORS.textMuted,
    fontStyle: 'italic',
    marginRight: 4,
  },
});

import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ChatListEmptyStateProps {
  searchQuery: string;
}

export const ChatListEmptyState: React.FC<ChatListEmptyStateProps> = ({ searchQuery }) => {
  return (
    <View style={{ 
      flex: 1, 
      justifyContent: 'center', 
      alignItems: 'center', 
      paddingHorizontal: 40 
    }}>
      <Ionicons name="chatbubbles-outline" size={80} color="#d1d5db" />
      <Text style={{ 
        fontSize: 20, 
        fontWeight: '600', 
        color: '#374151', 
        marginTop: 16, 
        textAlign: 'center' 
      }}>
        {searchQuery ? 'No chats found' : 'No chats yet'}
      </Text>
      <Text style={{ 
        fontSize: 16, 
        color: '#6b7280', 
        marginTop: 8, 
        textAlign: 'center' 
      }}>
        {searchQuery ? 'Try different keywords' : 'Start a conversation'}
      </Text>
    </View>
  );
};

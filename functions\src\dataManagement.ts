// 🔥 REAL DATA MANAGEMENT CLOUD FUNCTIONS
// No mockups, no fake data - 100% real data backup and management functionality

import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

const db = admin.firestore();

export const backupUserData = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { userId } = data;
    const targetUserId = userId || context.auth.uid;
    
    console.log('🔥 Backing up user data for:', targetUserId);

    const backup = {
      userId: targetUserId,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      data: {} as any,
    };

    // Backup user profile
    const userDoc = await db.collection('users').doc(targetUserId).get();
    if (userDoc.exists) {
      backup.data.profile = userDoc.data();
    }

    // Backup user's chats
    const chatsQuery = await db.collection('individual_chats')
      .where('participants', 'array-contains', targetUserId)
      .get();
    
    backup.data.chats = [];
    for (const chatDoc of chatsQuery.docs) {
      const chatData = chatDoc.data();
      const chatBackup = {
        id: chatDoc.id,
        ...chatData,
        messages: [] as any[],
      };

      // Backup messages
      const messagesQuery = await db.collection('individual_chats')
        .doc(chatDoc.id)
        .collection('messages')
        .orderBy('timestamp', 'desc')
        .limit(1000) // Limit to last 1000 messages
        .get();

      chatBackup.messages = messagesQuery.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));

      backup.data.chats.push(chatBackup);
    }

    // Backup user settings
    const settingsQuery = await db.collection('chat_settings')
      .where('userId', '==', targetUserId)
      .get();
    
    backup.data.settings = settingsQuery.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));

    // Save backup
    const backupRef = await db.collection('user_backups').add(backup);

    console.log('✅ User data backup completed:', backupRef.id);
    return { backupId: backupRef.id, status: 'completed' };
  } catch (error) {
    console.error('❌ Error backing up user data:', error);
    throw new functions.https.HttpsError('internal', 'Backup failed');
  }
});

export const exportChatHistory = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { chatId, format } = data;
    console.log('🔥 Exporting chat history:', chatId);

    // Verify user is participant
    const chatDoc = await db.collection('individual_chats').doc(chatId).get();
    if (!chatDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Chat not found');
    }

    const chatData = chatDoc.data();
    if (!chatData?.participants.includes(context.auth.uid)) {
      throw new functions.https.HttpsError('permission-denied', 'Not a chat participant');
    }

    // Get all messages
    const messagesQuery = await db.collection('individual_chats')
      .doc(chatId)
      .collection('messages')
      .orderBy('timestamp', 'asc')
      .get();

    const messages = messagesQuery.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp?.toDate?.()?.toISOString() || null,
    }));

    let exportData;
    
    if (format === 'json') {
      exportData = {
        chatId,
        participants: chatData.participants,
        participantNames: chatData.participantNames,
        exportedAt: new Date().toISOString(),
        messageCount: messages.length,
        messages,
      };
    } else if (format === 'txt') {
      const lines = [`Chat Export - ${new Date().toISOString()}`, ''];
      
      messages.forEach((msg: any) => {
        const timestamp = msg.timestamp ? new Date(msg.timestamp).toLocaleString() : 'Unknown time';
        const sender = msg.senderName || 'Unknown';
        const content = msg.content || msg.text || `[${msg.type} message]`;
        lines.push(`[${timestamp}] ${sender}: ${content}`);
      });
      
      exportData = lines.join('\n');
    } else {
      throw new functions.https.HttpsError('invalid-argument', 'Unsupported format');
    }

    // Save export record
    await db.collection('chat_exports').add({
      chatId,
      userId: context.auth.uid,
      format,
      messageCount: messages.length,
      exportedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    console.log('✅ Chat history exported');
    return { data: exportData, messageCount: messages.length };
  } catch (error) {
    console.error('❌ Error exporting chat history:', error);
    throw new functions.https.HttpsError('internal', 'Export failed');
  }
});

export const cleanupDeletedData = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async (context) => {
    try {
      console.log('🔥 Cleaning up deleted data...');

      const sevenDaysAgo = admin.firestore.Timestamp.fromDate(
        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      );

      // Clean up deleted messages
      const deletedMessagesQuery = await db.collectionGroup('messages')
        .where('deleted', '==', true)
        .where('deletedAt', '<', sevenDaysAgo)
        .limit(100)
        .get();

      if (!deletedMessagesQuery.empty) {
        const batch = db.batch();
        deletedMessagesQuery.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        console.log('✅ Cleaned up', deletedMessagesQuery.size, 'deleted messages');
      }

      // Clean up old backups (keep for 30 days)
      const thirtyDaysAgo = admin.firestore.Timestamp.fromDate(
        new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      );

      const oldBackupsQuery = await db.collection('user_backups')
        .where('timestamp', '<', thirtyDaysAgo)
        .limit(50)
        .get();

      if (!oldBackupsQuery.empty) {
        const batch = db.batch();
        oldBackupsQuery.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        console.log('✅ Cleaned up', oldBackupsQuery.size, 'old backups');
      }

      return null;
    } catch (error) {
      console.error('❌ Error cleaning up deleted data:', error);
      throw error;
    }
  });

export const archiveOldChats = functions.pubsub
  .schedule('every 7 days')
  .onRun(async (context) => {
    try {
      console.log('🔥 Archiving old inactive chats...');

      const sixMonthsAgo = admin.firestore.Timestamp.fromDate(
        new Date(Date.now() - 180 * 24 * 60 * 60 * 1000)
      );

      // Find inactive chats
      const inactiveChatsQuery = await db.collection('individual_chats')
        .where('lastMessageTime', '<', sixMonthsAgo)
        .where('archived', '!=', true)
        .limit(50)
        .get();

      if (!inactiveChatsQuery.empty) {
        const batch = db.batch();
        
        inactiveChatsQuery.forEach(doc => {
          batch.update(doc.ref, {
            archived: true,
            archivedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
        });

        await batch.commit();
        console.log('✅ Archived', inactiveChatsQuery.size, 'inactive chats');
      }

      return null;
    } catch (error) {
      console.error('❌ Error archiving old chats:', error);
      throw error;
    }
  });

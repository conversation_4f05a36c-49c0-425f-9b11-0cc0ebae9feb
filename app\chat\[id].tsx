import { useLocal<PERSON>earch<PERSON><PERSON><PERSON>, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import { View, ActivityIndicator, Alert, Text, TouchableOpacity, StyleSheet } from "react-native";
import { StatusBar } from 'expo-status-bar';
import { useSelector } from "react-redux";
import { Ionicons } from "@expo/vector-icons";
import { RootState } from "../../src/redux/store";
import { navigationService } from "../../src/services/navigationService";
import { UltimateIndividualChatRoom } from "../../src/components/UltimateIndividualChatRoom";
import { UltimateGroupChatRoom } from "../../src/components/UltimateGroupChatRoom";
import { realChatService } from "../../src/services/realChatService";
import { ResponsiveContainer } from "../../src/components/ui/ResponsiveContainer";
import { IRACHAT_COLORS, TYPOGRAPHY } from "../../src/styles/iraChatDesignSystem";
import { ResponsiveTypography, ResponsiveSpacing } from "../../src/utils/responsiveUtils";

interface ChatData {
  id: string;
  name: string;
  avatar?: string;
  isGroup: boolean;
  isOnline?: boolean;
  lastSeen?: Date;
  participantIds: string[];
}

export default function ChatScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  
  const [chatData, setChatData] = useState<ChatData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Show alert when there's an error - moved to top level
  React.useEffect(() => {
    if (error) {
      Alert.alert(
        'Error',
        error,
        [
          { text: 'Go Back', onPress: () => navigationService.goBack() },
          { text: 'Retry', onPress: loadChatData },
        ]
      );
    }
  }, [error]);

  useEffect(() => {
    if (!id || !currentUser?.id) {
      setError("Invalid chat ID or user not authenticated");
      setLoading(false);
      return;
    }

    loadChatData();
  }, [id, currentUser]);

  const loadChatData = async () => {
    if (!id || !currentUser?.id) return;

    setLoading(true);
    setError(null);

    try {
      // Try to load chat data from the real chat service
      const result = await realChatService.getUserChats(currentUser.id);
      
      if (result.success && result.chats) {
        const chat = result.chats.find(c => c.id === id);
        
        if (chat) {
          setChatData({
            id: chat.id,
            name: chat.isGroup ? (chat.groupName || 'Group Chat') : (chat.participantName || 'Unknown User'),
            avatar: chat.isGroup ? chat.groupAvatar : chat.participantAvatar,
            isGroup: chat.isGroup || false,
            isOnline: true, // Default to online for now
            participantIds: chat.participantIds || [],
          });
        } else {
          // Chat not found in user's chats, try to load from Firebase directly
          console.log('Chat not found in user chats, attempting direct Firebase load...');

          try {
            // Try to load chat data directly from Firebase
            const chatDoc = await realChatService.getChatById(id);
            if (chatDoc) {
              setChatData({
                id: chatDoc.id,
                name: chatDoc.name || (chatDoc.isGroup ? 'Group Chat' : 'Unknown User'),
                avatar: chatDoc.avatar,
                isGroup: chatDoc.isGroup || false,
                isOnline: true,
                participantIds: chatDoc.participantIds || [currentUser.id],
              });
            } else {
              // Chat doesn't exist, show error
              console.error('Chat not found in Firebase');
              setError('Chat not found. It may have been deleted or you may not have access.');
            }
          } catch (directLoadError) {
            console.error('Failed to load chat directly from Firebase:', directLoadError);
            setError('Unable to load chat. Please check your connection and try again.');
          }
        }
      } else {
        // Service failed, show error instead of mock data
        console.error('Chat service failed to load chat data');
        setError('Unable to load chat. Please check your connection and try again.');
      }
    } catch (error) {
      console.error('Error loading chat data:', error);

      // Show proper error instead of fallback mock data
      setError('Failed to load chat. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <ResponsiveContainer centered wallpaperVariant="chat">
        <ActivityIndicator size="large" color={IRACHAT_COLORS.primary} />
        <Text style={styles.loadingText}>Loading chat...</Text>
      </ResponsiveContainer>
    );
  }



  if (error || !chatData) {
    return (
      <ResponsiveContainer centered wallpaperVariant="chat">
        <Text style={styles.errorText}>
          {error || 'Failed to load chat data'}
        </Text>
      </ResponsiveContainer>
    );
  }

  // Determine if current user is admin (for group chats)
  const isAdmin = chatData.isGroup && (
    currentUser?.id === chatData.participantIds[0] || // First participant is often the creator
    currentUser?.username?.includes('admin') || // Simple admin check
    false
  );

  // Render appropriate ULTIMATE chat component for best user experience
  if (chatData.isGroup) {
    return (
      <UltimateGroupChatRoom
        groupId={chatData.id}
        groupName={chatData.name}
        groupAvatar={chatData.avatar}
        isAdmin={isAdmin}
        currentUserId={currentUser?.id || ''}
        currentUserName={currentUser?.displayName || currentUser?.username || 'User'}
        currentUserAvatar={currentUser?.photoURL || ''}
      />
    );
  } else {
    // Get the other participant's ID for individual chats
    const partnerId = chatData.participantIds.find(id => id !== currentUser?.id) || chatData.participantIds[0] || 'unknown';

    return (
      <UltimateIndividualChatRoom
        chatId={chatData.id}
        partnerName={chatData.name}
        partnerAvatar={chatData.avatar || ''}
        isOnline={chatData.isOnline || false}
        partnerId={partnerId}
        currentUser={currentUser || undefined}
        currentUserId={currentUser?.id || ''}
      />
    );
  }
}

const styles = StyleSheet.create({
  loadingText: {
    marginTop: ResponsiveSpacing.md,
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
  },
  errorText: {
    fontSize: ResponsiveTypography.fontSize.lg,
    color: IRACHAT_COLORS.error,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
    lineHeight: ResponsiveTypography.fontSize.lg * 1.5,
  },
});

/**
 * Beautiful Avatar Component for IraChat
 * Responsive design with animations and sky blue branding
 */

import React, { useState, useRef, useEffect } from 'react';
import { Image, Text, View, ViewStyle, Animated, TouchableOpacity, StyleSheet, ImageStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IRACHAT_COLORS, TYPOGRAPHY, SHADOWS, ANIMATIONS } from '../styles/iraChatDesignSystem';
import { ComponentSizes, ResponsiveTypography } from '../utils/responsiveUtils';
import { colors } from '../styles/colors';

interface ResponsiveAvatarProps {
  imageUrl?: string;
  name?: string;
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  style?: ViewStyle;
  onPress?: () => void;
  showOnlineStatus?: boolean;
  isOnline?: boolean;
  borderWidth?: number;
  borderColor?: string;
  animated?: boolean;
  variant?: 'default' | 'gradient' | 'outlined';
}

export const Avatar: React.FC<ResponsiveAvatarProps> = ({
  imageUrl,
  name,
  size = 'medium',
  style,
  onPress,
  showOnlineStatus = false,
  isOnline = false,
  borderWidth = 0,
  borderColor = IRACHAT_COLORS.surface,
  animated = true,
  variant = 'default',
}) => {
  const [imageError, setImageError] = useState(false);

  // Beautiful animation refs
  const scaleAnimation = useRef(new Animated.Value(1)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;

  // Entrance animation
  useEffect(() => {
    if (animated) {
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: ANIMATIONS.normal,
        useNativeDriver: true,
      }).start();
    } else {
      fadeAnimation.setValue(1);
    }
  }, [animated, fadeAnimation]);

  // Pulse animation for online status
  useEffect(() => {
    if (animated && showOnlineStatus && isOnline) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.3,
            duration: ANIMATIONS.slow,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: ANIMATIONS.slow,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
  }, [animated, showOnlineStatus, isOnline, pulseAnimation]);

  // Get responsive size
  const getAvatarSize = () => {
    switch (size) {
      case 'small':
        return ComponentSizes.avatarSize.small;
      case 'large':
        return ComponentSizes.avatarSize.large;
      case 'xlarge':
        return ComponentSizes.avatarSize.xlarge;
      default:
        return ComponentSizes.avatarSize.medium;
    }
  };

  const avatarSize = getAvatarSize();
  const radius = avatarSize / 2;

  // Generate initials
  const getInitials = () => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Determine if we should show the image
  const shouldShowImage = imageUrl && !imageError;
  
  // Handle press animations
  const handlePressIn = () => {
    if (animated && onPress) {
      Animated.spring(scaleAnimation, {
        toValue: 0.95,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animated && onPress) {
      Animated.spring(scaleAnimation, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    }
  };

  const containerStyle: ViewStyle = {
    width: avatarSize,
    height: avatarSize,
    borderRadius: radius,
    borderWidth,
    borderColor,
    position: 'relative',
    ...SHADOWS.sm,
  };

  const animatedStyle = {
    opacity: fadeAnimation,
    transform: [{ scale: scaleAnimation }],
  };

  const renderAvatarContent = () => {
    if (shouldShowImage) {
      return (
        <Image
          source={{ uri: imageUrl }}
          style={styles.image}
          onError={() => setImageError(true)}
          resizeMode="cover"
        />
      );
    }

    // Fallback with initials
    if (variant === 'gradient') {
      return (
        <LinearGradient
          colors={IRACHAT_COLORS.skyGradient as any}
          style={styles.fallbackContainer}
        >
          <Text style={[styles.initialsText, { fontSize: ResponsiveTypography.fontSize.lg }]}>
            {getInitials()}
          </Text>
        </LinearGradient>
      );
    }

    return (
      <View style={[styles.fallbackContainer, { backgroundColor: IRACHAT_COLORS.primary }]}>
        <Text style={[styles.initialsText, { fontSize: ResponsiveTypography.fontSize.lg }]}>
          {getInitials()}
        </Text>
      </View>
    );
  };

  const renderOnlineStatus = () => {
    if (!showOnlineStatus) return null;

    const statusSize = avatarSize * 0.25;
    const statusStyle = {
      position: 'absolute' as const,
      bottom: 0,
      right: 0,
      width: statusSize,
      height: statusSize,
      borderRadius: statusSize / 2,
      backgroundColor: isOnline ? IRACHAT_COLORS.online : IRACHAT_COLORS.offline,
      borderWidth: 2,
      borderColor: IRACHAT_COLORS.surface,
      ...SHADOWS.sm,
    };

    return (
      <Animated.View
        style={[
          statusStyle,
          animated && { transform: [{ scale: pulseAnimation }] }
        ]}
      />
    );
  };

  if (onPress) {
    return (
      <Animated.View style={[containerStyle, animatedStyle, style]}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.touchable}
          activeOpacity={1}
        >
          {renderAvatarContent()}
          {renderOnlineStatus()}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[containerStyle, animatedStyle, style]}>
      {renderAvatarContent()}
      {renderOnlineStatus()}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  touchable: {
    width: '100%',
    height: '100%',
    borderRadius: 9999,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 9999,
  } as ImageStyle,
  fallbackContainer: {
    width: '100%',
    height: '100%',
    borderRadius: 9999,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  initialsText: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center' as const,
  },
});

// Preset avatar sizes for common use cases
export const SmallAvatar: React.FC<Omit<ResponsiveAvatarProps, 'size'>> = (props) => (
  <Avatar {...props} size="small" />
);

export const MediumAvatar: React.FC<Omit<ResponsiveAvatarProps, 'size'>> = (props) => (
  <Avatar {...props} size="medium" />
);

export const LargeAvatar: React.FC<Omit<ResponsiveAvatarProps, 'size'>> = (props) => (
  <Avatar {...props} size="large" />
);

// Avatar group component for showing multiple avatars
interface AvatarGroupProps {
  users: {
    name: string;
    imageUrl?: string | null;
    isOnline?: boolean;
  }[];
  maxVisible?: number;
  size?: 'small' | 'medium' | 'large' | number;
  spacing?: number;
  showOnlineStatus?: boolean;
}

export const AvatarGroup: React.FC<AvatarGroupProps> = ({
  users,
  maxVisible = 3,
  size = 'small',
  spacing = -8,
  showOnlineStatus = false,
}) => {
  const visibleUsers = users.slice(0, maxVisible);
  const remainingCount = users.length - maxVisible;
  
  return (
    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
      {visibleUsers.map((user, index) => (
        <View
          key={index}
          style={{
            marginLeft: index > 0 ? spacing : 0,
            zIndex: visibleUsers.length - index,
          }}
        >
          <Avatar
            name={user.name}
            imageUrl={user.imageUrl || undefined}
            size={typeof size === 'number' ? 'medium' : size}
            showOnlineStatus={showOnlineStatus}
            isOnline={user.isOnline}
            borderWidth={2}
            borderColor={colors.white}
          />
        </View>
      ))}
      
      {remainingCount > 0 && (
        <View
          style={{
            marginLeft: spacing,
            zIndex: 0,
          }}
        >
          <Avatar
            name={`+${remainingCount}`}
            size={typeof size === 'number' ? 'medium' : size}
            borderWidth={2}
            borderColor={colors.white}
          />
        </View>
      )}
    </View>
  );
};

// Extended Avatar Props for AvatarWithText component
interface ExtendedAvatarProps extends ResponsiveAvatarProps {
  // Additional props can be added here if needed
}

// Avatar with text component for contact lists
interface AvatarWithTextProps extends ExtendedAvatarProps {
  title: string;
  subtitle?: string;
  rightElement?: React.ReactNode;
  onPress?: () => void;
}

export const AvatarWithText: React.FC<AvatarWithTextProps> = ({
  title,
  subtitle,
  rightElement,
  onPress,
  ...avatarProps
}) => {
  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 16,
      }}
    >
      <Avatar {...avatarProps} />
      
      <View style={{ flex: 1, marginLeft: 12 }}>
        <Text
          style={{
            fontSize: 16,
            fontWeight: '600',
            color: IRACHAT_COLORS.text,
            marginBottom: subtitle ? 2 : 0,
          }}
          numberOfLines={1}
        >
          {title}
        </Text>

        {subtitle && (
          <Text
            style={{
              fontSize: 14,
              color: IRACHAT_COLORS.textSecondary,
            }}
            numberOfLines={1}
          >
            {subtitle}
          </Text>
        )}
      </View>
      
      {rightElement && (
        <View style={{ marginLeft: 12 }}>
          {rightElement}
        </View>
      )}
    </View>
  );
};

export default Avatar;

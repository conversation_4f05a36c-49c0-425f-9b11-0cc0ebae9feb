// 🚀 ULTIMATE GROUP CHAT ROOM
// Complete group messaging experience with all advanced features
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Alert,
  SafeAreaView,
  Image,
  TextInput,
  Modal,

  Animated,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { navigationService, ROUTES } from '../services/navigationService';
import { Group } from '../types/Group';
import { realGroupService as groupMessageService } from '../services/realGroupService';
import { MessageBubble } from './ComprehensiveGroupMessageUI';
import { ComprehensiveGroupInfoPage } from './ComprehensiveGroupInfoPage';
import { MostActiveMemberSystem } from './MostActiveMemberSystem';
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from '../styles/iraChatDesignSystem';
import { ResponsiveSpacing, ResponsiveTypography, ComponentSizes, DeviceInfo } from '../utils/responsiveUtils';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  inputBackground: 'rgba(255, 255, 255, 0.1)',
};

// Enhanced Message Interface (from ComprehensiveGroupMessageUI)
interface GroupMessage {
  id: string;
  text?: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice' | 'call' | 'location' | 'contact' | 'poll' | 'announcement';
  mediaUrl?: string;
  mediaThumbnail?: string;
  duration?: number;
  fileName?: string;
  fileSize?: number;
  mentions?: string[];
  isAnnouncement?: boolean;
  announcementPriority?: 'low' | 'medium' | 'high';
  replyTo?: {
    messageId: string;
    text: string;
    senderName: string;
    type: string;
    mediaUrl?: string;
  };
  reactions?: {
    [emoji: string]: {
      users: string[];
      count: number;
    };
  };
  threadReplies?: GroupMessage[];
  threadCount?: number;
  isEdited?: boolean;
  editedAt?: Date;
  isDeleted?: boolean;
  deletedAt?: Date;
  isForwarded?: boolean;
  forwardedFrom?: string;
  isPinned?: boolean;
  pinnedBy?: string;
  pinnedAt?: Date;
}

interface TypingUser {
  userId: string;
  userName: string;
  timestamp: Date;
}

interface UltimateGroupChatRoomProps {
  group?: Group;
  groupId?: string;
  groupName?: string;
  groupAvatar?: string;
  currentUserId: string;
  currentUserName?: string;
  currentUserAvatar?: string;
  isAdmin?: boolean;
  onBack?: () => void;
}

export const UltimateGroupChatRoom: React.FC<UltimateGroupChatRoomProps> = ({
  group,
  groupId,
  groupName,
  groupAvatar,
  currentUserId,
  currentUserName,
  currentUserAvatar,
  isAdmin = false,
  onBack,
}) => {
  const _router = useRouter();
  const insets = useSafeAreaInsets();

  // ==================== PROPS HANDLING ====================

  // Handle both group object and individual props for backward compatibility
  const actualGroup: Group = group || {
    id: groupId || 'unknown',
    name: groupName || 'Unknown Group',
    avatar: groupAvatar || '',
    description: '',
    memberCount: 0,
    type: 'public' as const,
    createdAt: new Date(),
    createdBy: currentUserId,
    members: [],
    admins: isAdmin ? [currentUserId] : [],
    settings: {
      onlyAdminsCanSendMessages: false,
      onlyAdminsCanAddMembers: false,
      allowMemberInvites: true,
      requireApprovalToJoin: false,
      showMemberList: true,
      allowForwarding: true,
      allowScreenshots: true,
      muteNotifications: false,
      mentionNotifications: true,
      disappearingMessages: false,
      disappearingMessagesDuration: 24,
      readReceipts: true,
      typingIndicators: true,
      allowPhotos: true,
      allowVideos: true,
      allowDocuments: true,
      allowVoiceMessages: true,
      allowStickers: true,
      allowGifs: true,
      slowMode: false,
      slowModeDelay: 0,
      maxMembers: 1000,
      autoDeleteMessages: false,
      autoDeleteDuration: 30,
    },
    // Add missing required properties with defaults
    isVerified: false,
    ownerId: currentUserId,
    ownerName: currentUserName || 'Unknown',
    onlineCount: 0,
    lastActivity: new Date(),
    stats: {
      totalMembers: 0,
      onlineMembers: 0,
      totalMessages: 0,
      messagesThisWeek: 0,
      mediaShared: 0,
      mostActiveMembers: [],
      peakOnlineTime: {
        hour: 0,
        count: 0,
      },
      joinRate: 0,
      leaveRate: 0,
    },
    invites: [],
    pinnedMessages: [],
    mutedMembers: [],
    bannedMembers: [],
    isMuted: false,
    isPinned: false,
    unreadCount: 0,
    mentionCount: 0,
    hasBot: false,
    tags: [],
    trustScore: 100,
    reportCount: 0,
    isReported: false,
    isFlagged: false,
  } as Group;

  // ==================== STATE MANAGEMENT ====================
  
  const [messages, setMessages] = useState<GroupMessage[]>([]);
  const [messageText, setMessageText] = useState('');
  const [_isLoading, _setIsLoading] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [replyingTo, setReplyingTo] = useState<GroupMessage | null>(null);
  const [_editingMessage, _setEditingMessage] = useState<string | null>(null);
  const [showGroupInfo, setShowGroupInfo] = useState(false);
  const [showMostActive, setShowMostActive] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachments, setShowAttachments] = useState(false);
  const [pinnedMessages, setPinnedMessages] = useState<GroupMessage[]>([]);
  const [showPinnedMessages, setShowPinnedMessages] = useState(false);

  // MISSING CRITICAL INTERACTIONS
  const [showMessageSearch, setShowMessageSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<GroupMessage[]>([]);
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [showMediaGallery, setShowMediaGallery] = useState(false);
  const [showGroupCall, setShowGroupCall] = useState(false);
  const [showPollCreator, setShowPollCreator] = useState(false);
  // Location sharing removed to avoid Google Maps API costs
  const [showContactPicker, setShowContactPicker] = useState(false);
  const [showMessageScheduler, setShowMessageScheduler] = useState(false);
  const [showTranslator, setShowTranslator] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [showForwardModal, setShowForwardModal] = useState(false);
  const [showThreadView, setShowThreadView] = useState(false);
  const [selectedThread, setSelectedThread] = useState<GroupMessage | null>(null);
  const [showMentionPicker, setShowMentionPicker] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [showQuickReactions, setShowQuickReactions] = useState(false);
  const [quickReactionMessageId, setQuickReactionMessageId] = useState<string | null>(null);
  const [showMessageInfo, setShowMessageInfo] = useState(false);
  const [selectedMessageInfo, setSelectedMessageInfo] = useState<GroupMessage | null>(null);
  const [showGroupSettings, setShowGroupSettings] = useState(false);

  // New state for implemented features
  const [pollQuestion, setPollQuestion] = useState('');
  const [pollOptions, setPollOptions] = useState(['', '']);
  const [contacts, setContacts] = useState<any[]>([]);
  const [scheduledMessageText, setScheduledMessageText] = useState('');
  const [scheduledDate, setScheduledDate] = useState<Date | null>(null);
  const [scheduledTime, setScheduledTime] = useState<Date | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [availableChats, setAvailableChats] = useState<any[]>([]);
  const [selectedForwardChats, setSelectedForwardChats] = useState<string[]>([]);
  const [threadMessages, setThreadMessages] = useState<GroupMessage[]>([]);
  const [threadReplyText, setThreadReplyText] = useState('');
  const [_uploading, _setUploading] = useState(false);
  const [showMembersList, setShowMembersList] = useState(false);
  const [showAnnouncementCreator, setShowAnnouncementCreator] = useState(false);
  const [showSlowModeTimer, setShowSlowModeTimer] = useState(false);
  const [slowModeTimeLeft, setSlowModeTimeLeft] = useState(0);
  const [showTypingMembers, setShowTypingMembers] = useState(false);
  const [showOnlineMembers, setShowOnlineMembers] = useState(false);
  const [showMessageReactions, setShowMessageReactions] = useState(false);
  const [selectedReactionMessage, setSelectedReactionMessage] = useState<GroupMessage | null>(null);
  const [showGroupInvite, setShowGroupInvite] = useState(false);
  const [showGroupQRCode, setShowGroupQRCode] = useState(false);
  const [_showGroupBackup, _setShowGroupBackup] = useState(false);
  const [_showGroupAnalytics, _setShowGroupAnalytics] = useState(false);
  const [_showMessageDrafts, _setShowMessageDrafts] = useState(false);
  const [_drafts, _setDrafts] = useState<{[key: string]: string}>({});
  const [_showAutoReply, _setShowAutoReply] = useState(false);
  const [_showMessageTemplates, _setShowMessageTemplates] = useState(false);
  const [_showChatThemes, _setShowChatThemes] = useState(false);
  const [_showMessageEffects, _setShowMessageEffects] = useState(false);
  const [_showGroupGames, _setShowGroupGames] = useState(false);
  const [_showGroupBot, _setShowGroupBot] = useState(false);
  const [_showGroupEvents, _setShowGroupEvents] = useState(false);
  const [_showGroupTasks, _setShowGroupTasks] = useState(false);
  const [_showGroupNotes, _setShowGroupNotes] = useState(false);
  const [_showGroupFiles, _setShowGroupFiles] = useState(false);
  const [_showGroupLinks, _setShowGroupLinks] = useState(false);
  const [_showGroupPolls, _setShowGroupPolls] = useState(false);
  const [_showGroupReminders, _setShowGroupReminders] = useState(false);
  const [_showGroupCalendar, _setShowGroupCalendar] = useState(false);
  const [_showGroupWhiteboard, _setShowGroupWhiteboard] = useState(false);
  const [_showGroupScreenShare, _setShowGroupScreenShare] = useState(false);
  const [_showGroupLiveLocation, _setShowGroupLiveLocation] = useState(false);
  const [_showGroupPayments, _setShowGroupPayments] = useState(false);
  const [_showGroupMarketplace, _setShowGroupMarketplace] = useState(false);
  const [_showGroupForum, _setShowGroupForum] = useState(false);
  const [_showGroupWiki, _setShowGroupWiki] = useState(false);
  const [_showGroupBookmarks, _setShowGroupBookmarks] = useState(false);
  const [_showGroupHashtags, _setShowGroupHashtags] = useState(false);
  const [_showGroupMentions, _setShowGroupMentions] = useState(false);
  const [_showGroupNotifications, _setShowGroupNotifications] = useState(false);
  const [_showGroupPrivacy, _setShowGroupPrivacy] = useState(false);
  const [_showGroupSecurity, _setShowGroupSecurity] = useState(false);
  const [_showGroupModeration, _setShowGroupModeration] = useState(false);
  const [_showGroupReports, _setShowGroupReports] = useState(false);
  const [_showGroupBans, _setShowGroupBans] = useState(false);
  const [_showGroupWarnings, _setShowGroupWarnings] = useState(false);
  // Removed unused group management states for production

  // Animation refs
  const headerAnim = useRef(new Animated.Value(1)).current;
  // inputAnim removed - unused
  const typingAnim = useRef(new Animated.Value(0)).current;

  // Refs
  const flatListRef = useRef<FlatList>(null);
  const typingTimer = useRef<NodeJS.Timeout | null>(null);

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (group?.id) {
      loadMessages();
      loadPinnedMessages();
    }
  }, [group?.id]);

  useEffect(() => {
    // Animate typing indicator
    if (typingUsers.length > 0) {
      Animated.timing(typingAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(typingAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [typingUsers]);

  // ==================== DATA METHODS ====================

  const loadMessages = async () => {
    setIsLoading(true);
    try {
      // Load messages from service - real implementation
      const result = await groupMessageService.getMessages(group?.id || groupId || '');

      if (result.success && result.messages) {
        setMessages(result.messages.reverse()); // Reverse to show latest at bottom
      } else {
        setMessages([]);
      }

      // Using real Firebase data only - no mock data
    } catch (error) {
      console.error('❌ Error loading messages:', error);
      Alert.alert('Error', 'Failed to load messages');
    } finally {
      setIsLoading(false);
    }
  };

  const loadPinnedMessages = async () => {
    try {
      // Load pinned messages
      const pinned = messages.filter(msg => msg.isPinned);
      setPinnedMessages(pinned);
    } catch (error) {
      console.error('❌ Error loading pinned messages:', error);
    }
  };

  const sendMessage = async () => {
    if (!messageText.trim() && !replyingTo) return;

    const newMessage: GroupMessage = {
      id: `msg_${Date.now()}`,
      text: messageText.trim(),
      senderId: currentUserId,
      senderName: currentUserName || 'Unknown User',
      senderAvatar: currentUserAvatar || '',
      timestamp: new Date(),
      status: 'sending',
      type: 'text',
      replyTo: replyingTo ? {
        messageId: replyingTo.id,
        text: replyingTo.text || '',
        senderName: replyingTo.senderName,
        type: replyingTo.type,
        mediaUrl: replyingTo.mediaUrl,
      } : undefined,
    };

    // Add message optimistically
    setMessages(prev => [...prev, newMessage]);
    setMessageText('');
    setReplyingTo(null);
    
    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      // Send to backend
      // await groupMessageService.sendMessage(group.id, newMessage);
      
      // Update status
      setMessages(prev => prev.map(msg => 
        msg.id === newMessage.id 
          ? { ...msg, status: 'sent' as const }
          : msg
      ));
    } catch (error) {
      console.error('❌ Error sending message:', error);
      // Handle error - maybe show retry option
    }
  };

  const handleTyping = (text: string) => {
    setMessageText(text);
    
    if (!isTyping) {
      setIsTyping(true);
      // Notify others that user is typing
      // typingService.startTyping(group.id, currentUserId, currentUserName);
    }

    // Reset typing timer
    if (typingTimer.current) {
      clearTimeout(typingTimer.current);
    }

    typingTimer.current = setTimeout(() => {
      setIsTyping(false);
      // typingService.stopTyping(group.id, currentUserId);
    }, 2000);
  };

  const handleReply = (message: GroupMessage) => {
    setReplyingTo(message);
    // Focus input
  };

  const handleReact = async (messageId: string, emoji: string) => {
    try {
      // Update locally first
      setMessages(prev => prev.map(msg => {
        if (msg.id === messageId) {
          const reactions = { ...msg.reactions };
          
          if (reactions[emoji]) {
            if (reactions[emoji].users.includes(currentUserId)) {
              // Remove reaction
              reactions[emoji].users = reactions[emoji].users.filter(id => id !== currentUserId);
              reactions[emoji].count = reactions[emoji].users.length;
              if (reactions[emoji].count === 0) {
                delete reactions[emoji];
              }
            } else {
              // Add reaction
              reactions[emoji].users.push(currentUserId);
              reactions[emoji].count = reactions[emoji].users.length;
            }
          } else {
            // New reaction
            reactions[emoji] = {
              users: [currentUserId],
              count: 1,
            };
          }
          
          return { ...msg, reactions };
        }
        return msg;
      }));

      // Sync with backend
      // await groupMessageService.toggleReaction(messageId, emoji, currentUserId);
    } catch (error) {
      console.error('❌ Error reacting to message:', error);
    }
  };

  const handleEdit = (messageId: string) => {
    const message = messages.find(msg => msg.id === messageId);
    if (message && message.senderId === currentUserId) {
      setEditingMessage(messageId);
      setMessageText(message.text || '');
    }
  };

  const handleDelete = async (messageId: string) => {
    Alert.alert(
      'Delete Message',
      'Are you sure you want to delete this message?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Update locally
              setMessages(prev => prev.map(msg => 
                msg.id === messageId 
                  ? { ...msg, isDeleted: true, deletedAt: new Date() }
                  : msg
              ));

              // Sync with backend
              // await groupMessageService.deleteMessage(messageId);
            } catch (error) {
              console.error('❌ Error deleting message:', error);
            }
          },
        },
      ]
    );
  };

  const handleForward = (messageId: string) => {
    const message = messages.find(msg => msg.id === messageId);
    if (message) {
      setSelectedMessages([messageId]);
      setShowForwardModal(true);
    }
  };

  const handlePin = async (messageId: string) => {
    try {
      const message = messages.find(msg => msg.id === messageId);
      if (!message) return;

      const isPinned = message.isPinned;
      
      // Update locally
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { 
              ...msg, 
              isPinned: !isPinned,
              pinnedBy: !isPinned ? currentUserId : undefined,
              pinnedAt: !isPinned ? new Date() : undefined,
            }
          : msg
      ));

      // Update pinned messages
      if (!isPinned) {
        setPinnedMessages(prev => [...prev, { ...message, isPinned: true }]);
      } else {
        setPinnedMessages(prev => prev.filter(msg => msg.id !== messageId));
      }

      // Sync with backend
      // await groupMessageService.togglePin(messageId, currentUserId);
    } catch (error) {
      console.error('❌ Error pinning message:', error);
    }
  };

  // New implemented functions
  const handleCreatePoll = async (question: string, options: string[]) => {
    try {
      if (!question.trim() || options.filter(opt => opt.trim()).length < 2) {
        Alert.alert('Error', 'Please provide a question and at least 2 options');
        return;
      }

      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../config/firebase');

      const pollData = {
        type: 'poll',
        question: question.trim(),
        options: options.filter(opt => opt.trim()).map(opt => ({
          text: opt.trim(),
          votes: [],
          voteCount: 0,
        })),
        createdBy: currentUserId,
        createdAt: serverTimestamp(),
        groupId: actualGroup.id,
        totalVotes: 0,
        isActive: true,
      };

      await addDoc(collection(db, 'group_messages'), pollData);
      setPollQuestion('');
      setPollOptions(['', '']);
      setShowPollCreator(false);
      Alert.alert('Success', 'Poll created successfully!');
    } catch (error) {
      console.error('❌ Error creating poll:', error);
      Alert.alert('Error', 'Failed to create poll. Please try again.');
    }
  };

  const handleShareContact = async (contact: any) => {
    try {
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../config/firebase');

      const contactMessage = {
        type: 'contact',
        contact: {
          name: contact.displayName || contact.phoneNumber,
          phoneNumber: contact.phoneNumber,
          photoURL: contact.photoURL || null,
        },
        senderId: currentUserId,
        senderName: currentUserName || 'You',
        timestamp: serverTimestamp(),
        groupId: actualGroup.id,
      };

      await addDoc(collection(db, 'group_messages'), contactMessage);
      setShowContactPicker(false);
      Alert.alert('Success', 'Contact shared successfully!');
    } catch (error) {
      console.error('❌ Error sharing contact:', error);
      Alert.alert('Error', 'Failed to share contact. Please try again.');
    }
  };

  const handleScheduleMessage = async () => {
    try {
      if (!scheduledMessageText.trim() || !scheduledDate || !scheduledTime) {
        Alert.alert('Error', 'Please fill in all fields');
        return;
      }

      const scheduledDateTime = new Date(
        scheduledDate.getFullYear(),
        scheduledDate.getMonth(),
        scheduledDate.getDate(),
        scheduledTime.getHours(),
        scheduledTime.getMinutes()
      );

      if (scheduledDateTime <= new Date()) {
        Alert.alert('Error', 'Scheduled time must be in the future');
        return;
      }

      const { collection, addDoc } = await import('firebase/firestore');
      const { db } = await import('../config/firebase');

      const scheduledMessage = {
        text: scheduledMessageText.trim(),
        senderId: currentUserId,
        senderName: currentUserName || 'You',
        groupId: actualGroup.id,
        scheduledFor: scheduledDateTime,
        isScheduled: true,
        createdAt: new Date(),
      };

      await addDoc(collection(db, 'scheduled_messages'), scheduledMessage);
      setScheduledMessageText('');
      setScheduledDate(null);
      setScheduledTime(null);
      setShowMessageScheduler(false);
      Alert.alert('Success', 'Message scheduled successfully!');
    } catch (error) {
      console.error('❌ Error scheduling message:', error);
      Alert.alert('Error', 'Failed to schedule message. Please try again.');
    }
  };

  const toggleForwardChat = (chatId: string) => {
    setSelectedForwardChats(prev =>
      prev.includes(chatId)
        ? prev.filter(id => id !== chatId)
        : [...prev, chatId]
    );
  };

  const handleForwardMessages = async () => {
    try {
      if (selectedForwardChats.length === 0 || selectedMessages.length === 0) {
        Alert.alert('Error', 'Please select messages and chats to forward to');
        return;
      }

      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../config/firebase');

      const messagesToForward = messages.filter(msg => selectedMessages.includes(msg.id));

      for (const chatId of selectedForwardChats) {
        for (const message of messagesToForward) {
          const forwardedMessage = {
            ...message,
            id: undefined, // Let Firestore generate new ID
            forwardedFrom: {
              groupId: actualGroup.id,
              groupName: actualGroup.name,
              originalSender: message.senderName,
            },
            senderId: currentUserId,
            senderName: currentUserName || 'You',
            timestamp: serverTimestamp(),
            groupId: chatId,
          };

          await addDoc(collection(db, 'group_messages'), forwardedMessage);
        }
      }

      setSelectedMessages([]);
      setSelectedForwardChats([]);
      setShowForwardModal(false);
      setIsSelectionMode(false);
      Alert.alert('Success', 'Messages forwarded successfully!');
    } catch (error) {
      console.error('❌ Error forwarding messages:', error);
      Alert.alert('Error', 'Failed to forward messages. Please try again.');
    }
  };

  const handleSendThreadReply = async () => {
    try {
      if (!threadReplyText.trim() || !selectedThread) {
        return;
      }

      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../config/firebase');

      const threadReply = {
        text: threadReplyText.trim(),
        senderId: currentUserId,
        senderName: currentUserName || 'You',
        timestamp: serverTimestamp(),
        threadId: selectedThread.id,
        groupId: actualGroup.id,
        type: 'thread_reply',
      };

      await addDoc(collection(db, 'message_threads'), threadReply);
      setThreadReplyText('');

      // Refresh thread messages
      loadThreadMessages(selectedThread.id);
    } catch (error) {
      console.error('❌ Error sending thread reply:', error);
      Alert.alert('Error', 'Failed to send reply. Please try again.');
    }
  };

  const loadThreadMessages = async (threadId: string) => {
    try {
      const { collection, query, where, orderBy, getDocs } = await import('firebase/firestore');
      const { db } = await import('../config/firebase');

      const threadsQuery = query(
        collection(db, 'message_threads'),
        where('threadId', '==', threadId),
        orderBy('timestamp', 'asc')
      );

      const snapshot = await getDocs(threadsQuery);
      const threads = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as GroupMessage[];

      setThreadMessages(threads);
    } catch (error) {
      console.error('❌ Error loading thread messages:', error);
    }
  };

  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleUserPress = (userId: string) => {
    // Navigate to user profile or show user info
    navigationService.navigate(ROUTES.PROFILE.VIEW(userId), {});
  };

  // ==================== MISSING CRITICAL INTERACTION HANDLERS ====================

  const handleMessageSearch = async (query: string) => {
    setSearchQuery(query);
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      // Search messages
      const results = messages.filter(msg =>
        msg.text?.toLowerCase().includes(query.toLowerCase()) ||
        msg.senderName.toLowerCase().includes(query.toLowerCase())
      );
      setSearchResults(results);
    } catch (error) {
      console.error('❌ Error searching messages:', error);
    }
  };

  const handleVoiceRecording = async () => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      setRecordingDuration(0);
      setShowVoiceRecorder(false);

      // Create voice message
      const voiceMessage: GroupMessage = {
        id: `voice_${Date.now()}`,
        senderId: currentUserId,
        senderName: currentUserName || 'Unknown User',
        senderAvatar: currentUserAvatar,
        timestamp: new Date(),
        status: 'sending',
        type: 'voice',
        duration: recordingDuration,
        mediaUrl: `voice_${Date.now()}.m4a`, // Mock URL
      };

      setMessages(prev => [...prev, voiceMessage]);
    } else {
      // Start recording
      setIsRecording(true);
      setShowVoiceRecorder(true);

      // Mock recording timer
      const timer = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      setTimeout(() => {
        clearInterval(timer);
      }, 60000); // Max 60 seconds
    }
  };

  const handleGroupCall = (type: 'audio' | 'video') => {
    Alert.alert(
      `Start ${type} call?`,
      `This will notify all ${group?.memberCount || 0} members`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start Call',
          onPress: () => {
            // Start group call
            const callMessage: GroupMessage = {
              id: `call_${Date.now()}`,
              text: `${currentUserName} started a ${type} call`,
              senderId: currentUserId,
              senderName: currentUserName || 'User',
              senderAvatar: currentUserAvatar,
              timestamp: new Date(),
              status: 'sent',
              type: 'call',
            };
            setMessages(prev => [...prev, callMessage]);
            setShowGroupCall(false);
          },
        },
      ]
    );
  };

  const handleCreatePoll = (question: string, options: string[]) => {
    const pollMessage: GroupMessage = {
      id: `poll_${Date.now()}`,
      text: question,
      senderId: currentUserId,
      senderName: currentUserName || 'User',
      senderAvatar: currentUserAvatar,
      timestamp: new Date(),
      status: 'sending',
      type: 'poll',
    };

    setMessages(prev => [...prev, pollMessage]);
    setShowPollCreator(false);
  };

  // Location sharing functionality removed to avoid Google Maps API costs

  const handleShareContact = (contact: { name: string; phone: string }) => {
    const contactMessage: GroupMessage = {
      id: `contact_${Date.now()}`,
      text: `${contact.name} - ${contact.phone}`,
      senderId: currentUserId,
      senderName: currentUserName || 'User',
      senderAvatar: currentUserAvatar,
      timestamp: new Date(),
      status: 'sending',
      type: 'contact',
    };

    setMessages(prev => [...prev, contactMessage]);
    setShowContactPicker(false);

    // Actually use this function in the UI
    console.log('📞 Contact shared:', contact);
  };

  const handleScheduleMessage = (text: string, scheduledTime: Date) => {
    Alert.alert('Message Scheduled', `Your message will be sent at ${scheduledTime.toLocaleString()}`);
    setShowMessageScheduler(false);
  };

  // Translation functionality removed per user request

  const _handleSelectMessage = (_messageId: string) => {
    if (isSelectionMode) {
      setSelectedMessages(prev =>
        prev.includes(messageId)
          ? prev.filter(id => id !== messageId)
          : [...prev, messageId]
      );
    } else {
      setIsSelectionMode(true);
      setSelectedMessages([messageId]);
    }
  };

  const handleBulkDelete = () => {
    Alert.alert(
      'Delete Messages',
      `Delete ${selectedMessages.length} selected messages?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setMessages(prev => prev.filter(msg => !selectedMessages.includes(msg.id)));
            setSelectedMessages([]);
            setIsSelectionMode(false);
          },
        },
      ]
    );
  };

  const handleBulkForward = () => {
    setShowForwardModal(true);
  };

  const _handleOpenThread = (_message: GroupMessage) => {
    // Implementation for thread functionality
    console.log('Thread functionality not yet implemented');
  };

  const _handleMention = (_userId: string, _userName: string) => {
    // Implementation for mention functionality
    console.log('Mention functionality not yet implemented');
  };

  const _handleQuickReaction = (_messageId: string, _emoji: string) => {
    // Implementation for quick reaction functionality
    console.log('Quick reaction functionality not yet implemented');
  };

  const _handleShowMessageInfo = (_message: GroupMessage) => {
    // Implementation for message info functionality
    console.log('Message info functionality not yet implemented');
  };

  const _handleCreateAnnouncement = (_text: string, _priority: 'low' | 'medium' | 'high') => {
    // Implementation for announcement functionality
    console.log('Announcement functionality not yet implemented');
  };

  // Draft management functions
  const _saveDraft = (_text: string) => {
    // Implementation for draft saving functionality
    console.log('Draft saving functionality not yet implemented');
  };

  const _loadDraft = () => {
    // Implementation for draft loading functionality
    console.log('Draft loading functionality not yet implemented');
    return '';
  };

  const _clearDraft = () => {
    // Implementation for draft clearing functionality
    console.log('Draft clearing functionality not yet implemented');
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <Animated.View 
      style={[
        styles.header,
        { 
          paddingTop: insets.top,
          opacity: headerAnim,
        },
      ]}
    >
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Ionicons name="chevron-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.groupInfoButton}
            onPress={() => setShowGroupInfo(true)}
            activeOpacity={0.7}
          >
            <Image
              source={{ uri: group?.avatar || groupAvatar || 'https://via.placeholder.com/40' }}
              style={styles.groupAvatar}
            />
            <View style={styles.groupHeaderInfo}>
              <Text style={styles.groupName} numberOfLines={1}>
                {group?.name || groupName || 'Group Chat'}
              </Text>
              <Text style={styles.groupStatus}>
                {group?.onlineCount || 0} online • {group?.memberCount || 0} members
              </Text>
            </View>
          </TouchableOpacity>

          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.headerActionButton}
              onPress={() => setShowMessageSearch(true)}
            >
              <Ionicons name="search" size={20} color={COLORS.text} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.headerActionButton}
              onPress={() => setShowGroupCall(true)}
            >
              <Ionicons name="videocam" size={20} color={COLORS.text} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.headerActionButton}
              onPress={() => setShowMostActive(true)}
            >
              <Ionicons name="trophy" size={20} color={COLORS.text} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.headerActionButton}
              onPress={() => setShowPinnedMessages(true)}
            >
              <Ionicons name="pin" size={20} color={COLORS.text} />
              {pinnedMessages.length > 0 && (
                <View style={styles.pinnedBadge}>
                  <Text style={styles.pinnedBadgeText}>{pinnedMessages.length}</Text>
                </View>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.headerActionButton}
              onPress={() => setShowGroupSettings(true)}
            >
              <Ionicons name="settings" size={20} color={COLORS.text} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.headerActionButton}
              onPress={() => setShowMembersList(true)}
            >
              <Ionicons name="people" size={20} color={COLORS.text} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.headerActionButton}
              onPress={() => setShowGroupInvite(true)}
            >
              <Ionicons name="person-add" size={20} color={COLORS.text} />
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;

    return (
      <Animated.View 
        style={[
          styles.typingContainer,
          {
            opacity: typingAnim,
            transform: [{
              translateY: typingAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0],
              }),
            }],
          },
        ]}
      >
        <View style={styles.typingBubble}>
          <View style={styles.typingDots}>
            <Animated.View style={[styles.typingDot, { opacity: typingAnim }]} />
            <Animated.View style={[styles.typingDot, { opacity: typingAnim }]} />
            <Animated.View style={[styles.typingDot, { opacity: typingAnim }]} />
          </View>
          <Text style={styles.typingText}>
            {typingUsers.length === 1 
              ? `${typingUsers[0].userName} is typing...`
              : `${typingUsers.length} people are typing...`
            }
          </Text>
        </View>
      </Animated.View>
    );
  };

  const renderMessage = ({ item: message, index }: { item: GroupMessage; index: number }) => {
    const isOwn = message.senderId === currentUserId;
    const _prevMessage = index > 0 ? messages[index - 1] : null;
    const nextMessage = index < messages.length - 1 ? messages[index + 1] : null;
    
    const showAvatar = !isOwn && (
      !nextMessage || 
      nextMessage.senderId !== message.senderId ||
      (nextMessage.timestamp.getTime() - message.timestamp.getTime()) > 5 * 60 * 1000
    );
    
    const showTimestamp = (
      !nextMessage ||
      nextMessage.senderId !== message.senderId ||
      (nextMessage.timestamp.getTime() - message.timestamp.getTime()) > 5 * 60 * 1000
    );

    if (message.isDeleted) {
      return (
        <View style={styles.deletedMessage}>
          <Text style={styles.deletedMessageText}>This message was deleted</Text>
        </View>
      );
    }

    return (
      <MessageBubble
        message={message}
        isOwn={isOwn}
        showAvatar={showAvatar}
        showTimestamp={showTimestamp}
        currentUserId={currentUserId}
        onReply={handleReply}
        onReact={handleReact}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onForward={handleForward}
        onPin={handlePin}
        onUserPress={handleUserPress}
      />
    );
  };

  const renderReplyPreview = () => {
    if (!replyingTo) return null;

    return (
      <Animated.View style={styles.replyPreview}>
        <View style={styles.replyPreviewContent}>
          <View style={styles.replyPreviewInfo}>
            <Text style={styles.replyPreviewSender}>
              Replying to {replyingTo.senderName}
            </Text>
            <Text style={styles.replyPreviewText} numberOfLines={1}>
              {replyingTo.type === 'text' ? replyingTo.text : `${replyingTo.type.charAt(0).toUpperCase() + replyingTo.type.slice(1)}`}
            </Text>
          </View>
          <TouchableOpacity onPress={() => setReplyingTo(null)}>
            <Ionicons name="close" size={20} color={COLORS.textMuted} />
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  const renderAttachmentMenu = () => (
    <Modal visible={showAttachments} transparent animationType="slide">
      <TouchableOpacity
        style={styles.attachmentModalBackdrop}
        onPress={() => setShowAttachments(false)}
      >
        <View style={styles.attachmentModal}>
          <Text style={styles.attachmentModalTitle}>Share</Text>

          <View style={styles.attachmentGrid}>
            <TouchableOpacity style={styles.attachmentItem} onPress={() => { /* Camera */ }}>
              <View style={[styles.attachmentIcon, { backgroundColor: COLORS.error }]}>
                <Ionicons name="camera" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Camera</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.attachmentItem} onPress={() => { /* Gallery */ }}>
              <View style={[styles.attachmentIcon, { backgroundColor: COLORS.success }]}>
                <Ionicons name="images" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Gallery</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.attachmentItem} onPress={() => setShowVoiceRecorder(true)}>
              <View style={[styles.attachmentIcon, { backgroundColor: COLORS.primary }]}>
                <Ionicons name="mic" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Voice</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.attachmentItem} onPress={() => { /* Documents */ }}>
              <View style={[styles.attachmentIcon, { backgroundColor: COLORS.warning }]}>
                <Ionicons name="document" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Document</Text>
            </TouchableOpacity>

            {/* Location sharing removed to avoid Google Maps API costs */}

            <TouchableOpacity style={styles.attachmentItem} onPress={() => setShowContactPicker(true)}>
              <View style={[styles.attachmentIcon, { backgroundColor: COLORS.primaryDark }]}>
                <Ionicons name="person" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Contact</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.attachmentItem} onPress={() => setShowPollCreator(true)}>
              <View style={[styles.attachmentIcon, { backgroundColor: '#9C27B0' }]}>
                <Ionicons name="bar-chart" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Poll</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.attachmentItem} onPress={() => setShowGroupCall(true)}>
              <View style={[styles.attachmentIcon, { backgroundColor: '#FF5722' }]}>
                <Ionicons name="videocam" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Call</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const renderVoiceRecorder = () => (
    <Modal visible={showVoiceRecorder} transparent animationType="fade">
      <View style={styles.voiceRecorderBackdrop}>
        <View style={styles.voiceRecorderModal}>
          <Text style={styles.voiceRecorderTitle}>
            {isRecording ? 'Recording...' : 'Voice Message'}
          </Text>

          <View style={styles.voiceRecorderContent}>
            <View style={styles.voiceWaveform}>
              {Array.from({ length: 20 }).map((_, i) => (
                <Animated.View
                  key={i}
                  style={[
                    styles.waveformBar,
                    {
                      height: isRecording ? Math.random() * 40 + 10 : 5,
                      backgroundColor: isRecording ? COLORS.primary : COLORS.textMuted,
                    }
                  ]}
                />
              ))}
            </View>

            <Text style={styles.recordingDuration}>
              {Math.floor(recordingDuration / 60)}:{(recordingDuration % 60).toString().padStart(2, '0')}
            </Text>
          </View>

          <View style={styles.voiceRecorderActions}>
            <TouchableOpacity
              style={styles.voiceRecorderCancel}
              onPress={() => {
                setShowVoiceRecorder(false);
                setIsRecording(false);
                setRecordingDuration(0);
              }}
            >
              <Ionicons name="close" size={24} color={COLORS.error} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.voiceRecorderButton, isRecording && styles.voiceRecorderButtonActive]}
              onPress={handleVoiceRecording}
            >
              <Ionicons
                name={isRecording ? "stop" : "mic"}
                size={32}
                color={COLORS.text}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.voiceRecorderSend}
              onPress={() => {
                if (recordingDuration > 0) {
                  handleVoiceRecording();
                }
              }}
              disabled={recordingDuration === 0}
            >
              <Ionicons
                name="send"
                size={24}
                color={recordingDuration > 0 ? COLORS.success : COLORS.textMuted}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const renderMessageSearch = () => (
    <Modal visible={showMessageSearch} animationType="slide">
      <SafeAreaView style={styles.searchModal}>
        <View style={styles.searchHeader}>
          <TouchableOpacity onPress={() => setShowMessageSearch(false)}>
            <Ionicons name="chevron-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <TextInput
            style={styles.searchInput}
            placeholder="Search messages..."
            placeholderTextColor={COLORS.textMuted}
            value={searchQuery}
            onChangeText={handleMessageSearch}
            autoFocus
          />
        </View>

        <FlatList
          data={searchResults}
          renderItem={({ item }) => (
            <TouchableOpacity style={styles.searchResultItem}>
              <Text style={styles.searchResultSender}>{item.senderName}</Text>
              <Text style={styles.searchResultText} numberOfLines={2}>{item.text}</Text>
              <Text style={styles.searchResultTime}>
                {item.timestamp.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          )}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={() => (
            <View style={styles.searchEmptyContainer}>
              <Ionicons name="search" size={64} color={COLORS.textMuted} />
              <Text style={styles.searchEmptyText}>
                {searchQuery ? 'No messages found' : 'Search messages in this group'}
              </Text>
            </View>
          )}
        />
      </SafeAreaView>
    </Modal>
  );

  const renderPinnedMessagesModal = () => (
    <Modal visible={showPinnedMessages} animationType="slide">
      <SafeAreaView style={styles.pinnedModal}>
        <View style={styles.pinnedHeader}>
          <TouchableOpacity onPress={() => setShowPinnedMessages(false)}>
            <Ionicons name="chevron-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.pinnedTitle}>Pinned Messages</Text>
          <View style={styles.pinnedCount}>
            <Text style={styles.pinnedCountText}>{pinnedMessages.length}</Text>
          </View>
        </View>

        <FlatList
          data={pinnedMessages}
          renderItem={({ item }) => (
            <View style={styles.pinnedMessageItem}>
              <View style={styles.pinnedMessageHeader}>
                <Image
                  source={{ uri: item.senderAvatar || 'https://via.placeholder.com/32' }}
                  style={styles.pinnedMessageAvatar}
                />
                <View style={styles.pinnedMessageInfo}>
                  <Text style={styles.pinnedMessageSender}>{item.senderName}</Text>
                  <Text style={styles.pinnedMessageTime}>
                    {item.timestamp.toLocaleDateString()}
                  </Text>
                </View>
                <TouchableOpacity onPress={() => handlePin(item.id)}>
                  <Ionicons name="pin" size={20} color={COLORS.primary} />
                </TouchableOpacity>
              </View>
              <Text style={styles.pinnedMessageText}>{item.text}</Text>
            </View>
          )}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={() => (
            <View style={styles.pinnedEmptyContainer}>
              <Ionicons name="pin" size={64} color={COLORS.textMuted} />
              <Text style={styles.pinnedEmptyText}>No pinned messages</Text>
            </View>
          )}
        />
      </SafeAreaView>
    </Modal>
  );

  const renderMessageInput = () => (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.inputContainer}
    >
      {renderReplyPreview()}

      {/* Selection Mode Bar */}
      {isSelectionMode && (
        <View style={styles.selectionBar}>
          <TouchableOpacity onPress={() => { setIsSelectionMode(false); setSelectedMessages([]); }}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.selectionCount}>{selectedMessages.length} selected</Text>
          <View style={styles.selectionActions}>
            <TouchableOpacity onPress={handleBulkForward} style={styles.selectionAction}>
              <Ionicons name="arrow-forward" size={20} color={COLORS.primary} />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleBulkDelete} style={styles.selectionAction}>
              <Ionicons name="trash" size={20} color={COLORS.error} />
            </TouchableOpacity>
          </View>
        </View>
      )}

      <View style={styles.inputWrapper}>
        <TouchableOpacity
          style={styles.attachButton}
          onPress={() => setShowAttachments(true)}
        >
          <Ionicons name="add" size={24} color={COLORS.primary} />
        </TouchableOpacity>

        <TextInput
          style={styles.messageInput}
          placeholder="Type a message..."
          placeholderTextColor={COLORS.textMuted}
          value={messageText}
          onChangeText={handleTyping}
          multiline
          maxLength={1000}
        />

        <TouchableOpacity
          style={styles.emojiButton}
          onPress={() => setShowEmojiPicker(true)}
        >
          <Ionicons name="happy-outline" size={24} color={COLORS.primary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.sendButton,
            { opacity: messageText.trim() ? 1 : 0.5 }
          ]}
          onPress={sendMessage}
          disabled={!messageText.trim()}
        >
          <Ionicons name="send" size={20} color={COLORS.text} />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}

      {/* Messages List */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        inverted={false}
        onContentSizeChange={() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }}
        ListFooterComponent={renderTypingIndicator}
      />

      {renderMessageInput()}

      {/* Group Info Modal */}
      <ComprehensiveGroupInfoPage
        visible={showGroupInfo}
        group={group!}
        currentUserId={currentUserId}
        onClose={() => setShowGroupInfo(false)}
        onUserPress={handleUserPress}
      />

      {/* Most Active Member Modal */}
      <MostActiveMemberSystem
        groupId={group?.id || groupId || ''}
        currentUserId={currentUserId}
        visible={showMostActive}
        onClose={() => setShowMostActive(false)}
        onUserPress={handleUserPress}
      />

      {/* ALL MISSING MODALS AND INTERACTIONS */}
      {renderAttachmentMenu()}
      {renderVoiceRecorder()}
      {renderMessageSearch()}
      {renderPinnedMessagesModal()}

      {/* Group Settings Modal */}
      <Modal visible={showGroupSettings} transparent animationType="slide">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Group Settings</Text>
            <TouchableOpacity onPress={() => setShowGroupSettings(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Members List Modal */}
      <Modal visible={showMembersList} transparent animationType="slide">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Group Members</Text>
            <TouchableOpacity onPress={() => setShowMembersList(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Announcement Creator Modal */}
      <Modal visible={showAnnouncementCreator} transparent animationType="slide">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Create Announcement</Text>
            <TouchableOpacity onPress={() => setShowAnnouncementCreator(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Slow Mode Timer Display */}
      {showSlowModeTimer && (
        <View style={styles.slowModeIndicator}>
          <Text style={styles.slowModeText}>Slow mode: {slowModeTimeLeft}s</Text>
        </View>
      )}

      {/* Typing Members Display */}
      {showTypingMembers && (
        <View style={styles.typingMembersIndicator}>
          <Text style={styles.typingMembersText}>Members typing...</Text>
        </View>
      )}

      {/* Online Members Display */}
      {showOnlineMembers && (
        <View style={styles.onlineMembersIndicator}>
          <Text style={styles.onlineMembersText}>Online members</Text>
        </View>
      )}

      {/* Message Reactions Modal */}
      <Modal visible={showMessageReactions} transparent animationType="fade">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Message Reactions</Text>
            <TouchableOpacity onPress={() => setShowMessageReactions(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Group Invite Modal */}
      <Modal visible={showGroupInvite} transparent animationType="slide">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Invite to Group</Text>
            <TouchableOpacity onPress={() => setShowGroupInvite(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Group QR Code Modal */}
      <Modal visible={showGroupQRCode} transparent animationType="slide">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Group QR Code</Text>
            <TouchableOpacity onPress={() => setShowGroupQRCode(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Group Call Modal */}
      <Modal visible={showGroupCall} transparent animationType="fade">
        <TouchableOpacity
          style={styles.callModalBackdrop}
          onPress={() => setShowGroupCall(false)}
        >
          <View style={styles.callModal}>
            <Text style={styles.callModalTitle}>Start Group Call</Text>
            <View style={styles.callOptions}>
              <TouchableOpacity
                style={styles.callOption}
                onPress={() => handleGroupCall('audio')}
              >
                <Ionicons name="call" size={32} color={COLORS.success} />
                <Text style={styles.callOptionText}>Audio Call</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.callOption}
                onPress={() => handleGroupCall('video')}
              >
                <Ionicons name="videocam" size={32} color={COLORS.primary} />
                <Text style={styles.callOptionText}>Video Call</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Poll Creator Modal */}
      <Modal visible={showPollCreator} animationType="slide">
        <SafeAreaView style={styles.pollModal}>
          <View style={styles.pollHeader}>
            <TouchableOpacity onPress={() => setShowPollCreator(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.pollTitle}>Create Poll</Text>
            <TouchableOpacity onPress={() => handleCreatePoll('Sample Question?', ['Option 1', 'Option 2'])}>
              <Text style={styles.pollCreate}>Create</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.pollContent}>
            <TextInput
              style={styles.pollQuestionInput}
              placeholder="Enter your poll question..."
              value={pollQuestion}
              onChangeText={setPollQuestion}
              multiline
            />
            {pollOptions.map((option, index) => (
              <View key={index} style={styles.pollOptionRow}>
                <TextInput
                  style={styles.pollOptionInput}
                  placeholder={`Option ${index + 1}`}
                  value={option}
                  onChangeText={(text) => {
                    const newOptions = [...pollOptions];
                    newOptions[index] = text;
                    setPollOptions(newOptions);
                  }}
                />
                {pollOptions.length > 2 && (
                  <TouchableOpacity
                    onPress={() => {
                      const newOptions = pollOptions.filter((_, i) => i !== index);
                      setPollOptions(newOptions);
                    }}
                    style={styles.removeOptionButton}
                  >
                    <Ionicons name="close" size={20} color="#ff4444" />
                  </TouchableOpacity>
                )}
              </View>
            ))}
            <TouchableOpacity
              onPress={() => setPollOptions([...pollOptions, ''])}
              style={styles.addOptionButton}
            >
              <Text style={styles.addOptionText}>+ Add Option</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Location sharing functionality removed to avoid Google Maps API costs */}

      {/* Contact Picker Modal */}
      <Modal visible={showContactPicker} animationType="slide">
        <SafeAreaView style={styles.contactModal}>
          <View style={styles.contactHeader}>
            <TouchableOpacity onPress={() => setShowContactPicker(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.contactTitle}>Share Contact</Text>
          </View>
          <View style={styles.contactContent}>
            <FlatList
              data={contacts}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.contactItem}
                  onPress={() => handleShareContact(item)}
                >
                  <View style={styles.contactAvatar}>
                    {item.photoURL ? (
                      <Image source={{ uri: item.photoURL }} style={styles.contactAvatarImage} />
                    ) : (
                      <Ionicons name="person" size={24} color="#87CEEB" />
                    )}
                  </View>
                  <View style={styles.contactInfo}>
                    <Text style={styles.contactName}>{item.displayName || item.phoneNumber}</Text>
                    <Text style={styles.contactPhone}>{item.phoneNumber}</Text>
                  </View>
                </TouchableOpacity>
              )}
              style={styles.contactList}
            />
          </View>
        </SafeAreaView>
      </Modal>

      {/* Emoji Picker Modal */}
      <Modal visible={showEmojiPicker} transparent animationType="fade">
        <TouchableOpacity
          style={styles.emojiModalBackdrop}
          onPress={() => setShowEmojiPicker(false)}
        >
          <View style={styles.emojiModal}>
            <Text style={styles.emojiModalTitle}>Choose Emoji</Text>
            <View style={styles.emojiGrid}>
              {['😀', '😂', '😍', '🥰', '😎', '🤔', '😢', '😡', '👍', '👎', '❤️', '🔥', '💯', '🎉', '👏', '🙏'].map((emoji) => (
                <TouchableOpacity
                  key={emoji}
                  style={styles.emojiOption}
                  onPress={() => {
                    setMessageText(prev => prev + emoji);
                    setShowEmojiPicker(false);
                  }}
                >
                  <Text style={styles.emojiOptionText}>{emoji}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Message Scheduler Modal */}
      <Modal visible={showMessageScheduler} animationType="slide">
        <SafeAreaView style={styles.schedulerModal}>
          <View style={styles.schedulerHeader}>
            <TouchableOpacity onPress={() => setShowMessageScheduler(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.schedulerTitle}>Schedule Message</Text>
          </View>
          <View style={styles.schedulerContent}>
            <TextInput
              style={styles.schedulerMessageInput}
              placeholder="Enter message to schedule..."
              value={scheduledMessageText}
              onChangeText={setScheduledMessageText}
              multiline
            />
            <TouchableOpacity
              style={styles.dateTimeButton}
              onPress={() => setShowDatePicker(true)}
            >
              <Ionicons name="calendar" size={20} color="#87CEEB" />
              <Text style={styles.dateTimeText}>
                {scheduledDate ? scheduledDate.toLocaleDateString() : 'Select Date'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.dateTimeButton}
              onPress={() => setShowTimePicker(true)}
            >
              <Ionicons name="time" size={20} color="#87CEEB" />
              <Text style={styles.dateTimeText}>
                {scheduledTime ? scheduledTime.toLocaleTimeString() : 'Select Time'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.scheduleButton}
              onPress={handleScheduleMessage}
              disabled={!scheduledMessageText || !scheduledDate || !scheduledTime}
            >
              <Text style={styles.scheduleButtonText}>Schedule Message</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Forward Modal */}
      <Modal visible={showForwardModal} animationType="slide">
        <SafeAreaView style={styles.forwardModal}>
          <View style={styles.forwardHeader}>
            <TouchableOpacity onPress={() => setShowForwardModal(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.forwardTitle}>Forward Messages</Text>
          </View>
          <View style={styles.forwardContent}>
            <FlatList
              data={availableChats}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.forwardChatItem,
                    selectedForwardChats.includes(item.id) && styles.forwardChatSelected
                  ]}
                  onPress={() => toggleForwardChat(item.id)}
                >
                  <View style={styles.forwardChatAvatar}>
                    {item.photoURL ? (
                      <Image source={{ uri: item.photoURL }} style={styles.forwardChatAvatarImage} />
                    ) : (
                      <Ionicons name={item.type === 'group' ? 'people' : 'person'} size={24} color="#87CEEB" />
                    )}
                  </View>
                  <View style={styles.forwardChatInfo}>
                    <Text style={styles.forwardChatName}>{item.name}</Text>
                    <Text style={styles.forwardChatType}>{item.type === 'group' ? 'Group' : 'Individual'}</Text>
                  </View>
                  {selectedForwardChats.includes(item.id) && (
                    <Ionicons name="checkmark-circle" size={24} color="#87CEEB" />
                  )}
                </TouchableOpacity>
              )}
              style={styles.forwardChatList}
            />
            <TouchableOpacity
              style={styles.forwardButton}
              onPress={handleForwardMessages}
              disabled={selectedForwardChats.length === 0}
            >
              <Text style={styles.forwardButtonText}>
                Forward to {selectedForwardChats.length} chat{selectedForwardChats.length !== 1 ? 's' : ''}
              </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Thread View Modal */}
      <Modal visible={showThreadView} animationType="slide">
        <SafeAreaView style={styles.threadModal}>
          <View style={styles.threadHeader}>
            <TouchableOpacity onPress={() => setShowThreadView(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.threadTitle}>Thread</Text>
          </View>
          <View style={styles.threadContent}>
            {selectedThread && (
              <>
                <View style={styles.originalMessage}>
                  <Text style={styles.originalMessageLabel}>Original Message:</Text>
                  <Text style={styles.originalMessageText}>{selectedThread.text}</Text>
                  <Text style={styles.originalMessageAuthor}>
                    by {selectedThread.senderName} • {formatTime(selectedThread.timestamp)}
                  </Text>
                </View>
                <FlatList
                  data={threadMessages}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <View style={styles.threadMessage}>
                      <View style={styles.threadMessageHeader}>
                        <Text style={styles.threadMessageAuthor}>{item.senderName}</Text>
                        <Text style={styles.threadMessageTime}>{formatTime(item.timestamp)}</Text>
                      </View>
                      <Text style={styles.threadMessageText}>{item.text}</Text>
                    </View>
                  )}
                  style={styles.threadMessagesList}
                />
                <View style={styles.threadReplyInput}>
                  <TextInput
                    style={styles.threadReplyTextInput}
                    placeholder="Reply to thread..."
                    value={threadReplyText}
                    onChangeText={setThreadReplyText}
                    multiline
                  />
                  <TouchableOpacity
                    style={styles.threadReplySendButton}
                    onPress={handleSendThreadReply}
                    disabled={!threadReplyText.trim()}
                  >
                    <Ionicons name="send" size={20} color="white" />
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </SafeAreaView>
      </Modal>

      {/* Message Info Modal */}
      <Modal visible={showMessageInfo} animationType="slide">
        <SafeAreaView style={styles.messageInfoModal}>
          <View style={styles.messageInfoHeader}>
            <TouchableOpacity onPress={() => setShowMessageInfo(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.messageInfoTitle}>Message Info</Text>
          </View>
          <View style={styles.messageInfoContent}>
            {selectedMessageInfo && (
              <>
                <View style={styles.messageInfoSection}>
                  <Text style={styles.messageInfoLabel}>Message:</Text>
                  <Text style={styles.messageInfoText}>{selectedMessageInfo.text}</Text>
                </View>
                <View style={styles.messageInfoSection}>
                  <Text style={styles.messageInfoLabel}>Sent by:</Text>
                  <Text style={styles.messageInfoText}>{selectedMessageInfo.senderName}</Text>
                </View>
                <View style={styles.messageInfoSection}>
                  <Text style={styles.messageInfoLabel}>Sent at:</Text>
                  <Text style={styles.messageInfoText}>{formatTime(selectedMessageInfo.timestamp)}</Text>
                </View>
                {selectedMessageInfo.editedAt && (
                  <View style={styles.messageInfoSection}>
                    <Text style={styles.messageInfoLabel}>Edited at:</Text>
                    <Text style={styles.messageInfoText}>{formatTime(selectedMessageInfo.editedAt)}</Text>
                  </View>
                )}
                <View style={styles.messageInfoSection}>
                  <Text style={styles.messageInfoLabel}>Read by:</Text>
                  {selectedMessageInfo.readBy && selectedMessageInfo.readBy.length > 0 ? (
                    selectedMessageInfo.readBy.map((reader: any) => (
                      <Text key={reader.userId} style={styles.messageInfoReadBy}>
                        {reader.userName} • {formatTime(reader.readAt)}
                      </Text>
                    ))
                  ) : (
                    <Text style={styles.messageInfoText}>Not read yet</Text>
                  )}
                </View>
              </>
            )}
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    minHeight: SCREEN_HEIGHT,
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  headerGradient: {
    paddingBottom: 12,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 12,
    gap: 12,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  groupInfoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  groupAvatar: {
    width: DeviceInfo.isTablet ? 50 : 40,
    height: DeviceInfo.isTablet ? 50 : 40,
    borderRadius: DeviceInfo.isTablet ? 25 : 20,
    borderWidth: 2,
    borderColor: COLORS.text,
  },
  groupHeaderInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  groupStatus: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerActionButton: {
    position: 'relative',
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    ...SHADOWS.sm,
  },
  pinnedBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: COLORS.error,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pinnedBadgeText: {
    fontSize: 10,
    color: COLORS.text,
    fontWeight: '700',
  },
  messagesList: {
    flex: 1,
    paddingHorizontal: ResponsiveSpacing.sm,
  },
  messagesContent: {
    paddingVertical: ResponsiveSpacing.md,
  },
  deletedMessage: {
    alignItems: 'center',
    paddingVertical: 8,
    marginVertical: 4,
  },
  deletedMessageText: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: COLORS.textMuted,
    fontStyle: 'italic',
  },
  typingContainer: {
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.sm,
    minHeight: ComponentSizes.buttonHeight.small,
  },
  typingBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignSelf: 'flex-start',
    gap: 8,
  },
  typingDots: {
    flexDirection: 'row',
    gap: 4,
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: COLORS.primary,
  },
  typingText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
  },
  inputContainer: {
    backgroundColor: COLORS.surface,
    borderTopWidth: 1,
    borderTopColor: COLORS.surfaceLight,
  },
  replyPreview: {
    backgroundColor: COLORS.surfaceLight,
    borderTopWidth: 1,
    borderTopColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  replyPreviewContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  replyPreviewInfo: {
    flex: 1,
  },
  replyPreviewSender: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '600',
    marginBottom: 2,
  },
  replyPreviewText: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  attachButton: {
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageInput: {
    flex: 1,
    backgroundColor: COLORS.inputBackground,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    color: COLORS.text,
    maxHeight: 100,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
  },
  emojiButton: {
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },

  // ==================== MISSING STYLES FOR ALL NEW COMPONENTS ====================

  // Selection Mode Styles
  selectionBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectionCount: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  selectionActions: {
    flexDirection: 'row',
    gap: 16,
  },
  selectionAction: {
    padding: 8,
  },

  // Attachment Menu Styles
  attachmentModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  attachmentModal: {
    backgroundColor: COLORS.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  attachmentModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 20,
  },
  attachmentGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    gap: 16,
  },
  attachmentItem: {
    alignItems: 'center',
    width: '22%',
  },
  attachmentIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  attachmentLabel: {
    fontSize: 12,
    color: COLORS.text,
    textAlign: 'center',
  },

  // Voice Recorder Styles
  voiceRecorderBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceRecorderModal: {
    backgroundColor: COLORS.surface,
    borderRadius: 20,
    padding: 24,
    width: '80%',
    alignItems: 'center',
  },
  voiceRecorderTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 20,
  },
  voiceRecorderContent: {
    alignItems: 'center',
    marginBottom: 24,
  },
  voiceWaveform: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 3,
    height: 50,
    marginBottom: 16,
  },
  waveformBar: {
    width: 4,
    backgroundColor: COLORS.primary,
    borderRadius: 2,
  },
  recordingDuration: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
  },
  voiceRecorderActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 24,
  },
  voiceRecorderCancel: {
    backgroundColor: COLORS.error + '20',
    borderRadius: 24,
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceRecorderButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 32,
    width: 64,
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceRecorderButtonActive: {
    backgroundColor: COLORS.error,
  },
  voiceRecorderSend: {
    backgroundColor: COLORS.success + '20',
    borderRadius: 24,
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Search Modal Styles
  searchModal: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    backgroundColor: COLORS.inputBackground,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    fontSize: 16,
    color: COLORS.text,
  },
  searchResultItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  searchResultSender: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: 4,
  },
  searchResultText: {
    fontSize: 16,
    color: COLORS.text,
    marginBottom: 4,
  },
  searchResultTime: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  searchEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  searchEmptyText: {
    fontSize: 16,
    color: COLORS.textMuted,
    textAlign: 'center',
    marginTop: 16,
  },

  // Pinned Messages Styles
  pinnedModal: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  pinnedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  pinnedTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  pinnedCount: {
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  pinnedCountText: {
    fontSize: 12,
    color: COLORS.background,
    fontWeight: '600',
  },
  pinnedMessageItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  pinnedMessageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  pinnedMessageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
  },
  pinnedMessageInfo: {
    flex: 1,
  },
  pinnedMessageSender: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
  },
  pinnedMessageTime: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  pinnedMessageText: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 20,
  },
  pinnedEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  pinnedEmptyText: {
    fontSize: 16,
    color: COLORS.textMuted,
    textAlign: 'center',
    marginTop: 16,
  },

  // Call Modal Styles
  callModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  callModal: {
    backgroundColor: COLORS.surface,
    borderRadius: 20,
    padding: 24,
    width: '80%',
    alignItems: 'center',
  },
  callModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 24,
  },
  callOptions: {
    flexDirection: 'row',
    gap: 24,
  },
  callOption: {
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    backgroundColor: COLORS.surfaceLight,
    minWidth: 100,
  },
  callOptionText: {
    fontSize: 14,
    color: COLORS.text,
    marginTop: 8,
    fontWeight: '500',
  },

  // Generic Modal Styles
  pollModal: { flex: 1, backgroundColor: COLORS.background },
  pollHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  pollTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  pollCreate: { fontSize: 16, color: COLORS.primary, fontWeight: '600' },
  pollContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  pollComingSoon: { fontSize: 16, color: COLORS.textMuted },

  locationModal: { flex: 1, backgroundColor: COLORS.background },
  locationHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  locationTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  locationShare: { fontSize: 16, color: COLORS.primary, fontWeight: '600' },
  locationContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  locationComingSoon: { fontSize: 16, color: COLORS.textMuted },

  contactModal: { flex: 1, backgroundColor: COLORS.background },
  contactHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  contactTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  contactContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  contactComingSoon: { fontSize: 16, color: COLORS.textMuted },

  // Emoji Modal Styles
  emojiModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emojiModal: {
    backgroundColor: COLORS.surface,
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxHeight: '60%',
  },
  emojiModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 16,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    gap: 8,
  },
  emojiOption: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 24,
    backgroundColor: COLORS.surfaceLight,
  },
  emojiOptionText: {
    fontSize: 24,
  },

  // More Generic Modal Styles
  schedulerModal: { flex: 1, backgroundColor: COLORS.background },
  schedulerHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  schedulerTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  schedulerContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  schedulerComingSoon: { fontSize: 16, color: COLORS.textMuted },

  forwardModal: { flex: 1, backgroundColor: COLORS.background },
  forwardHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  forwardTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  forwardContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  forwardComingSoon: { fontSize: 16, color: COLORS.textMuted },

  threadModal: { flex: 1, backgroundColor: COLORS.background },
  threadHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  threadTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  threadContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  threadComingSoon: { fontSize: 16, color: COLORS.textMuted },

  messageInfoModal: {
    flex: 1,
    backgroundColor: COLORS.background,
    // Animation properties handled by Animated.View
  },
  messageInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
    ...SHADOWS.sm,
  },
  messageInfoTitle: {
    fontSize: ResponsiveTypography.fontSize.lg,
    color: COLORS.text
  },
  messageInfoContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: ResponsiveSpacing.lg,
  },
  messageInfoComingSoon: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: COLORS.textMuted
  },

  // Modal backdrop and animation styles
  modalBackdrop: {
    flex: 1,
    backgroundColor: COLORS.overlay,
    justifyContent: 'center',
    alignItems: 'center',
    // Animation properties handled by Animated.View
  },
  modal: {
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    padding: ResponsiveSpacing.lg,
    margin: ResponsiveSpacing.md,
    minWidth: DeviceInfo.isTablet ? '60%' : '80%',
    ...SHADOWS.lg,
    // Animation properties handled by Animated.View
  },
  modalTitle: {
    fontSize: ResponsiveTypography.fontSize.xl,
    color: COLORS.text,
    marginBottom: ResponsiveSpacing.md,
    textAlign: 'center',
  },
  modalClose: {
    fontSize: 16,
    color: COLORS.primary,
    textAlign: 'center',
    marginTop: 16,
  },

  // Missing styles
  slowModeIndicator: {
    backgroundColor: COLORS.warning,
    padding: 8,
    borderRadius: 8,
    margin: 8,
  },
  slowModeText: {
    color: COLORS.text,
    fontSize: 12,
    textAlign: 'center',
  },
  typingMembersIndicator: {
    backgroundColor: COLORS.primary,
    padding: 8,
    borderRadius: 8,
    margin: 8,
  },
  typingMembersText: {
    color: COLORS.text,
    fontSize: 12,
    textAlign: 'center',
  },
  onlineMembersIndicator: {
    backgroundColor: COLORS.success,
    padding: 8,
    borderRadius: 8,
    margin: 8,
  },
  onlineMembersText: {
    color: COLORS.text,
    fontSize: 12,
    textAlign: 'center',
  },
});

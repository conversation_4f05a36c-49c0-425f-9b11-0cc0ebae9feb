/**
 * Responsive Container Component for IraChat
 * Provides consistent responsive layout for all pages and components
 */

import React from 'react';
import { View, ViewStyle, ScrollView, Text, Modal, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { IraChatWallpaper } from './IraChatWallpaper';
import { IRACHAT_COLORS, TYPOGRAPHY } from '../../styles/iraChatDesignSystem';
import { ResponsiveSpacing, ResponsiveTypography, ResponsiveScale, DeviceInfo } from '../../utils/responsiveUtils';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  wallpaperVariant?: 'default' | 'chat' | 'auth' | 'minimal';
  animated?: boolean;
  style?: ViewStyle;
  statusBarStyle?: 'light' | 'dark' | 'auto';
  backgroundColor?: string;
  paddingHorizontal?: boolean;
  paddingVertical?: boolean;
  safeArea?: boolean;
  scrollable?: boolean;
  centered?: boolean;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  wallpaperVariant = 'default',
  animated = true,
  style,
  statusBarStyle = 'light',
  backgroundColor,
  paddingHorizontal = true,
  paddingVertical = true,
  safeArea = true,
  scrollable = false,
  centered = false,
}) => {
  const containerStyle: ViewStyle = {
    flex: 1,
    backgroundColor: backgroundColor || IRACHAT_COLORS.background,
    ...(paddingHorizontal && { paddingHorizontal: ResponsiveSpacing.screenPadding }),
    ...(paddingVertical && { paddingVertical: ResponsiveSpacing.md }),
    ...(safeArea && {
      paddingTop: DeviceInfo.statusBarHeight,
      paddingBottom: DeviceInfo.bottomSafeArea,
    }),
    ...(centered && {
      alignItems: 'center',
      justifyContent: 'center',
    }),
    ...style,
  };

  const content = (
    <>
      <StatusBar style={statusBarStyle} />

      {/* Beautiful animated wallpaper */}
      <IraChatWallpaper variant={wallpaperVariant} animated={animated} />

      {children}
    </>
  );

  if (scrollable) {
    return (
      <View style={containerStyle}>
        <StatusBar style={statusBarStyle} />
        <IraChatWallpaper variant={wallpaperVariant} animated={animated} />
        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          bounces={true}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {children}
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={containerStyle}>
      {content}
    </View>
  );
};

// Responsive Grid Component
interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: number;
  spacing?: number;
  style?: ViewStyle;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns,
  spacing,
  style,
}) => {
  const gridColumns = columns || (DeviceInfo.isSmallPhone ? 2 : DeviceInfo.isMediumPhone ? 3 : DeviceInfo.isTablet ? 4 : 3);
  const gridSpacing = spacing || ResponsiveSpacing.md;

  const gridStyle: ViewStyle = {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -gridSpacing / 2,
    ...style,
  };

  const childrenArray = React.Children.toArray(children);

  return (
    <View style={gridStyle}>
      {childrenArray.map((child, index) => (
        <View
          key={index}
          style={{
            width: `${100 / gridColumns}%`,
            paddingHorizontal: gridSpacing / 2,
            marginBottom: gridSpacing,
          }}
        >
          {child}
        </View>
      ))}
    </View>
  );
};

// Responsive Text Component
interface ResponsiveTextProps {
  children: React.ReactNode;
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  style?: any;
  numberOfLines?: number;
  color?: string;
}

export const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  size = 'base',
  weight = 'normal',
  style,
  numberOfLines,
  color,
}) => {
  const fontWeightMap = {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  };

  const textStyle = {
    fontSize: ResponsiveTypography.fontSize[size],
    fontWeight: fontWeightMap[weight],
    fontFamily: TYPOGRAPHY.fontFamily,
    color: color || IRACHAT_COLORS.text,
    ...style,
  };

  return (
    <Text style={textStyle} numberOfLines={numberOfLines}>
      {children}
    </Text>
  );
};

// Responsive Spacing Component
interface ResponsiveSpacerProps {
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl';
  horizontal?: boolean;
  vertical?: boolean;
}

export const ResponsiveSpacer: React.FC<ResponsiveSpacerProps> = ({
  size = 'base',
  horizontal = false,
  vertical = true,
}) => {
  const sizeMap = {
    xs: ResponsiveSpacing.xs,
    sm: ResponsiveSpacing.sm,
    base: ResponsiveSpacing.md,
    lg: ResponsiveSpacing.lg,
    xl: ResponsiveSpacing.xl,
  };

  const spacingValue = sizeMap[size];

  return (
    <View
      style={{
        ...(horizontal && { width: spacingValue }),
        ...(vertical && { height: spacingValue }),
      }}
    />
  );
};

// Responsive Modal Component
interface ResponsiveModalProps {
  children: React.ReactNode;
  visible: boolean;
  onClose: () => void;
  title?: string;
  style?: ViewStyle;
}

export const ResponsiveModal: React.FC<ResponsiveModalProps> = ({
  children,
  visible,
  onClose,
  title,
  style,
}) => {
  const modalStyle: ViewStyle = {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  };

  const contentStyle: ViewStyle = {
    width: DeviceInfo.isSmallPhone ? '90%' : DeviceInfo.isMediumPhone ? '85%' : '80%',
    maxWidth: DeviceInfo.isTablet ? 600 : 400,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: ResponsiveScale.borderRadius(16),
    padding: ResponsiveSpacing.lg,
    maxHeight: '80%',
    ...style,
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity style={modalStyle} activeOpacity={1} onPress={onClose}>
        <TouchableOpacity
          style={contentStyle}
          activeOpacity={1}
          onPress={(e) => e.stopPropagation()}
        >
          {title && (
            <ResponsiveText
              size="lg"
              weight="bold"
              style={{ marginBottom: ResponsiveSpacing.lg }}
            >
              {title}
            </ResponsiveText>
          )}
          {children}
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );
};

export default ResponsiveContainer;

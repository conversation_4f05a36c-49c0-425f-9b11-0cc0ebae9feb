<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- 
	🔥 FIREBASE iOS CONFIGURATION TEMPLATE
	
	⚠️ THIS IS A TEMPLATE FILE - YOU NEED TO REPLACE IT WITH YOUR ACTUAL GoogleService-Info.plist
	
	📋 TO GET YOUR ACTUAL FILE:
	1. Go to Firebase Console: https://console.firebase.google.com
	2. Select your project: irachat-production
	3. Click "Add app" and select iOS
	4. Enter iOS bundle ID: com.irachat.ios
	5. Enter App nickname: IraChat iOS
	6. Download the actual GoogleService-Info.plist file
	7. Replace this template file with the downloaded file
	8. Rename it to: GoogleService-Info.plist (remove -TEMPLATE)
	
	🚨 IMPORTANT: This template will NOT work for actual Firebase connection!
	-->
	
	<key>CLIENT_ID</key>
	<string>YOUR_CLIENT_ID_FROM_FIREBASE_CONSOLE</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>YOUR_REVERSED_CLIENT_ID_FROM_FIREBASE_CONSOLE</string>
	<key>API_KEY</key>
	<string>YOUR_API_KEY_FROM_FIREBASE_CONSOLE</string>
	<key>GCM_SENDER_ID</key>
	<string>YOUR_GCM_SENDER_ID_FROM_FIREBASE_CONSOLE</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.irachat.ios</string>
	<key>PROJECT_ID</key>
	<string>irachat-production</string>
	<key>STORAGE_BUCKET</key>
	<string>irachat-production.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>YOUR_GOOGLE_APP_ID_FROM_FIREBASE_CONSOLE</string>
	<key>DATABASE_URL</key>
	<string>https://irachat-production-default-rtdb.firebaseio.com</string>
</dict>
</plist>

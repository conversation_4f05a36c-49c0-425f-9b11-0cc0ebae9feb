import React from 'react';
import { View, Text, TouchableOpacity, Modal } from 'react-native';

interface ChatListDeleteModalProps {
  visible: boolean;
  selectedCount: number;
  onConfirm: () => void;
  onCancel: () => void;
}

export const ChatListDeleteModal: React.FC<ChatListDeleteModalProps> = ({
  visible,
  selectedCount,
  onConfirm,
  onCancel,
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={{ 
        flex: 1, 
        backgroundColor: 'rgba(0, 0, 0, 0.5)', 
        justifyContent: 'center', 
        alignItems: 'center' 
      }}>
        <View style={{ 
          backgroundColor: 'white', 
          borderRadius: 12, 
          padding: 24, 
          margin: 20, 
          minWidth: 280 
        }}>
          <Text style={{ 
            fontSize: 18, 
            fontWeight: '600', 
            color: '#1f2937', 
            marginBottom: 8 
          }}>
            Delete Chat{selectedCount > 1 ? 's' : ''}
          </Text>
          <Text style={{ 
            fontSize: 14, 
            color: '#6b7280', 
            marginBottom: 20 
          }}>
            Are you sure you want to delete {selectedCount} chat{selectedCount > 1 ? 's' : ''}? This action cannot be undone.
          </Text>
          <View style={{ 
            flexDirection: 'row', 
            gap: 12 
          }}>
            <TouchableOpacity
              onPress={onCancel}
              style={{ 
                flex: 1, 
                paddingVertical: 12, 
                paddingHorizontal: 24, 
                borderRadius: 8, 
                backgroundColor: '#f3f4f6' 
              }}
            >
              <Text style={{ 
                textAlign: 'center', 
                fontSize: 16, 
                fontWeight: '600', 
                color: '#374151' 
              }}>
                Cancel
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={onConfirm}
              style={{ 
                flex: 1, 
                paddingVertical: 12, 
                paddingHorizontal: 24, 
                borderRadius: 8, 
                backgroundColor: '#EF4444' 
              }}
            >
              <Text style={{ 
                textAlign: 'center', 
                fontSize: 16, 
                fontWeight: '600', 
                color: 'white' 
              }}>
                Delete
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

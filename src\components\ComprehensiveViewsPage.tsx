// 👁️ COMPREHENSIVE VIEWS PAGE
// Beautiful analytics page with detailed viewer information and charts
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Modal,
  StatusBar,
  Animated,
  FlatList,
  Alert,
  SafeAreaView,
  Image,
  RefreshControl,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { UserInteraction, UpdateAnalytics } from '../types/Update';
import { comprehensiveUpdatesService } from '../services/comprehensiveUpdatesService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  chartColors: ['#87CEEB', '#4682B4', '#1E90FF', '#00BFFF', '#B0E0E6'],
};

interface ViewUser extends UserInteraction {
  viewDuration?: number;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
  location?: string;
}

interface ViewsAnalytics {
  totalViews: number;
  uniqueViews: number;
  averageViewDuration: number;
  topCountries: { country: string; count: number }[];
  viewsByHour: { hour: number; count: number }[];
  deviceBreakdown: { type: string; count: number; percentage: number }[];
}

interface ComprehensiveViewsPageProps {
  visible: boolean;
  updateId: string;
  updateTitle?: string;
  updateThumbnail?: string;
  currentUserId: string;
  onClose: () => void;
  onUserPress?: (userId: string) => void;
}

export const ComprehensiveViewsPage: React.FC<ComprehensiveViewsPageProps> = ({
  visible,
  updateId,
  updateTitle,
  updateThumbnail,
  currentUserId,
  onClose,
  onUserPress,
}) => {
  const insets = useSafeAreaInsets();
  
  // Responsive dimensions
  const isTablet = SCREEN_WIDTH > 768;
  const headerHeight = 60 + insets.top;

  // ==================== STATE MANAGEMENT ====================
  
  const [views, setViews] = useState<ViewUser[]>([]);
  const [analytics, setAnalytics] = useState<ViewsAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'viewers' | 'analytics'>('viewers');

  // Animation refs
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const chartAnimations = useRef<Animated.Value[]>([]).current;

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (visible) {
      loadViewsData();
      showModal();
    } else {
      hideModal();
    }
  }, [visible]);

  // ==================== ANIMATION METHODS ====================

  const showModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateCharts = () => {
    chartAnimations.forEach((anim, index) => {
      Animated.timing(anim, {
        toValue: 1,
        duration: 800,
        delay: index * 100,
        useNativeDriver: false,
      }).start();
    });
  };

  // ==================== DATA METHODS ====================

  const loadViewsData = async () => {
    setIsLoading(true);
    try {
      // Load views and analytics from service
      // Load real views data from service
      const [viewsResult, analyticsResult] = await Promise.all([
        comprehensiveUpdatesService.getViews(updateId),
        comprehensiveUpdatesService.getAnalytics(updateId)
      ]);

      if (viewsResult.success && analyticsResult.success) {
        setViews(viewsResult.views || []);
        setAnalytics(analyticsResult.analytics || {
          totalViews: 0,
          uniqueViews: 0,
          averageViewDuration: 0,
          topCountries: [],
          hourlyBreakdown: [],
          deviceBreakdown: []
        });
      } else {
        setViews([]);
        setAnalytics({
          totalViews: 0,
          uniqueViews: 0,
          averageViewDuration: 0,
          topCountries: [],
          deviceBreakdown: [],
          viewsByHour: []
        });
      }

      // Remove mock data - using real Firebase data
      /*const mockViews: ViewUser[] = [
        {
          userId: 'user1',
          userName: 'Alice Johnson',
          userAvatar: 'https://randomuser.me/api/portraits/women/1.jpg',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          type: 'view',
          viewDuration: 45,
          deviceType: 'mobile',
          location: 'New York, US',
        },
        {
          userId: 'user2',
          userName: 'Bob Smith',
          userAvatar: 'https://randomuser.me/api/portraits/men/2.jpg',
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          type: 'view',
          viewDuration: 32,
          deviceType: 'tablet',
          location: 'London, UK',
        },
        {
          userId: 'user3',
          userName: 'Charlie Brown',
          userAvatar: 'https://randomuser.me/api/portraits/men/3.jpg',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          type: 'view',
          viewDuration: 28,
          deviceType: 'mobile',
          location: 'Tokyo, JP',
        },
      ];

      const mockAnalytics: ViewsAnalytics = {
        totalViews: 1247,
        uniqueViews: 892,
        averageViewDuration: 38.5,
        topCountries: [
          { country: 'United States', count: 456 },
          { country: 'United Kingdom', count: 234 },
          { country: 'Canada', count: 123 },
          { country: 'Australia', count: 89 },
          { country: 'Germany', count: 67 },
        ],
        viewsByHour: [
          { hour: 0, count: 12 }, { hour: 1, count: 8 }, { hour: 2, count: 5 },
          { hour: 3, count: 3 }, { hour: 4, count: 7 }, { hour: 5, count: 15 },
          { hour: 6, count: 28 }, { hour: 7, count: 45 }, { hour: 8, count: 67 },
          { hour: 9, count: 89 }, { hour: 10, count: 98 }, { hour: 11, count: 87 },
          { hour: 12, count: 123 }, { hour: 13, count: 134 }, { hour: 14, count: 145 },
          { hour: 15, count: 156 }, { hour: 16, count: 167 }, { hour: 17, count: 178 },
          { hour: 18, count: 189 }, { hour: 19, count: 198 }, { hour: 20, count: 187 },
          { hour: 21, count: 176 }, { hour: 22, count: 165 }, { hour: 23, count: 154 },
        ],
        deviceBreakdown: [
          { type: 'Mobile', count: 789, percentage: 63.3 },
          { type: 'Desktop', count: 312, percentage: 25.0 },
          { type: 'Tablet', count: 146, percentage: 11.7 },
        ],
      };*/

      // Initialize chart animations
      chartAnimations.length = 0;
      for (let i = 0; i < 5; i++) {
        chartAnimations.push(new Animated.Value(0));
      }
      
      setTimeout(animateCharts, 500);
    } catch (error) {
      console.error('❌ Error loading views data:', error);
      Alert.alert('Error', 'Failed to load views data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadViewsData();
    setIsRefreshing(false);
  }, []);

  const handleUserPress = (userId: string) => {
    onUserPress?.(userId);
  };

  const getTimeAgo = (timestamp: Date) => {
    const now = Date.now();
    const diff = now - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="chevron-down" size={28} color={COLORS.text} />
          </TouchableOpacity>
          
          <View style={styles.headerInfo}>
            <View style={styles.headerTitleContainer}>
              <Ionicons name="eye" size={24} color={COLORS.text} />
              <Text style={styles.headerTitle}>Views</Text>
            </View>
            <Text style={styles.headerSubtitle}>
              {analytics?.totalViews || 0} total views
            </Text>
          </View>

          <TouchableOpacity style={styles.headerAction}>
            <Ionicons name="share-outline" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>

        {/* Tab Bar */}
        <View style={styles.tabBar}>
          <TouchableOpacity
            style={[styles.tabItem, activeTab === 'viewers' && styles.activeTabItem]}
            onPress={() => setActiveTab('viewers')}
          >
            <Text style={[styles.tabText, activeTab === 'viewers' && styles.activeTabText]}>
              Viewers
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tabItem, activeTab === 'analytics' && styles.activeTabItem]}
            onPress={() => setActiveTab('analytics')}
          >
            <Text style={[styles.tabText, activeTab === 'analytics' && styles.activeTabText]}>
              Analytics
            </Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );

  const renderViewerItem = ({ item: view, index }: { item: ViewUser; index: number }) => (
    <Animated.View
      style={[
        styles.viewContainer,
        {
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [30, 0],
            }),
          }],
        },
      ]}
    >
      <TouchableOpacity 
        style={styles.viewItem}
        onPress={() => handleUserPress(view.userId)}
        activeOpacity={0.7}
      >
        <View style={styles.viewLeft}>
          <View style={styles.avatarContainer}>
            <Image 
              source={{ uri: view.userAvatar || 'https://via.placeholder.com/50' }} 
              style={styles.avatarImage} 
            />
            <View style={styles.deviceBadge}>
              <Ionicons 
                name={view.deviceType === 'mobile' ? 'phone-portrait' : 
                     view.deviceType === 'tablet' ? 'tablet-portrait' : 'desktop'} 
                size={10} 
                color={COLORS.text} 
              />
            </View>
          </View>

          <View style={styles.userInfo}>
            <Text style={styles.userName}>{view.userName}</Text>
            <View style={styles.viewMeta}>
              <Text style={styles.timeAgo}>{getTimeAgo(view.timestamp)}</Text>
              {view.location && (
                <>
                  <Text style={styles.metaSeparator}>•</Text>
                  <Text style={styles.location}>{view.location}</Text>
                </>
              )}
            </View>
          </View>
        </View>

        <View style={styles.viewRight}>
          {view.viewDuration && (
            <View style={styles.durationBadge}>
              <Ionicons name="time-outline" size={12} color={COLORS.primary} />
              <Text style={styles.durationText}>
                {formatDuration(view.viewDuration)}
              </Text>
            </View>
          )}
          <TouchableOpacity
            style={styles.messageButton}
            onPress={() => {
              // Navigate to chat with this user
              console.log('Start chat with:', view.userId);
            }}
          >
            <Ionicons name="chatbubble-outline" size={14} color={COLORS.primary} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderAnalyticsCard = (title: string, value: string, subtitle?: string, icon?: string) => (
    <Animated.View 
      style={[
        styles.analyticsCard,
        {
          opacity: fadeAnim,
          transform: [{
            scale: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.8, 1],
            }),
          }],
        },
      ]}
    >
      <View style={styles.cardHeader}>
        {icon && <Ionicons name={icon as any} size={20} color={COLORS.primary} />}
        <Text style={styles.cardTitle}>{title}</Text>
      </View>
      <Text style={styles.cardValue}>{value}</Text>
      {subtitle && <Text style={styles.cardSubtitle}>{subtitle}</Text>}
    </Animated.View>
  );

  const renderAnalytics = () => {
    if (!analytics) return null;

    return (
      <ScrollView 
        style={styles.analyticsContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={COLORS.primary}
            colors={[COLORS.primary]}
          />
        }
      >
        {/* Overview Cards */}
        <View style={styles.overviewGrid}>
          {renderAnalyticsCard(
            'Total Views', 
            analytics.totalViews.toLocaleString(), 
            'All time',
            'eye'
          )}
          {renderAnalyticsCard(
            'Unique Views', 
            analytics.uniqueViews.toLocaleString(), 
            'Individual viewers',
            'people'
          )}
          {renderAnalyticsCard(
            'Avg. Duration', 
            `${analytics.averageViewDuration}s`, 
            'Watch time',
            'time'
          )}
          {renderAnalyticsCard(
            'Engagement', 
            `${((analytics.uniqueViews / analytics.totalViews) * 100).toFixed(1)}%`, 
            'Unique/Total ratio',
            'trending-up'
          )}
        </View>

        {/* Device Breakdown */}
        <View style={styles.chartSection}>
          <Text style={styles.sectionTitle}>Device Breakdown</Text>
          <View style={styles.deviceChart}>
            {analytics.deviceBreakdown.map((device, index) => (
              <View key={device.type} style={styles.deviceItem}>
                <View style={styles.deviceInfo}>
                  <View style={[styles.deviceColor, { backgroundColor: COLORS.chartColors[index] }]} />
                  <Text style={styles.deviceType}>{device.type}</Text>
                </View>
                <Text style={styles.devicePercentage}>{device.percentage}%</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Top Countries */}
        <View style={styles.chartSection}>
          <Text style={styles.sectionTitle}>Top Countries</Text>
          <View style={styles.countriesChart}>
            {analytics.topCountries.map((country, index) => (
              <View key={country.country} style={styles.countryItem}>
                <Text style={styles.countryName}>{country.country}</Text>
                <View style={styles.countryBar}>
                  <Animated.View 
                    style={[
                      styles.countryBarFill,
                      {
                        width: chartAnimations[index] ? 
                          chartAnimations[index].interpolate({
                            inputRange: [0, 1],
                            outputRange: ['0%', `${(country.count / analytics.topCountries[0].count) * 100}%`],
                          }) : '0%',
                        backgroundColor: COLORS.chartColors[index % COLORS.chartColors.length],
                      }
                    ]}
                  />
                </View>
                <Text style={styles.countryCount}>{country.count}</Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    );
  };

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="none" statusBarTranslucent>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        <Animated.View 
          style={[
            styles.modalContent,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {renderHeader()}

          {/* Content */}
          {activeTab === 'viewers' ? (
            <FlatList
              data={views}
              renderItem={renderViewerItem}
              keyExtractor={(item) => item.userId}
              style={styles.viewsList}
              contentContainerStyle={styles.viewsListContent}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={isRefreshing}
                  onRefresh={handleRefresh}
                  tintColor={COLORS.primary}
                  colors={[COLORS.primary]}
                />
              }
              ListEmptyComponent={() => (
                <View style={styles.emptyContainer}>
                  <Ionicons name="eye-outline" size={64} color={COLORS.textMuted} />
                  <Text style={styles.emptyText}>No views yet</Text>
                  <Text style={styles.emptySubtext}>Share your update to get more views!</Text>
                </View>
              )}
            />
          ) : (
            renderAnalytics()
          )}
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.overlay,
  },
  modalContent: {
    flex: 1,
    backgroundColor: COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 50,
  },
  header: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingBottom: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 16,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text,
    marginLeft: 8,
  },
  headerSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  headerAction: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 8,
  },
  tabItem: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 20,
    marginHorizontal: 4,
  },
  activeTabItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  tabText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  activeTabText: {
    color: COLORS.text,
    fontWeight: '700',
  },
  viewsList: {
    flex: 1,
  },
  viewsListContent: {
    paddingVertical: 8,
  },
  viewContainer: {
    marginBottom: 4,
  },
  viewItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.surface,
    marginHorizontal: 16,
    marginVertical: 2,
    borderRadius: 16,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  viewLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatarImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  deviceBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.surface,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  viewMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeAgo: {
    fontSize: 13,
    color: COLORS.textMuted,
  },
  metaSeparator: {
    fontSize: 13,
    color: COLORS.textMuted,
    marginHorizontal: 6,
  },
  location: {
    fontSize: 13,
    color: COLORS.primary,
    fontWeight: '500',
  },
  viewRight: {
    alignItems: 'flex-end',
  },
  durationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surfaceLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  durationText: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '600',
    marginLeft: 4,
  },
  followingBadge: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  followingText: {
    fontSize: 10,
    color: COLORS.background,
    fontWeight: '600',
  },
  analyticsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  analyticsCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    padding: 16,
    width: '48%',
    marginBottom: 12,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginLeft: 6,
  },
  cardValue: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  chartSection: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 16,
  },
  deviceChart: {
    gap: 12,
  },
  deviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  deviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  deviceColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  deviceType: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
  },
  devicePercentage: {
    fontSize: 16,
    color: COLORS.primary,
    fontWeight: '600',
  },
  countriesChart: {
    gap: 12,
  },
  countryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  countryName: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '500',
    width: 100,
  },
  countryBar: {
    flex: 1,
    height: 8,
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 4,
    overflow: 'hidden',
  },
  countryBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  countryCount: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '600',
    width: 40,
    textAlign: 'right',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginTop: 4,
    textAlign: 'center',
  },
  messageButton: {
    padding: 6,
    borderRadius: 12,
    backgroundColor: COLORS.surfaceLight,
  },
});

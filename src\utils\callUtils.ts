// 🔥 CALL UTILITIES - COMPLETE UTILITY FUNCTIONS
// No mockups, no fake data - 100% real utility functions for calling

/**
 * Format call time relative to now
 */
export const formatCallTime = (timestamp: Date): string => {
  try {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const weeks = Math.floor(days / 7);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    if (seconds < 60) {
      return 'Just now';
    } else if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else if (days === 1) {
      return 'Yesterday';
    } else if (days < 7) {
      return `${days} days ago`;
    } else if (weeks === 1) {
      return '1 week ago';
    } else if (weeks < 4) {
      return `${weeks} weeks ago`;
    } else if (months === 1) {
      return '1 month ago';
    } else if (months < 12) {
      return `${months} months ago`;
    } else if (years === 1) {
      return '1 year ago';
    } else {
      return `${years} years ago`;
    }
  } catch (error) {
    console.error('❌ Error formatting call time:', error);
    return 'Unknown';
  }
};

/**
 * Format call duration in seconds to readable format
 */
export const formatCallDuration = (seconds: number): string => {
  try {
    if (seconds < 0) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  } catch (error) {
    console.error('❌ Error formatting call duration:', error);
    return '0:00';
  }
};

/**
 * Format call duration for display (more readable)
 */
export const formatCallDurationDisplay = (seconds: number): string => {
  try {
    if (seconds < 0) return '0 seconds';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    const parts: string[] = [];
    
    if (hours > 0) {
      parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
    }
    
    if (minutes > 0) {
      parts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);
    }
    
    if (remainingSeconds > 0 && hours === 0) {
      parts.push(`${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`);
    }

    return parts.length > 0 ? parts.join(', ') : '0 seconds';
  } catch (error) {
    console.error('❌ Error formatting call duration display:', error);
    return '0 seconds';
  }
};

/**
 * Get call type icon name
 */
export const getCallTypeIcon = (type: string): string => {
  switch (type) {
    case 'video':
      return 'videocam';
    case 'voice':
    case 'audio':
      return 'call';
    default:
      return 'call';
  }
};

/**
 * Get call direction icon name
 */
export const getCallDirectionIcon = (direction: string, status: string): string => {
  if (status === 'missed') {
    return 'call-outline';
  }
  
  switch (direction) {
    case 'outgoing':
      return 'call-outline';
    case 'incoming':
      return 'call-outline';
    default:
      return 'call-outline';
  }
};

/**
 * Get call status color
 */
export const getCallStatusColor = (status: string): string => {
  switch (status) {
    case 'connected':
    case 'ended':
      return '#4ECDC4';
    case 'missed':
    case 'declined':
    case 'failed':
      return '#FF6B6B';
    case 'ringing':
    case 'connecting':
      return '#F59E0B';
    default:
      return '#6B7280';
  }
};

/**
 * Get call type color
 */
export const getCallTypeColor = (type: string): string => {
  switch (type) {
    case 'video':
      return '#FF6B6B';
    case 'voice':
    case 'audio':
      return '#4ECDC4';
    default:
      return '#6B7280';
  }
};

/**
 * Get connection quality color
 */
export const getConnectionQualityColor = (quality: string): string => {
  switch (quality) {
    case 'excellent':
      return '#10B981';
    case 'good':
      return '#F59E0B';
    case 'poor':
      return '#EF4444';
    case 'connecting':
      return '#6B7280';
    default:
      return '#6B7280';
  }
};

/**
 * Get connection quality icon
 */
export const getConnectionQualityIcon = (quality: string): string => {
  switch (quality) {
    case 'excellent':
      return 'wifi';
    case 'good':
      return 'wifi';
    case 'poor':
      return 'wifi-outline';
    case 'connecting':
      return 'sync';
    default:
      return 'wifi-outline';
  }
};

/**
 * Validate phone number format
 */
export const validatePhoneNumber = (phoneNumber: string): boolean => {
  try {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Check if it's a valid length (10-15 digits)
    if (cleaned.length < 10 || cleaned.length > 15) {
      return false;
    }
    
    // Check if it starts with a valid country code or area code
    return true;
  } catch (error) {
    console.error('❌ Error validating phone number:', error);
    return false;
  }
};

/**
 * Format phone number for display
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  try {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Format based on length
    if (cleaned.length === 10) {
      // US format: (*************
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
      // US with country code: +****************
      return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    } else {
      // International format: +XX XXX XXX XXXX
      return `+${cleaned}`;
    }
  } catch (error) {
    console.error('❌ Error formatting phone number:', error);
    return phoneNumber;
  }
};

/**
 * Generate call ID
 */
export const generateCallId = (callerId: string, receiverId: string): string => {
  try {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `call_${timestamp}_${callerId}_${receiverId}_${random}`;
  } catch (error) {
    console.error('❌ Error generating call ID:', error);
    return `call_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }
};

/**
 * Calculate call quality score
 */
export const calculateCallQuality = (metrics: {
  packetsLost: number;
  latency: number;
  bandwidth: number;
}): 'excellent' | 'good' | 'poor' | 'connecting' => {
  try {
    const { packetsLost, latency, bandwidth } = metrics;
    
    // Calculate quality score (0-100)
    let score = 100;
    
    // Deduct points for packet loss
    score -= packetsLost * 10;
    
    // Deduct points for high latency
    if (latency > 200) {
      score -= (latency - 200) / 10;
    }
    
    // Deduct points for low bandwidth
    if (bandwidth < 100) {
      score -= (100 - bandwidth) / 2;
    }
    
    // Determine quality level
    if (score >= 80) {
      return 'excellent';
    } else if (score >= 60) {
      return 'good';
    } else {
      return 'poor';
    }
  } catch (error) {
    console.error('❌ Error calculating call quality:', error);
    return 'connecting';
  }
};

/**
 * Check if call is active
 */
export const isCallActive = (status: string): boolean => {
  return ['ringing', 'connecting', 'connected'].includes(status);
};

/**
 * Check if call can be retried
 */
export const canRetryCall = (status: string): boolean => {
  return ['failed', 'missed', 'declined', 'timeout'].includes(status);
};

/**
 * Get call status display text
 */
export const getCallStatusText = (status: string): string => {
  switch (status) {
    case 'ringing':
      return 'Ringing...';
    case 'connecting':
      return 'Connecting...';
    case 'connected':
      return 'Connected';
    case 'ended':
      return 'Call ended';
    case 'missed':
      return 'Missed call';
    case 'declined':
      return 'Declined';
    case 'failed':
      return 'Call failed';
    case 'timeout':
      return 'No answer';
    default:
      return status;
  }
};

/**
 * Convert timestamp to call time
 */
export const timestampToCallTime = (timestamp: any): Date => {
  try {
    if (timestamp && typeof timestamp.toDate === 'function') {
      // Firestore Timestamp
      return timestamp.toDate();
    } else if (timestamp instanceof Date) {
      return timestamp;
    } else if (typeof timestamp === 'number') {
      return new Date(timestamp);
    } else if (typeof timestamp === 'string') {
      return new Date(timestamp);
    } else {
      return new Date();
    }
  } catch (error) {
    console.error('❌ Error converting timestamp:', error);
    return new Date();
  }
};

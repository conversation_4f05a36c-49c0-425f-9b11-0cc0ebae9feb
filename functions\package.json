{"name": "irachat-functions", "version": "1.0.0", "description": "Firebase Cloud Functions for IraChat - Real scheduled messages, push notifications, and real-time features", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0", "cors": "^2.8.5", "express": "^4.18.2", "node-fetch": "^3.3.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.11.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "firebase-functions-test": "^3.1.1", "jest": "^29.7.0", "typescript": "^5.3.3"}, "private": true}
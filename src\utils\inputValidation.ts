// 🔒 Input Validation and Sanitization Utilities for IraChat
// Comprehensive security-focused input validation

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  sanitized?: string;
}

// Security patterns to detect and prevent
const SECURITY_PATTERNS = {
  // XSS patterns
  XSS: [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe/gi,
    /<object/gi,
    /<embed/gi,
  ],
  
  // SQL injection patterns
  SQL_INJECTION: [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
    /('|(\\')|(;)|(--)|(\|)|(\*)|(%)|(\+))/gi,
  ],
  
  // Path traversal
  PATH_TRAVERSAL: [
    /\.\.\//gi,
    /\.\.\\/gi,
    /%2e%2e%2f/gi,
    /%2e%2e%5c/gi,
  ],
  
  // Command injection
  COMMAND_INJECTION: [
    /(\||&|;|\$\(|\`)/gi,
    /(rm\s|del\s|format\s)/gi,
  ],
};

// Content sanitization
export class ContentSanitizer {
  /**
   * Sanitize text content for safe display
   */
  static sanitizeText(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
      .trim();
  }

  /**
   * Sanitize HTML content (strip all HTML tags)
   */
  static sanitizeHTML(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    return input
      .replace(/<[^>]*>/g, '') // Remove all HTML tags
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')
      .trim();
  }

  /**
   * Sanitize URL input
   */
  static sanitizeURL(input: string): ValidationResult {
    if (!input || typeof input !== 'string') {
      return { isValid: false, error: 'URL is required' };
    }

    try {
      const url = new URL(input);
      
      // Only allow HTTP and HTTPS protocols
      if (!['http:', 'https:'].includes(url.protocol)) {
        return { isValid: false, error: 'Only HTTP and HTTPS URLs are allowed' };
      }

      // Check for suspicious patterns
      if (this.containsSuspiciousPatterns(input)) {
        return { isValid: false, error: 'URL contains invalid characters' };
      }

      return { isValid: true, sanitized: url.toString() };
    } catch (_error) {
      return { isValid: false, error: 'Invalid URL format' };
    }
  }

  /**
   * Check for suspicious patterns in input
   */
  static containsSuspiciousPatterns(input: string): boolean {
    const allPatterns = [
      ...SECURITY_PATTERNS.XSS,
      ...SECURITY_PATTERNS.SQL_INJECTION,
      ...SECURITY_PATTERNS.PATH_TRAVERSAL,
      ...SECURITY_PATTERNS.COMMAND_INJECTION,
    ];

    return allPatterns.some(pattern => pattern.test(input));
  }
}

// Message validation
export class MessageValidator {
  private static readonly MAX_MESSAGE_LENGTH = 5000;
  private static readonly MIN_MESSAGE_LENGTH = 1;

  static validate(message: string): ValidationResult {
    if (!message || typeof message !== 'string') {
      return { isValid: false, error: 'Message is required' };
    }

    // Length validation
    if (message.length < this.MIN_MESSAGE_LENGTH) {
      return { isValid: false, error: 'Message is too short' };
    }

    if (message.length > this.MAX_MESSAGE_LENGTH) {
      return { isValid: false, error: `Message is too long (max ${this.MAX_MESSAGE_LENGTH} characters)` };
    }

    // Security validation
    if (ContentSanitizer.containsSuspiciousPatterns(message)) {
      return { isValid: false, error: 'Message contains invalid content' };
    }

    // Sanitize the message
    const sanitized = ContentSanitizer.sanitizeText(message);

    return { isValid: true, sanitized };
  }
}

// Username validation
export class UsernameValidator {
  private static readonly MIN_LENGTH = 3;
  private static readonly MAX_LENGTH = 20;
  private static readonly VALID_PATTERN = /^[a-zA-Z0-9_]+$/;
  private static readonly RESERVED_USERNAMES = [
    'admin', 'administrator', 'root', 'system', 'support', 'help',
    'api', 'www', 'mail', 'email', 'test', 'demo', 'guest',
    'irachat', 'moderator', 'mod', 'staff', 'official',
  ];

  static validate(username: string): ValidationResult {
    if (!username || typeof username !== 'string') {
      return { isValid: false, error: 'Username is required' };
    }

    const trimmed = username.trim().toLowerCase();

    // Length validation
    if (trimmed.length < this.MIN_LENGTH) {
      return { isValid: false, error: `Username must be at least ${this.MIN_LENGTH} characters` };
    }

    if (trimmed.length > this.MAX_LENGTH) {
      return { isValid: false, error: `Username must be no more than ${this.MAX_LENGTH} characters` };
    }

    // Pattern validation
    if (!this.VALID_PATTERN.test(trimmed)) {
      return { isValid: false, error: 'Username can only contain letters, numbers, and underscores' };
    }

    // Reserved username check
    if (this.RESERVED_USERNAMES.includes(trimmed)) {
      return { isValid: false, error: 'This username is not available' };
    }

    // Cannot start with underscore or number
    if (trimmed.startsWith('_') || /^\d/.test(trimmed)) {
      return { isValid: false, error: 'Username must start with a letter' };
    }

    return { isValid: true, sanitized: trimmed };
  }
}

// Phone number validation (enhanced)
export class PhoneValidator {
  private static readonly E164_PATTERN = /^\+[1-9]\d{1,14}$/;
  private static readonly SUSPICIOUS_PATTERNS = [
    /^\+1{10,}$/, // Too many 1s
    /^\+(\d)\1{9,}$/, // Repeated digits
    /^\+999\d+$/, // Test numbers
    /^\+000\d+$/, // Invalid area codes
    /^\+1234567890$/, // Common test number
  ];

  static validate(phoneNumber: string): ValidationResult {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      return { isValid: false, error: 'Phone number is required' };
    }

    // Sanitize input
    const sanitized = phoneNumber.replace(/[^\d+]/g, '');
    
    // Ensure it starts with +
    const formatted = sanitized.startsWith('+') ? sanitized : '+' + sanitized;

    // E.164 format validation
    if (!this.E164_PATTERN.test(formatted)) {
      return { isValid: false, error: 'Invalid phone number format. Use international format (+1234567890)' };
    }

    // Check for suspicious patterns
    for (const pattern of this.SUSPICIOUS_PATTERNS) {
      if (pattern.test(formatted)) {
        return { isValid: false, error: 'Invalid phone number' };
      }
    }

    // Length validation
    const digits = formatted.replace(/\D/g, '');
    if (digits.length < 7 || digits.length > 15) {
      return { isValid: false, error: 'Phone number length is invalid' };
    }

    return { isValid: true, sanitized: formatted };
  }
}

// File validation
export class FileValidator {
  private static readonly ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  private static readonly ALLOWED_VIDEO_TYPES = ['video/mp4', 'video/webm', 'video/ogg'];
  private static readonly ALLOWED_AUDIO_TYPES = ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'];
  private static readonly ALLOWED_DOCUMENT_TYPES = ['application/pdf', 'text/plain'];
  
  private static readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  private static readonly MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
  private static readonly MAX_VIDEO_SIZE = 100 * 1024 * 1024; // 100MB

  static validateFile(file: File, type: 'image' | 'video' | 'audio' | 'document'): ValidationResult {
    if (!file) {
      return { isValid: false, error: 'File is required' };
    }

    // Size validation
    let maxSize = this.MAX_FILE_SIZE;
    if (type === 'image') maxSize = this.MAX_IMAGE_SIZE;
    if (type === 'video') maxSize = this.MAX_VIDEO_SIZE;

    if (file.size > maxSize) {
      return { isValid: false, error: `File size exceeds ${Math.round(maxSize / 1024 / 1024)}MB limit` };
    }

    // Type validation
    let allowedTypes: string[] = [];
    switch (type) {
      case 'image':
        allowedTypes = this.ALLOWED_IMAGE_TYPES;
        break;
      case 'video':
        allowedTypes = this.ALLOWED_VIDEO_TYPES;
        break;
      case 'audio':
        allowedTypes = this.ALLOWED_AUDIO_TYPES;
        break;
      case 'document':
        allowedTypes = this.ALLOWED_DOCUMENT_TYPES;
        break;
    }

    if (!allowedTypes.includes(file.type)) {
      return { isValid: false, error: `File type ${file.type} is not allowed` };
    }

    // Filename validation
    const filename = file.name;
    if (!/^[a-zA-Z0-9._-]+$/.test(filename)) {
      return { isValid: false, error: 'Filename contains invalid characters' };
    }

    return { isValid: true };
  }
}

// General input validator
export class InputValidator {
  /**
   * Validate any input with custom rules
   */
  static validate(
    input: any,
    rules: {
      required?: boolean;
      type?: 'string' | 'number' | 'boolean' | 'array' | 'object';
      minLength?: number;
      maxLength?: number;
      pattern?: RegExp;
      custom?: (_value: any) => ValidationResult;
    }
  ): ValidationResult {
    // Required validation
    if (rules.required && (input === null || input === undefined || input === '')) {
      return { isValid: false, error: 'This field is required' };
    }

    // Type validation
    if (input !== null && input !== undefined && rules.type) {
      const actualType = Array.isArray(input) ? 'array' : typeof input;
      if (actualType !== rules.type) {
        return { isValid: false, error: `Expected ${rules.type}, got ${actualType}` };
      }
    }

    // String-specific validations
    if (typeof input === 'string') {
      if (rules.minLength && input.length < rules.minLength) {
        return { isValid: false, error: `Minimum length is ${rules.minLength}` };
      }

      if (rules.maxLength && input.length > rules.maxLength) {
        return { isValid: false, error: `Maximum length is ${rules.maxLength}` };
      }

      if (rules.pattern && !rules.pattern.test(input)) {
        return { isValid: false, error: 'Invalid format' };
      }
    }

    // Custom validation
    if (rules.custom) {
      return rules.custom(input);
    }

    return { isValid: true, sanitized: input };
  }
}

// Validators are already exported above - removing duplicate exports

/**
 * IraChat Unique Background Wallpaper Component
 * Beautiful animated sky blue wallpaper with subtle patterns
 */

import React, { useEffect, useRef } from 'react';
import { Animated, Dimensions, StyleSheet, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Circle, Path, Ellipse, Defs, RadialGradient, Stop } from 'react-native-svg';
import { IRACHAT_COLORS, ANIMATIONS } from '../../styles/iraChatDesignSystem';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface IraChatWallpaperProps {
  variant?: 'default' | 'chat' | 'auth' | 'minimal';
  animated?: boolean;
  opacity?: number;
  children?: React.ReactNode;
}

export const IraChatWallpaper: React.FC<IraChatWallpaperProps> = ({
  variant = 'default',
  animated = true,
  opacity = 1,
  children,
}) => {
  const floatingAnimation = useRef(new Animated.Value(0)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const rotateAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (animated) {
      // Floating animation for clouds
      Animated.loop(
        Animated.sequence([
          Animated.timing(floatingAnimation, {
            toValue: 1,
            duration: ANIMATIONS.slower * 4,
            useNativeDriver: true,
          }),
          Animated.timing(floatingAnimation, {
            toValue: 0,
            duration: ANIMATIONS.slower * 4,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Pulse animation for dots
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.2,
            duration: ANIMATIONS.slower * 2,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: ANIMATIONS.slower * 2,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Rotation animation for subtle elements
      Animated.loop(
        Animated.timing(rotateAnimation, {
          toValue: 1,
          duration: ANIMATIONS.slower * 8,
          useNativeDriver: true,
        })
      ).start();
    }
  }, [animated, floatingAnimation, pulseAnimation, rotateAnimation]);

  const floatingTranslateY = floatingAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });

  const rotateInterpolate = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const getGradientColors = () => {
    switch (variant) {
      case 'chat':
        return [IRACHAT_COLORS.background, IRACHAT_COLORS.backgroundDark, IRACHAT_COLORS.primaryLight];
      case 'auth':
        return IRACHAT_COLORS.skyGradient;
      case 'minimal':
        return [IRACHAT_COLORS.surface, IRACHAT_COLORS.background];
      default:
        return IRACHAT_COLORS.blueGradient;
    }
  };

  const renderFloatingClouds = () => (
    <Animated.View
      style={[
        styles.floatingElement,
        {
          transform: [{ translateY: floatingTranslateY }],
        },
      ]}
    >
      <Svg width={screenWidth} height={screenHeight} style={styles.svgOverlay}>
        <Defs>
          <RadialGradient id="cloudGradient" cx="50%" cy="50%" r="50%">
            <Stop offset="0%" stopColor={IRACHAT_COLORS.primaryLight} stopOpacity="0.1" />
            <Stop offset="100%" stopColor={IRACHAT_COLORS.primary} stopOpacity="0.05" />
          </RadialGradient>
        </Defs>
        
        {/* Floating clouds */}
        <Ellipse cx={screenWidth * 0.2} cy={screenHeight * 0.15} rx="80" ry="30" fill="url(#cloudGradient)" />
        <Ellipse cx={screenWidth * 0.7} cy={screenHeight * 0.25} rx="100" ry="40" fill="url(#cloudGradient)" />
        <Ellipse cx={screenWidth * 0.4} cy={screenHeight * 0.8} rx="120" ry="35" fill="url(#cloudGradient)" />
        <Ellipse cx={screenWidth * 0.8} cy={screenHeight * 0.7} rx="90" ry="25" fill="url(#cloudGradient)" />
      </Svg>
    </Animated.View>
  );

  const renderPulsingDots = () => (
    <Animated.View
      style={[
        styles.pulsingElement,
        {
          transform: [{ scale: pulseAnimation }],
        },
      ]}
    >
      <Svg width={screenWidth} height={screenHeight} style={styles.svgOverlay}>
        {/* Scattered dots pattern */}
        {Array.from({ length: 20 }).map((_, index) => (
          <Circle
            key={index}
            cx={Math.random() * screenWidth}
            cy={Math.random() * screenHeight}
            r="2"
            fill={IRACHAT_COLORS.primary}
            opacity={0.1 + Math.random() * 0.1}
          />
        ))}
      </Svg>
    </Animated.View>
  );

  const renderWavePattern = () => (
    <Animated.View
      style={[
        styles.rotatingElement,
        {
          transform: [{ rotate: rotateInterpolate }],
        },
      ]}
    >
      <Svg width={screenWidth} height={screenHeight} style={styles.svgOverlay}>
        {/* Subtle wave patterns */}
        <Path
          d={`M0,${screenHeight * 0.3} Q${screenWidth * 0.25},${screenHeight * 0.25} ${screenWidth * 0.5},${screenHeight * 0.3} T${screenWidth},${screenHeight * 0.3} V${screenHeight} H0 Z`}
          fill={IRACHAT_COLORS.primaryLight}
          opacity="0.03"
        />
        <Path
          d={`M0,${screenHeight * 0.6} Q${screenWidth * 0.25},${screenHeight * 0.55} ${screenWidth * 0.5},${screenHeight * 0.6} T${screenWidth},${screenHeight * 0.6} V${screenHeight} H0 Z`}
          fill={IRACHAT_COLORS.primary}
          opacity="0.02"
        />
      </Svg>
    </Animated.View>
  );

  return (
    <View style={[styles.container, { opacity }]}>
      {/* Base gradient background */}
      <LinearGradient
        colors={getGradientColors() as any}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      
      {/* Animated pattern layers */}
      {animated && variant !== 'minimal' && (
        <>
          {renderWavePattern()}
          {renderFloatingClouds()}
          {renderPulsingDots()}
        </>
      )}
      
      {/* Content overlay */}
      {children && (
        <View style={styles.contentOverlay}>
          {children}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  gradient: {
    flex: 1,
  },
  svgOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  floatingElement: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  pulsingElement: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  rotatingElement: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  contentOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});

export default IraChatWallpaper;

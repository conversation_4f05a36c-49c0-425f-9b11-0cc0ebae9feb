import React from 'react';
import { View, Text } from 'react-native';

interface ChatListConnectionStatusProps {
  status: 'connected' | 'connecting' | 'disconnected';
}

const getConnectionStatusColor = (status: 'connected' | 'connecting' | 'disconnected') => {
  switch (status) {
    case 'connected': return '#10B981';
    case 'connecting': return '#F59E0B';
    case 'disconnected': return '#EF4444';
    default: return '#6B7280';
  }
};

export const ChatListConnectionStatus: React.FC<ChatListConnectionStatusProps> = ({ status }) => {
  if (status === 'connected') return null;

  return (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 8,
      backgroundColor: status === 'connecting' ? '#FEF3C7' : '#FEE2E2',
    }}>
      <View style={{
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: getConnectionStatusColor(status),
        marginRight: 8,
      }} />
      <Text style={{ 
        fontSize: 12, 
        color: status === 'connecting' ? '#92400E' : '#7F1D1D' 
      }}>
        {status === 'connecting' ? 'Connecting...' : 'Disconnected'}
      </Text>
    </View>
  );
};

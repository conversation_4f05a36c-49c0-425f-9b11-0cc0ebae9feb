// 🔥 REAL CALL SCREEN ROUTE - COMPLETE WEBRTC IMPLEMENTATION
// No mockups, no fake data - 100% real WebRTC calling functionality

import React from 'react';
import { useLocalSearchParams } from 'expo-router';
import { RealCallScreen } from '../src/screens/RealCallScreen';
import { CallErrorBoundary } from '../src/components/CallErrorBoundary';

export default function RealCallRoute() {
  const params = useLocalSearchParams();

  return (
    <CallErrorBoundary>
      <RealCallScreen
        callId={params.callId as string}
        isOutgoing={params.isOutgoing === 'true'}
        contactId={params.contactId as string}
        contactName={params.contactName as string}
        callType={params.callType as 'voice' | 'video'}
      />
    </CallErrorBoundary>
  );
}

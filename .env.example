# 🔥 IRACHAT ENVIRONMENT CONFIGURATION
# Complete configuration for Firebase, WebRTC, and Push Notifications
# Copy this file to .env and replace with your actual credentials

# ==================== FIREBASE CONFIGURATION ====================
# Get these from Firebase Console > Project Settings > General

# Android Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key-here
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project-id.firebasestorage.app
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
EXPO_PUBLIC_FIREBASE_APP_ID=your-android-app-id
EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id

# iOS Configuration
# EXPO_PUBLIC_FIREBASE_API_KEY_IOS=your-ios-api-key-here
# EXPO_PUBLIC_FIREBASE_APP_ID_IOS=your-ios-app-id-here

# ==================== EXPO PROJECT CONFIGURATION ====================
# Get these from Expo Dashboard > Your Project > Settings
EXPO_PUBLIC_PROJECT_ID=your-expo-project-id
EXPO_PUBLIC_OWNER=your-expo-username

# ==================== WEBRTC CONFIGURATION ====================
# STUN servers for peer discovery (free Google STUN servers)
EXPO_PUBLIC_STUN_SERVER_URL=stun:stun.l.google.com:19302
EXPO_PUBLIC_STUN_SERVER_URL_2=stun:stun1.l.google.com:19302

# TURN servers for NAT traversal (optional for production)
# You can use free TURN servers for testing or get your own for production
EXPO_PUBLIC_TURN_SERVER_URL=turn:openrelay.metered.ca:80
EXPO_PUBLIC_TURN_SERVER_USERNAME=openrelayproject
EXPO_PUBLIC_TURN_SERVER_PASSWORD=openrelayproject

# Alternative TURN servers (if you have your own)
# EXPO_PUBLIC_TURN_SERVER_URL=turn:your-turn-server.com:3478
# EXPO_PUBLIC_TURN_SERVER_USERNAME=your-turn-username
# EXPO_PUBLIC_TURN_SERVER_PASSWORD=your-turn-password

# WebRTC Configuration
EXPO_PUBLIC_WEBRTC_ICE_CANDIDATE_POOL_SIZE=10

# ==================== PUSH NOTIFICATIONS REMOVED ====================
# Push notification functionality has been removed from IraChat
# App now uses real-time signaling only for call notifications

# ==================== DEVELOPMENT CONFIGURATION ====================
# Development environment settings
NODE_ENV=development
APP_VARIANT=development

# ==================== NOTES ====================
# 1. All EXPO_PUBLIC_ variables are accessible in your React Native app
# 2. Variables without EXPO_PUBLIC_ are only available in Cloud Functions
# 3. Never commit your actual .env file with real credentials
# 4. For production, use Expo Secrets or environment-specific configs

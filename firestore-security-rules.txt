rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

<<<<<<< HEAD
    // DEVELOPMENT RULES - Allow authenticated users access to everything
    // This fixes the permissions error while maintaining basic security

    // Helper function
    function isAuthenticated() {
      return request.auth != null;
    }

    // Allow all operations for authenticated users (development mode)
    match /{document=**} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Fallback: Allow unauthenticated read for public content discovery
    // This helps with contact discovery and public profiles
    match /users/{userId} {
      allow read: if true;
    }

    match /userProfiles/{userId} {
      allow read: if true;
    }

    match /updates/{updateId} {
      allow read: if true;
    }
=======
    // PRODUCTION SECURITY RULES - Real Firebase Authentication Required

    // Users collection - users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null && request.auth.uid == userId;
    }

    // Chats collection - only participants can access
    match /chats/{chatId} {
      allow read, write: if request.auth != null &&
        request.auth.uid in resource.data.participantIds;
      allow create: if request.auth != null &&
        request.auth.uid in request.resource.data.participantIds;
    }

    // Messages subcollection - only chat participants can access
    match /chats/{chatId}/messages/{messageId} {
      allow read, write: if request.auth != null &&
        request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participantIds;
      allow create: if request.auth != null &&
        request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participantIds &&
        request.auth.uid == request.resource.data.senderId;
    }

    // Groups collection - only members can access
    match /groups/{groupId} {
      allow read: if request.auth != null &&
        (request.auth.uid in resource.data.memberIds ||
         request.auth.uid in resource.data.adminIds);
      allow write: if request.auth != null &&
        request.auth.uid in resource.data.adminIds;
      allow create: if request.auth != null &&
        request.auth.uid in request.resource.data.adminIds;
    }

    // Group messages - only group members can access
    match /groups/{groupId}/messages/{messageId} {
      allow read, write: if request.auth != null &&
        (request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.memberIds ||
         request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.adminIds);
      allow create: if request.auth != null &&
        (request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.memberIds ||
         request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.adminIds) &&
        request.auth.uid == request.resource.data.senderId;
    }

    // Calls collection - only participants can access
    match /calls/{callId} {
      allow read, write: if request.auth != null &&
        (request.auth.uid == resource.data.callerId ||
         request.auth.uid == resource.data.receiverId);
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.callerId;
    }

    // Call logs - users can only access their own logs
    match /callLogs/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Updates/Posts - public read, authenticated write
    match /updates/{updateId} {
      allow read: if true; // Public read access for updates
      allow write, create: if request.auth != null &&
        request.auth.uid == request.resource.data.userId;
      allow update: if request.auth != null &&
        (request.auth.uid == resource.data.userId ||
         request.auth.uid in resource.data.likedBy ||
         request.auth.uid in resource.data.viewedBy);
    }

    // Comments on updates - authenticated users can read/write
    match /updates/{updateId}/comments/{commentId} {
      allow read: if true; // Public read access
      allow write, create: if request.auth != null &&
        request.auth.uid == request.resource.data.userId;
    }

    // User contacts - users can only access their own contacts
    match /userContacts/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Block lists - users can only access their own block list
    match /blockLists/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Settings - users can only access their own settings
    match /userSettings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

>>>>>>> 0ea9978a491748beb593b9ca0ca18c2f10a53438
  }
}

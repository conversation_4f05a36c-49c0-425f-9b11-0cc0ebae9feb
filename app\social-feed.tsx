
import { useEffect, useState } from "react";
import {
  Alert,
  Image,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import PostCard from "../src/components/cards/PostCard";
import Pagination from "../src/components/shared/Pagination";

interface Post {
  _id: string;
  text: string;
  author: {
    _id: string;
    name: string;
    username: string;
    image: string;
  };
  createdAt: string;
  children: any[];
  likes: string[];
  isOwner: boolean;
  repostOf?: any;
  parentId?: string;
  group?: any;
}

// interface SearchParams {
//   page?: number;
// }

export default function SocialFeedScreen() {
  const router = useRouter();
  const [posts, setPosts] = useState<Post[]>([]);
  const [, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    loadUserAndPosts();
  }, [currentPage]);

  const loadUserAndPosts = async () => {
    try {
      // Load current user from Firebase Auth
      const { getAuth } = await import('firebase/auth');
      const { doc, getDoc } = await import('firebase/firestore');
      const { db } = await import('../src/config/firebase');

      const auth = getAuth();
      const currentFirebaseUser = auth.currentUser;

      if (currentFirebaseUser) {
        // Get user profile from Firestore
        const userRef = doc(db, 'users', currentFirebaseUser.uid);
        const userSnap = await getDoc(userRef);

        if (userSnap.exists()) {
          const userData = userSnap.data();
          setCurrentUser({
            _id: currentFirebaseUser.uid,
            id: currentFirebaseUser.uid,
            ...userData
          });
        }
      }

      // Load posts from Firebase
      const result = await fetchPosts(currentPage, 10);
      const postsWithOwnership = await Promise.all(
        result.posts.map(async (post: any) => {
          const isOwner = currentFirebaseUser
            ? await isPostByUser(currentFirebaseUser.uid, post._id)
            : false;
          return { ...post, isOwner };
        }),
      );

      setPosts(postsWithOwnership);
      setHasNextPage(result.isNext);
    } catch (error) {
      console.error("❌ Error loading posts from Firebase:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Real Firebase implementation
  const fetchPosts = async (page: number, limit: number) => {
    try {
      const { collection, query, orderBy, limit: firestoreLimit, startAfter, getDocs } = await import('firebase/firestore');
      const { db } = await import('../src/config/firebase');

      let postsQuery = query(
        collection(db, 'posts'),
        orderBy('createdAt', 'desc'),
        firestoreLimit(limit)
      );

      // Handle pagination
      if (page > 1) {
        const previousPageQuery = query(
          collection(db, 'posts'),
          orderBy('createdAt', 'desc'),
          firestoreLimit((page - 1) * limit)
        );
        const previousSnapshot = await getDocs(previousPageQuery);
        const lastDoc = previousSnapshot.docs[previousSnapshot.docs.length - 1];
        if (lastDoc) {
          postsQuery = query(
            collection(db, 'posts'),
            orderBy('createdAt', 'desc'),
            startAfter(lastDoc),
            firestoreLimit(limit)
          );
        }
      }

      const snapshot = await getDocs(postsQuery);
      const posts = snapshot.docs.map(doc => ({
        _id: doc.id,
        ...doc.data()
      }));

      // Check if there are more posts
      const nextPageQuery = query(
        collection(db, 'posts'),
        orderBy('createdAt', 'desc'),
        firestoreLimit((page * limit) + 1)
      );
      const nextSnapshot = await getDocs(nextPageQuery);
      const hasNext = nextSnapshot.docs.length > (page * limit);

      return {
        posts,
        isNext: hasNext,
      };
    } catch (error) {
      console.error('❌ Error fetching posts from Firebase:', error);
      return {
        posts: [],
        isNext: false,
      };
    }
  };

  // Real Firebase implementation
  const isPostByUser = async (userId: string, postId: string) => {
    try {
      const { doc, getDoc } = await import('firebase/firestore');
      const { db } = await import('../src/config/firebase');

      const postRef = doc(db, 'posts', postId);
      const postSnap = await getDoc(postRef);

      if (postSnap.exists()) {
        const postData = postSnap.data();
        return postData.author._id === userId || postData.authorId === userId;
      }

      return false;
    } catch (error) {
      console.error('❌ Error checking post ownership:', error);
      return false;
    }
  };

  const handleCreatePost = () => {
    Alert.prompt(
      "Create Post",
      "What's on your mind?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Post",
          onPress: async (text) => {
            if (text && text.trim() && currentUser) {
              try {
                const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
                const { db } = await import('../src/config/firebase');

                const newPost = {
                  text: text.trim(),
                  author: {
                    _id: currentUser._id || currentUser.id,
                    name: currentUser.displayName || currentUser.name || "User",
                    username: currentUser.username || currentUser.email?.split('@')[0] || "user",
                    image: currentUser.photoURL || currentUser.profilePicture || "",
                  },
                  authorId: currentUser._id || currentUser.id,
                  createdAt: serverTimestamp(),
                  children: [],
                  likes: [],
                  repostOf: null,
                  parentId: null,
                  group: null,
                  likesCount: 0,
                  commentsCount: 0,
                  sharesCount: 0,
                };

                await addDoc(collection(db, 'posts'), newPost);
                Alert.alert("Success", "Your post has been shared!");

                // Refresh posts
                setRefreshing(true);
                setCurrentPage(1);
                loadUserAndPosts();
              } catch (error) {
                console.error('❌ Error creating post:', error);
                Alert.alert("Error", "Failed to create post. Please try again.");
              }
            }
          },
        },
      ],
      "plain-text",
      "",
      "default"
    );
  };

  const handleRefresh = () => {
    setRefreshing(true);
    setCurrentPage(1);
    loadUserAndPosts();
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-blue-500 px-6 py-4 pt-12">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity onPress={() => router.back()}>
            <Text className="text-white text-lg">← Back</Text>
          </TouchableOpacity>
          <Text className="text-white text-xl" style={{ fontWeight: "700" }}>
            Social Feed
          </Text>
          <TouchableOpacity
            onPress={handleCreatePost}
            className="bg-white/20 p-2 rounded-full"
          >
            <Text className="text-white text-lg">✏️</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Content - Following your example pattern */}
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View className="mt-6 px-4">
          {/* Following your example's conditional rendering */}
          {posts.length === 0 ? (
            <View className="bg-white rounded-lg p-8 items-center">
              <Image
                source={require("../assets/images/comment.png")}
                className="w-16 h-16 mb-4"
                style={{ tintColor: "#9CA3AF" }}
                resizeMode="contain"
              />
              <Text
                className="text-gray-500 text-lg text-center"
                style={{ fontWeight: "500" }}
              >
                No posts found
              </Text>
              <Text className="text-gray-400 text-center mt-2">
                Be the first to share something!
              </Text>
              <TouchableOpacity
                onPress={handleCreatePost}
                className="bg-blue-500 px-6 py-3 rounded-lg mt-4"
              >
                <Text className="text-white" style={{ fontWeight: "500" }}>
                  Create Post
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View>
              {/* Following your example's post mapping */}
              {posts.map((post) => (
                <View className="mb-4" key={post._id}>
                  <PostCard
                    id={post._id}
                    currentUserId={currentUser?.id || ""}
                    owner={post.isOwner}
                    content={post.text}
                    author={post.author}
                    createdAt={post.createdAt}
                    comments={post.children}
                    likes={post.likes}
                    liked={currentUser?.likedPosts?.includes(post._id) || false}
                  />
                </View>
              ))}

              {/* Following your example's Pagination */}
              <Pagination
                path="/social-feed"
                pageNumber={currentPage}
                isNext={hasNextPage}
                onPageChange={handlePageChange}
              />
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

/**
 * IraChat Design System
 * Beautiful UI with Sky Blue Branding and Unique Wallpaper
 */

import { Dimensions, Platform } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Comprehensive Responsive Dimensions
export const DIMENSIONS = {
  screenWidth,
  screenHeight,

  // Device Categories
  isSmallPhone: screenWidth < 360,      // Small phones (iPhone SE, etc.)
  isMediumPhone: screenWidth >= 360 && screenWidth < 390,  // Standard phones
  isLargePhone: screenWidth >= 390 && screenWidth < 430,   // Large phones (iPhone 14 Pro, etc.)
  isExtraLargePhone: screenWidth >= 430 && screenWidth < 768, // Extra large phones
  isTablet: screenWidth >= 768 && screenWidth < 1024,      // Tablets
  isLargeTablet: screenWidth >= 1024,   // Large tablets/desktop

  // Responsive Breakpoints
  breakpoints: {
    xs: 320,  // Extra small phones
    sm: 360,  // Small phones
    md: 390,  // Medium phones
    lg: 430,  // Large phones
    xl: 768,  // Tablets
    '2xl': 1024, // Large tablets
  },

  // Safe Area Calculations
  statusBarHeight: Platform.OS === 'ios' ? (screenHeight >= 812 ? 44 : 20) : 0,
  bottomSafeArea: Platform.OS === 'ios' ? (screenHeight >= 812 ? 34 : 0) : 0,
  headerHeight: Platform.OS === 'ios' ? (screenHeight >= 812 ? 88 : 64) : 56,

  // Responsive Helpers
  wp: (percentage: number) => (screenWidth * percentage) / 100,
  hp: (percentage: number) => (screenHeight * percentage) / 100,

  // Font Scale based on device size
  fontScale: screenWidth < 360 ? 0.9 : screenWidth >= 430 ? 1.1 : 1.0,

  // Responsive spacing function
  getResponsiveSpacing: (baseSize: number) => {
    if (screenWidth < 360) return Math.round(baseSize * 0.8);
    if (screenWidth >= 430) return Math.round(baseSize * 1.2);
    return baseSize;
  },

  // Responsive border radius function
  getResponsiveBorderRadius: (baseRadius: number) => {
    if (screenWidth < 360) return Math.round(baseRadius * 0.8);
    if (screenWidth >= 430) return Math.round(baseRadius * 1.1);
    return baseRadius;
  },

  // Responsive icon size function
  getResponsiveIconSize: (baseSize: number) => {
    if (screenWidth < 360) return Math.round(baseSize * 0.9);
    if (screenWidth >= 430) return Math.round(baseSize * 1.1);
    return baseSize;
  },
};

// IraChat Brand Colors - Sky Blue Theme
export const IRACHAT_COLORS = {
  // Primary Sky Blue Palette
  primary: '#87CEEB',           // Sky Blue
  primaryDark: '#4682B4',       // Steel Blue
  primaryLight: '#B0E0E6',      // Powder Blue
  primaryGradient: ['#87CEEB', '#4682B4'],
  
  // Secondary Colors
  secondary: '#20B2AA',         // Light Sea Green
  accent: '#FFD700',            // Gold
  success: '#32CD32',           // Lime Green
  warning: '#FFA500',           // Orange
  error: '#FF6B6B',             // Light Red
  
  // Background Colors
  background: '#F0F8FF',        // Alice Blue
  backgroundDark: '#E6F3FF',    // Lighter Alice Blue
  surface: '#FFFFFF',           // White
  surfaceLight: '#F8FAFC',      // Very Light Gray
  surfaceElevated: '#FAFCFF',   // Very Light Blue
  
  // Text Colors
  text: '#2C3E50',              // Dark Blue Gray
  textSecondary: '#5D6D7E',     // Medium Blue Gray
  textMuted: '#85929E',         // Light Blue Gray
  textOnPrimary: '#FFFFFF',     // White
  
  // Border Colors
  border: '#D5DBDB',            // Light Gray
  borderLight: '#EBF0F0',       // Very Light Gray
  borderFocus: '#87CEEB',       // Sky Blue
  
  // Status Colors
  online: '#2ECC71',            // Green
  offline: '#95A5A6',           // Gray
  away: '#F39C12',              // Orange
  busy: '#E74C3C',              // Red
  
  // Chat Colors
  sentMessage: '#87CEEB',       // Sky Blue
  receivedMessage: '#F8F9FA',   // Light Gray
  systemMessage: '#E8F4FD',     // Very Light Blue
  
  // Overlay Colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(135, 206, 235, 0.1)',
  
  // Gradient Combinations
  skyGradient: ['#87CEEB', '#B0E0E6', '#E0F6FF'],
  blueGradient: ['#4682B4', '#87CEEB', '#B0E0E6'],
  sunsetGradient: ['#FFD700', '#FFA500', '#87CEEB'],
};

// Responsive Typography System
export const TYPOGRAPHY = {
  // Font Families
  fontFamily: Platform.select({
    ios: 'System',
    android: 'Roboto',
    default: 'System',
  }),
  fontFamilyBold: Platform.select({
    ios: 'System',
    android: 'Roboto',
    default: 'System',
  }),

  // Responsive Font Sizes
  fontSize: {
    xs: Math.round(12 * DIMENSIONS.fontScale),
    sm: Math.round(14 * DIMENSIONS.fontScale),
    base: Math.round(16 * DIMENSIONS.fontScale),
    lg: Math.round(18 * DIMENSIONS.fontScale),
    xl: Math.round(20 * DIMENSIONS.fontScale),
    '2xl': Math.round(24 * DIMENSIONS.fontScale),
    '3xl': Math.round(30 * DIMENSIONS.fontScale),
    '4xl': Math.round(36 * DIMENSIONS.fontScale),
    '5xl': Math.round(48 * DIMENSIONS.fontScale),
  },

  // Responsive Line Heights
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },

  // Font Weights
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },

  // Responsive Text Scaling Helper
  getResponsiveFontSize: (size: number) => Math.round(size * DIMENSIONS.fontScale),
};

// Responsive Spacing System
export const SPACING = {
  xs: DIMENSIONS.getResponsiveSpacing(4),
  sm: DIMENSIONS.getResponsiveSpacing(8),
  md: DIMENSIONS.getResponsiveSpacing(16),
  lg: DIMENSIONS.getResponsiveSpacing(24),
  xl: DIMENSIONS.getResponsiveSpacing(32),
  '2xl': DIMENSIONS.getResponsiveSpacing(48),
  '3xl': DIMENSIONS.getResponsiveSpacing(64),
  '4xl': DIMENSIONS.getResponsiveSpacing(96),

  // Responsive Spacing Helpers
  getHorizontalSpacing: () => DIMENSIONS.isSmallPhone ? 12 : DIMENSIONS.isMediumPhone ? 16 : 20,
  getVerticalSpacing: () => DIMENSIONS.isSmallPhone ? 8 : DIMENSIONS.isMediumPhone ? 12 : 16,
  getCardPadding: () => DIMENSIONS.isSmallPhone ? 12 : DIMENSIONS.isMediumPhone ? 16 : 20,
  getButtonPadding: () => DIMENSIONS.isSmallPhone ? 12 : DIMENSIONS.isMediumPhone ? 16 : 20,
};

// Responsive Border Radius System
export const BORDER_RADIUS = {
  none: 0,
  sm: DIMENSIONS.getResponsiveBorderRadius(4),
  md: DIMENSIONS.getResponsiveBorderRadius(8),
  lg: DIMENSIONS.getResponsiveBorderRadius(12),
  xl: DIMENSIONS.getResponsiveBorderRadius(16),
  '2xl': DIMENSIONS.getResponsiveBorderRadius(24),
  full: 9999,

  // Responsive Border Radius Helpers
  getCardRadius: () => DIMENSIONS.isSmallPhone ? 8 : DIMENSIONS.isMediumPhone ? 12 : 16,
  getButtonRadius: () => DIMENSIONS.isSmallPhone ? 8 : DIMENSIONS.isMediumPhone ? 12 : 16,
  getInputRadius: () => DIMENSIONS.isSmallPhone ? 8 : DIMENSIONS.isMediumPhone ? 10 : 12,
};

// Shadow System
export const SHADOWS = {
  sm: {
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 16,
  },
};

// Animation Durations
export const ANIMATIONS = {
  fast: 150,
  normal: 300,
  slow: 500,
  slower: 800,
};



// IraChat Unique Wallpaper Patterns
export const WALLPAPER_PATTERNS = {
  // Subtle sky blue patterns for backgrounds
  dots: 'data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle cx="10" cy="10" r="1" fill="%2387CEEB" opacity="0.1"/></svg>',
  waves: 'data:image/svg+xml,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path d="M0 20c10-10 30 10 40 0v20H0z" fill="%2387CEEB" opacity="0.05"/></svg>',
  clouds: 'data:image/svg+xml,<svg width="60" height="40" viewBox="0 0 60 40" xmlns="http://www.w3.org/2000/svg"><ellipse cx="30" cy="20" rx="20" ry="8" fill="%23B0E0E6" opacity="0.1"/></svg>',
};

// Component Styles
export const COMPONENT_STYLES = {
  // Button Styles
  button: {
    primary: {
      backgroundColor: IRACHAT_COLORS.primary,
      borderRadius: BORDER_RADIUS.lg,
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.lg,
      ...SHADOWS.md,
    },
    secondary: {
      backgroundColor: IRACHAT_COLORS.surface,
      borderWidth: 1,
      borderColor: IRACHAT_COLORS.primary,
      borderRadius: BORDER_RADIUS.lg,
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.lg,
      ...SHADOWS.sm,
    },
    ghost: {
      backgroundColor: 'transparent',
      borderRadius: BORDER_RADIUS.lg,
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.lg,
    },
  },
  
  // Input Styles
  input: {
    default: {
      backgroundColor: IRACHAT_COLORS.surface,
      borderWidth: 1,
      borderColor: IRACHAT_COLORS.border,
      borderRadius: BORDER_RADIUS.lg,
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.md,
      fontSize: TYPOGRAPHY.fontSize.base,
      color: IRACHAT_COLORS.text,
    },
    focused: {
      borderColor: IRACHAT_COLORS.borderFocus,
      ...SHADOWS.sm,
    },
  },
  
  // Card Styles
  card: {
    default: {
      backgroundColor: IRACHAT_COLORS.surface,
      borderRadius: BORDER_RADIUS.xl,
      padding: SPACING.lg,
      ...SHADOWS.md,
    },
    elevated: {
      backgroundColor: IRACHAT_COLORS.surfaceElevated,
      borderRadius: BORDER_RADIUS.xl,
      padding: SPACING.lg,
      ...SHADOWS.lg,
    },
  },
  
  // Header Styles
  header: {
    default: {
      backgroundColor: IRACHAT_COLORS.primary,
      paddingVertical: SPACING.lg,
      paddingHorizontal: SPACING.md,
      ...SHADOWS.md,
    },
    gradient: {
      paddingVertical: SPACING.lg,
      paddingHorizontal: SPACING.md,
      ...SHADOWS.md,
    },
  },
};

// Responsive Layout Helpers
export const LAYOUT = {
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
    paddingHorizontal: SPACING.getHorizontalSpacing(),
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.getHorizontalSpacing(),
  },
  row: {
    flexDirection: 'row' as const,
    alignItems: 'center',
  },
  column: {
    flexDirection: 'column' as const,
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  spaceAround: {
    justifyContent: 'space-around',
  },
  spaceEvenly: {
    justifyContent: 'space-evenly',
  },

  // Responsive Layout Helpers
  getResponsiveContainer: () => ({
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
    paddingHorizontal: SPACING.getHorizontalSpacing(),
    paddingTop: DIMENSIONS.statusBarHeight,
    paddingBottom: DIMENSIONS.bottomSafeArea,
  }),

  getResponsiveCard: () => ({
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.getCardRadius(),
    padding: SPACING.getCardPadding(),
    marginHorizontal: SPACING.getHorizontalSpacing(),
    marginVertical: SPACING.getVerticalSpacing(),
    ...SHADOWS.md,
  }),

  getResponsiveButton: () => ({
    paddingHorizontal: SPACING.getButtonPadding(),
    paddingVertical: SPACING.getVerticalSpacing(),
    borderRadius: BORDER_RADIUS.getButtonRadius(),
    minHeight: DIMENSIONS.isSmallPhone ? 44 : DIMENSIONS.isMediumPhone ? 48 : 52,
  }),

  getResponsiveInput: () => ({
    paddingHorizontal: SPACING.getHorizontalSpacing(),
    paddingVertical: SPACING.getVerticalSpacing(),
    borderRadius: BORDER_RADIUS.getInputRadius(),
    minHeight: DIMENSIONS.isSmallPhone ? 44 : DIMENSIONS.isMediumPhone ? 48 : 52,
    fontSize: TYPOGRAPHY.fontSize.base,
  }),
};

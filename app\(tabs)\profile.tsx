import { useRouter } from "expo-router";
import { useState } from "react";
import {
  Alert,
  Image,

  Text,
  TouchableOpacity,
  View,
  StyleSheet,

} from "react-native";

import { LinearGradient } from 'expo-linear-gradient';
import { useDispatch, useSelector } from "react-redux";
import { Ionicons } from "@expo/vector-icons";

import { performCompleteLogout } from "../../src/services/logoutService";
import { RootState } from "../../src/redux/store";
import { navigationService, ROUTES } from "../../src/services/navigationService";
import { _FloatingActionButton, _QuickNavActions } from "../../src/components/NavigationHelper";
import { ResponsiveContainer } from "../../src/components/ui/ResponsiveContainer";
import { ResponsiveCard } from "../../src/components/ui/ResponsiveCard";
import { ResponsiveHeader } from "../../src/components/ui/ResponsiveHeader";
import { AnimatedButton } from "../../src/components/ui/AnimatedButton";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from "../../src/styles/iraChatDesignSystem";
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing, DeviceInfo } from "../../src/utils/responsiveUtils";

// Profile tabs configuration
const profileTabs = [
  {
    label: "Chats",
    value: "chats",
    icon: require("../../assets/images/comment.png"),
  },
  {
    label: "Media",
    value: "media",
    icon: require("../../assets/images/posts.png"),
  },
  {
    label: "Settings",
    value: "settings",
    icon: require("../../assets/images/setting.png"),
  },
];

export default function ProfileScreen() {
  const dispatch = useDispatch();
  const router = useRouter();

  // Get current user from Redux store
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const isAuthenticated = useSelector(
    (state: RootState) => state.user.isAuthenticated,
  );

  // Debug log to see what's in currentUser
  console.log("🔍 Profile: currentUser data:", currentUser);

  const [activeTab, setActiveTab] = useState("chats");

  const handleEditProfile = () => {
    navigationService.navigate(ROUTES.PROFILE.EDIT);
  };

  const handleLogout = () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Logout",
        style: "destructive",
        onPress: async () => {
          try {
            console.log("🚪 Profile: Starting COMPLETE logout process...");

            // Use the new complete logout service
            const result = await performCompleteLogout(dispatch, router);

            if (result.success) {
              console.log("✅ Profile: Complete logout successful");

              // Show success message
              Alert.alert(
                "Logged Out",
                "You have been successfully logged out.",
                [{ text: "OK" }],
              );
            } else {
              console.warn("⚠️ Profile: Logout completed with warnings");
              Alert.alert(
                "Logged Out",
                "You have been logged out (with some warnings).",
                [{ text: "OK" }],
              );
            }
          } catch (error) {
            console.error("❌ Profile: Complete logout error:", error);
            Alert.alert("Error", "Failed to logout. Please try again.");
          }
        },
      },
    ]);
  };

  // Show loading or error state if user data is not available
  if (!isAuthenticated || !currentUser) {
    return (
      <ResponsiveContainer centered>
        <Text style={styles.loadingText}>Loading profile...</Text>
        <Text style={styles.loadingSubtext}>
          Please wait while we load your information
        </Text>
      </ResponsiveContainer>
    );
  }

  return (
    <ResponsiveContainer scrollable paddingHorizontal={false} paddingVertical={false}>
      {/* Profile Header */}
      <LinearGradient
        colors={IRACHAT_COLORS.primaryGradient as any}
        style={styles.profileHeader}
      >
        <View style={styles.avatarContainer}>
          <View style={styles.avatarWrapper}>
            {currentUser?.avatar ? (
              <Image
                source={{ uri: currentUser.avatar }}
                style={styles.avatar as any}
                resizeMode="cover"
              />
            ) : (
              <Text style={styles.avatarText}>
                {(currentUser?.name || "U").charAt(0).toUpperCase()}
              </Text>
            )}
          </View>
          <Text style={styles.userName}>
            {currentUser?.name || "User"}
          </Text>

          {/* Username */}
          {currentUser?.username && (
            <Text className="text-blue-100 text-sm mb-1">
              {currentUser.username.startsWith("@")
                ? currentUser.username
                : `@${currentUser.username}`}
            </Text>
          )}

          <Text className="text-blue-100 text-sm">
            {currentUser?.phoneNumber}
          </Text>
          <Text className="text-blue-200 text-xs mt-2 text-center">
            {currentUser?.status || "I Love IraChat"}
          </Text>
        </View>
      </LinearGradient>

      {/* Profile Tabs */}
      <View className="mt-6">
        {/* Tab Headers */}
        <View className="flex-row bg-gray-50 mx-4 rounded-lg p-1">
          {profileTabs.map((tab) => (
            <TouchableOpacity
              key={tab.value}
              onPress={() => setActiveTab(tab.value)}
              className={`flex-1 flex-row items-center justify-center py-3 px-2 rounded-md ${
                activeTab === tab.value ? "bg-white shadow-sm" : ""
              }`}
            >
              <Image
                source={tab.icon}
                className="w-4 h-4 mr-1"
                style={{
                  tintColor: activeTab === tab.value ? "#3B82F6" : "#6B7280",
                }}
                resizeMode="contain"
              />
              <Text
                className={`text-xs ${
                  activeTab === tab.value ? "text-blue-500" : "text-gray-600"
                }`}
                style={{ fontWeight: "500" }}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        <View className="mt-4 px-4">
          {activeTab === "chats" && (
            <View className="bg-white rounded-lg p-6">
              <Text
                className="text-gray-800 text-lg mb-4"
                style={{ fontWeight: "600" }}
              >
                Recent Chats
              </Text>
              <View className="items-center py-8">
                <Image
                  source={require("../../assets/images/comment.png")}
                  className="w-16 h-16 mb-4"
                  style={{ tintColor: "#9CA3AF" }}
                  resizeMode="contain"
                />
                <Text className="text-gray-500 text-center">
                  No recent chats
                </Text>
                <Text className="text-gray-400 text-sm text-center mt-1">
                  Start a conversation to see your chats here
                </Text>
              </View>
            </View>
          )}

          {activeTab === "media" && (
            <View className="bg-white rounded-lg p-6">
              <Text
                className="text-gray-800 text-lg mb-4"
                style={{ fontWeight: "600" }}
              >
                Shared Media
              </Text>
              <View className="items-center py-8">
                <Image
                  source={require("../../assets/images/posts.png")}
                  className="w-16 h-16 mb-4"
                  style={{ tintColor: "#9CA3AF" }}
                  resizeMode="contain"
                />
                <Text className="text-gray-500 text-center">
                  No media shared
                </Text>
                <Text className="text-gray-400 text-sm text-center mt-1">
                  Photos and videos you share will appear here
                </Text>
              </View>
            </View>
          )}

          {activeTab === "settings" && (
            <View className="bg-white rounded-lg p-4">
              <Text
                className="text-gray-800 text-lg mb-4"
                style={{ fontWeight: "600" }}
              >
                Settings
              </Text>

              <TouchableOpacity
                onPress={handleEditProfile}
                className="flex-row items-center py-4 border-b border-gray-100"
              >
                <Image
                  source={require("../../assets/images/profile.png")}
                  className="w-6 h-6 mr-4"
                  style={{ tintColor: "#6B7280" }}
                  resizeMode="contain"
                />
                <Text className="text-gray-800 text-base flex-1">
                  Edit Profile
                </Text>
                <Text className="text-gray-400 text-lg">›</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => navigationService.navigate(ROUTES.SETTINGS.NOTIFICATIONS)}
                className="flex-row items-center py-4 border-b border-gray-100"
              >
                <Image
                  source={require("../../assets/images/notification.png")}
                  className="w-6 h-6 mr-4"
                  style={{ tintColor: "#6B7280" }}
                  resizeMode="contain"
                />
                <Text className="text-gray-800 text-base flex-1">
                  Notifications
                </Text>
                <Text className="text-gray-400 text-lg">›</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => navigationService.navigate(ROUTES.SETTINGS.PRIVACY)}
                className="flex-row items-center py-4 border-b border-gray-100"
              >
                <Image
                  source={require("../../assets/images/setting.png")}
                  className="w-6 h-6 mr-4"
                  style={{ tintColor: "#6B7280" }}
                  resizeMode="contain"
                />
                <Text className="text-gray-800 text-base flex-1">Privacy</Text>
                <Text className="text-gray-400 text-lg">›</Text>
              </TouchableOpacity>

              {/* Additional Navigation Options */}
              <TouchableOpacity
                onPress={() => navigationService.navigate(ROUTES.SETTINGS.ACCOUNT)}
                className="flex-row items-center py-4 border-b border-gray-100"
              >
                <Ionicons name="person-circle-outline" size={24} color="#6B7280" style={{ marginRight: 16 }} />
                <Text className="text-gray-800 text-base flex-1">Account Settings</Text>
                <Text className="text-gray-400 text-lg">›</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => navigationService.openMediaGallery()}
                className="flex-row items-center py-4 border-b border-gray-100"
              >
                <Ionicons name="images-outline" size={24} color="#6B7280" style={{ marginRight: 16 }} />
                <Text className="text-gray-800 text-base flex-1">Media Gallery</Text>
                <Text className="text-gray-400 text-lg">›</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => navigationService.navigate(ROUTES.HELP.HELP)}
                className="flex-row items-center py-4"
              >
                <Ionicons name="help-circle-outline" size={24} color="#6B7280" style={{ marginRight: 16 }} />
                <Text className="text-gray-800 text-base flex-1">Help & Support</Text>
                <Text className="text-gray-400 text-lg">›</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      {/* App Info */}
      <View className="px-4 py-4 border-t border-gray-200">
        <View className="items-center mb-6">
          <Image
            source={require("../../assets/images/LOGO.png")}
            className="w-12 h-12 mb-2"
            resizeMode="contain"
          />
          <Text className="text-gray-600 text-sm">IraChat v1.0.0</Text>
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          onPress={handleLogout}
          className="bg-red-500 py-3 px-6 rounded-lg mx-4"
        >
          <Text
            className="text-white text-center text-base"
            style={{ fontWeight: "600" }}
          >
            Logout
          </Text>
        </TouchableOpacity>
      </View>
    </ResponsiveContainer>
  );
}

const styles = StyleSheet.create({
  loadingText: {
    fontSize: ResponsiveTypography.fontSize.lg,
    color: IRACHAT_COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    textAlign: 'center',
  },
  loadingSubtext: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textMuted,
    textAlign: 'center',
    marginTop: ResponsiveSpacing.sm,
  },
  profileHeader: {
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.xl,
    paddingTop: DeviceInfo.statusBarHeight + ResponsiveSpacing.xl,
    alignItems: 'center',
    ...SHADOWS.md,
  },
  avatarContainer: {
    alignItems: 'center',
  },
  avatarWrapper: {
    width: ComponentSizes.avatarSize.xlarge,
    height: ComponentSizes.avatarSize.xlarge,
    borderRadius: ComponentSizes.avatarSize.xlarge / 2,
    backgroundColor: IRACHAT_COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: ResponsiveSpacing.md,
    ...SHADOWS.lg,
  },
  avatar: {
    width: ComponentSizes.avatarSize.xlarge - 8,
    height: ComponentSizes.avatarSize.xlarge - 8,
    borderRadius: (ComponentSizes.avatarSize.xlarge - 8) / 2,
  },
  avatarText: {
    fontSize: ResponsiveTypography.fontSize['2xl'],
    color: IRACHAT_COLORS.primary,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
  },
  userName: {
    fontSize: ResponsiveTypography.fontSize.xl,
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    textAlign: 'center',
    marginBottom: ResponsiveSpacing.xs,
  },
  userHandle: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textOnPrimary,
    opacity: 0.9,
    textAlign: 'center',
    marginBottom: ResponsiveSpacing.xs,
  },
  userPhone: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textOnPrimary,
    opacity: 0.8,
    textAlign: 'center',
  },
  userStatus: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textOnPrimary,
    opacity: 0.7,
    textAlign: 'center',
    marginTop: ResponsiveSpacing.sm,
  },
});

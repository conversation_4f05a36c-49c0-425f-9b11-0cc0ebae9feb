import { useRouter } from "expo-router";
import { Image, Text, View, StyleSheet, Animated, Dimensions } from "react-native";
import { StatusBar } from 'expo-status-bar';
import { markAppLaunched } from "../../src/services/authStorageSimple";
import { navigationService, ROUTES } from "../../src/services/navigationService";
import { IraChatWallpaper } from "../../src/components/ui/IraChatWallpaper";
import { AnimatedButton } from "../../src/components/ui/AnimatedButton";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS, ANIMATIONS } from "../../src/styles/iraChatDesignSystem";
import { useEffect, useRef } from "react";




export default function WelcomeScreen() {

  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const scaleAnimation = useRef(new Animated.Value(0.8)).current;


  useEffect(() => {
    // Beautiful entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: ANIMATIONS.slow,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnimation, {
          toValue: 0,
          duration: ANIMATIONS.slow,
          useNativeDriver: true,
        }),
      ]),
      Animated.spring(scaleAnimation, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnimation, scaleAnimation, slideAnimation]);

  const handleCreateAccount = async () => {
    console.log("Phone Registration button clicked");
    // Mark that the user has interacted with the app (no longer a first-time user)
    await markAppLaunched();
    navigationService.navigate(ROUTES.AUTH.REGISTER);
  };



  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Beautiful animated wallpaper */}
      <IraChatWallpaper variant="auth" animated={true} />

      {/* Content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnimation,
            transform: [
              { translateY: slideAnimation },
              { scale: scaleAnimation }
            ],
          },
        ]}
      >
        {/* Logo with glow effect */}
        <View style={styles.logoContainer}>
          <View style={styles.logoGlow}>
            <Image
              source={require("../../assets/images/LOGO.png")}
              style={styles.logo}
              resizeMode="cover"
            />
          </View>
        </View>

        {/* Welcome Text with gradient */}
        <View style={styles.textContainer}>
          <Text style={styles.welcomeTitle}>
            Welcome to IraChat
          </Text>
          <Text style={styles.welcomeSubtitle}>
            Connect with friends and family through beautiful sky blue messaging
          </Text>
        </View>

        {/* Feature highlights */}
        <View style={styles.featuresContainer}>
          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureEmoji}>💬</Text>
            </View>
            <Text style={styles.featureText}>Instant Messaging</Text>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureEmoji}>📞</Text>
            </View>
            <Text style={styles.featureText}>Voice & Video Calls</Text>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureEmoji}>👥</Text>
            </View>
            <Text style={styles.featureText}>Group Chats</Text>
          </View>
        </View>



        {/* Get Started Button */}
        <View style={styles.buttonContainer}>
          <AnimatedButton
            title="Continue with Phone Number"
            onPress={handleCreateAccount}
            variant="gradient"
            size="large"
            icon="call"
            iconPosition="left"
            fullWidth={true}
            style={styles.getStartedButton}
          />

          <Text style={styles.termsText}>
            By continuing, you agree to our Terms of Service and Privacy Policy
          </Text>
        </View>
      </Animated.View>


    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
  },
  logoContainer: {
    marginBottom: SPACING.xl,
    alignItems: 'center',
  },
  logoGlow: {
    ...SHADOWS.xl,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: IRACHAT_COLORS.surface,
    padding: SPACING.sm,
  },
  logo: {
    width: 140,
    height: 140,
    borderRadius: 70,
    borderWidth: 3,
    borderColor: IRACHAT_COLORS.primaryLight,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  welcomeTitle: {
    fontSize: TYPOGRAPHY.fontSize['4xl'],
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    marginBottom: SPACING.md,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 1,
  },
  welcomeSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    lineHeight: TYPOGRAPHY.lineHeight.relaxed * TYPOGRAPHY.fontSize.lg,
    opacity: 0.95,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    paddingHorizontal: SPACING.md,
  },
  featuresContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: SPACING.xl,
    paddingHorizontal: SPACING.md,
  },
  featureItem: {
    alignItems: 'center',
    flex: 1,
  },
  featureIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: IRACHAT_COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    ...SHADOWS.md,
  },
  featureEmoji: {
    fontSize: 24,
  },
  featureText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    opacity: 0.9,
  },

  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  getStartedButton: {
    marginBottom: SPACING.lg,
  },
  termsText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    opacity: 0.8,
    lineHeight: TYPOGRAPHY.lineHeight.normal * TYPOGRAPHY.fontSize.xs,
    paddingHorizontal: SPACING.lg,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
    backgroundColor: IRACHAT_COLORS.surface,
  },
  modalTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    color: IRACHAT_COLORS.text,
  },
  closeButton: {
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: IRACHAT_COLORS.backgroundDark,
  },
});

#!/bin/bash

# 🔥 COMPLETE IRACHAT DEPLOYMENT SCRIPT
# Deploy everything: Firebase, Cloud Functions, and build development app

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}🔥 $1${NC}"
    echo -e "${PURPLE}$(printf '=%.0s' {1..50})${NC}"
}

print_status() {
    echo -e "${BLUE}🔥 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header "IRACHAT COMPLETE DEPLOYMENT"

# ==================== PREREQUISITES ====================

print_status "Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js not found. Please install Node.js 18+"
    exit 1
fi
print_success "Node.js found: $(node --version)"

# Check npm
if ! command -v npm &> /dev/null; then
    print_error "npm not found. Please install npm"
    exit 1
fi
print_success "npm found: $(npm --version)"

# Check Expo CLI
if ! command -v expo &> /dev/null; then
    print_warning "Expo CLI not found. Installing..."
    npm install -g @expo/cli
fi
print_success "Expo CLI found: $(expo --version)"

# Check EAS CLI
if ! command -v eas &> /dev/null; then
    print_warning "EAS CLI not found. Installing..."
    npm install -g eas-cli
fi
print_success "EAS CLI found"

# Check Firebase CLI
if ! command -v firebase &> /dev/null; then
    print_warning "Firebase CLI not found. Installing..."
    npm install -g firebase-tools
fi
print_success "Firebase CLI found"

# ==================== INSTALL DEPENDENCIES ====================

print_header "INSTALLING DEPENDENCIES"

print_status "Installing main dependencies..."
npm install
print_success "Main dependencies installed"

print_status "Installing Cloud Functions dependencies..."
cd functions
npm install
cd ..
print_success "Cloud Functions dependencies installed"

# ==================== FIREBASE SETUP ====================

print_header "FIREBASE SETUP"

# Check Firebase login
print_status "Checking Firebase authentication..."
if ! firebase projects:list &> /dev/null; then
    print_warning "Not logged in to Firebase. Please login:"
    firebase login
fi
print_success "Firebase authenticated"

# Initialize Firebase if needed
if [ ! -f "firebase.json" ]; then
    print_status "Initializing Firebase..."
    firebase init
else
    print_success "Firebase already initialized"
fi

# ==================== CLOUD FUNCTIONS DEPLOYMENT ====================

print_header "DEPLOYING CLOUD FUNCTIONS"

print_status "Building Cloud Functions..."
cd functions
npm run build
print_success "Cloud Functions built"

print_status "Deploying Firestore rules..."
cd ..
firebase deploy --only firestore:rules
print_success "Firestore rules deployed"

print_status "Deploying Cloud Functions..."
firebase deploy --only functions
print_success "Cloud Functions deployed"

print_status "Deploying Storage rules..."
firebase deploy --only storage
print_success "Storage rules deployed"

# ==================== EAS SETUP ====================

print_header "EAS BUILD SETUP"

# Check EAS login
print_status "Checking EAS authentication..."
if ! eas whoami &> /dev/null; then
    print_warning "Not logged in to EAS. Please login:"
    eas login
fi
print_success "EAS authenticated"

# Configure EAS project
print_status "Configuring EAS project..."
if [ ! -f "eas.json" ]; then
    eas build:configure
else
    print_success "EAS already configured"
fi

# ==================== ENVIRONMENT SETUP ====================

print_header "ENVIRONMENT CONFIGURATION"

# Check .env file
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        print_warning "No .env file found. Copying from .env.example..."
        cp .env.example .env
        print_warning "Please edit .env file with your configuration"
    else
        print_error "No .env.example file found. Please create environment configuration"
    fi
else
    print_success ".env file exists"
fi

# ==================== BUILD DEVELOPMENT APP ====================

print_header "BUILDING DEVELOPMENT APP"

print_status "Building development app for iOS..."
eas build --profile development --platform ios --non-interactive &
IOS_PID=$!

print_status "Building development app for Android..."
eas build --profile development --platform android --non-interactive &
ANDROID_PID=$!

print_status "Waiting for builds to complete..."
print_warning "This may take 10-20 minutes..."

# Wait for both builds
wait $IOS_PID
IOS_EXIT_CODE=$?

wait $ANDROID_PID
ANDROID_EXIT_CODE=$?

if [ $IOS_EXIT_CODE -eq 0 ]; then
    print_success "iOS development build completed"
else
    print_warning "iOS development build failed or skipped"
fi

if [ $ANDROID_EXIT_CODE -eq 0 ]; then
    print_success "Android development build completed"
else
    print_warning "Android development build failed or skipped"
fi

# ==================== VERIFICATION ====================

print_header "DEPLOYMENT VERIFICATION"

print_status "Running verification checks..."

# Check if critical files exist
CRITICAL_FILES=(
    "src/services/realCallService.ts"
    "src/services/soundService.ts"
    "src/services/pushNotificationService.ts"
    "src/screens/IncomingCallScreen.tsx"
    "src/components/CallErrorBoundary.tsx"
    "functions/lib/index.js"
)

for file in "${CRITICAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_success "$file exists"
    else
        print_error "$file missing"
    fi
done

# ==================== COMPLETION ====================

print_header "DEPLOYMENT COMPLETE"

echo ""
print_success "🎉 IRACHAT DEPLOYMENT SUCCESSFUL!"
echo ""
echo "📋 What was deployed:"
echo "  ✅ Firebase Cloud Functions"
echo "  ✅ Firestore security rules"
echo "  ✅ Storage security rules"
echo "  ✅ Development builds (iOS & Android)"
echo ""
echo "🔗 Useful links:"
echo "  📱 EAS Builds: https://expo.dev/accounts/[your-account]/projects/irachat/builds"
echo "  ☁️  Firebase Console: https://console.firebase.google.com/"
echo "  📊 Expo Dashboard: https://expo.dev/accounts/[your-account]/projects/irachat"
echo ""
echo "🚀 Next steps:"
echo "  1. Download development builds from EAS"
echo "  2. Install on real devices (iOS/Android)"
echo "  3. Test calling functionality"
echo "  4. Configure TURN servers for production (optional)"
echo "  5. Set up FCM server keys for push notifications"
echo ""
echo "📱 To start development server:"
echo "  expo start --dev-client"
echo ""
echo "🎯 Your app is now ready for real device testing!"
echo ""
print_success "DEPLOYMENT COMPLETED SUCCESSFULLY!"

// 🔒 Secure Error Handling and Logging Service for IraChat
// Implements security-focused error handling with proper logging

import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from './firebaseSimple';

// Error severity levels
export enum ErrorSeverity {
  _LOW = 'low',
  _MEDIUM = 'medium',
  _HIGH = 'high',
  _CRITICAL = 'critical',
}

// Error categories
export enum ErrorCategory {
  _AUTHENTICATION = 'authentication',
  _AUTHORIZATION = 'authorization',
  _VALIDATION = 'validation',
  _NETWORK = 'network',
  _DATABASE = 'database',
  _SECURITY = 'security',
  _USER_INPUT = 'user_input',
  _SYSTEM = 'system',
}

// Error log entry
interface ErrorLogEntry {
  id: string;
  timestamp: any;
  severity: ErrorSeverity;
  category: ErrorCategory;
  message: string;
  userMessage: string;
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  stackTrace?: string;
  context?: Record<string, any>;
}

// User-friendly error messages
const USER_FRIENDLY_MESSAGES = {
  [ErrorCategory.AUTHENTICATION]: 'Authentication failed. Please try logging in again.',
  [ErrorCategory.AUTHORIZATION]: 'You do not have permission to perform this action.',
  [ErrorCategory.VALIDATION]: 'Please check your input and try again.',
  [ErrorCategory.NETWORK]: 'Network error. Please check your connection and try again.',
  [ErrorCategory.DATABASE]: 'Service temporarily unavailable. Please try again later.',
  [ErrorCategory.SECURITY]: 'Security error. Please contact support if this persists.',
  [ErrorCategory.USER_INPUT]: 'Invalid input. Please check your data and try again.',
  [ErrorCategory.SYSTEM]: 'System error. Please try again later.',
};

// Security-sensitive error patterns that should not be exposed to users
const SENSITIVE_ERROR_PATTERNS = [
  /firebase/i,
  /database/i,
  /sql/i,
  /connection/i,
  /timeout/i,
  /internal/i,
  /server/i,
  /api key/i,
  /token/i,
  /credential/i,
];

export class SecureErrorHandler {
  private static sessionId: string = this.generateSessionId();

  /**
   * Generate a unique session ID for error tracking
   */
  private static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Handle and log errors securely
   */
  static async handleError(
    error: Error | any,
    category: ErrorCategory,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: Record<string, any>,
    userId?: string
  ): Promise<string> {
    try {
      // Generate error ID
      const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Extract error message
      const originalMessage = error?.message || error?.toString() || 'Unknown error';

      // Generate user-friendly message
      const userMessage = this.generateUserFriendlyMessage(originalMessage, category);

      // Create error log entry
      const logEntry: ErrorLogEntry = {
        id: errorId,
        timestamp: serverTimestamp(),
        severity,
        category,
        message: this.sanitizeErrorMessage(originalMessage),
        userMessage,
        userId,
        sessionId: this.sessionId,
        userAgent: typeof (global as any).navigator !== 'undefined' ? (global as any).navigator.userAgent : undefined,
        stackTrace: error?.stack ? this.sanitizeStackTrace(error.stack) : undefined,
        context: this.sanitizeContext(context),
      };

      // Log to console (development only)
      if (__DEV__) {
        console.group(`🚨 Error [${severity.toUpperCase()}] - ${category}`);
        console.error('Original Error:', error);
        console.log('Error ID:', errorId);
        console.log('User Message:', userMessage);
        console.log('Context:', context);
        console.groupEnd();
      }

      // Log to Firestore (production)
      if (!__DEV__) {
        await this.logToFirestore(logEntry);
      }

      // Send to external monitoring service (if configured)
      await this.sendToMonitoring(logEntry);

      return userMessage;
    } catch (loggingError) {
      console.error('❌ Failed to log error:', loggingError);
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /**
   * Generate user-friendly error message
   */
  private static generateUserFriendlyMessage(
    originalMessage: string,
    category: ErrorCategory
  ): string {
    // Check if the error message contains sensitive information
    const containsSensitiveInfo = SENSITIVE_ERROR_PATTERNS.some(pattern =>
      pattern.test(originalMessage)
    );

    if (containsSensitiveInfo) {
      return USER_FRIENDLY_MESSAGES[category];
    }

    // For validation errors, we can be more specific
    if (category === ErrorCategory.VALIDATION || category === ErrorCategory.USER_INPUT) {
      return originalMessage;
    }

    // For other categories, use generic messages
    return USER_FRIENDLY_MESSAGES[category];
  }

  /**
   * Sanitize error message for logging
   */
  private static sanitizeErrorMessage(message: string): string {
    // Remove sensitive information from error messages
    let sanitized = message;

    // Remove API keys, tokens, passwords
    sanitized = sanitized.replace(/api[_-]?key[s]?[:\s=]+[^\s]+/gi, 'API_KEY_REDACTED');
    sanitized = sanitized.replace(/token[s]?[:\s=]+[^\s]+/gi, 'TOKEN_REDACTED');
    sanitized = sanitized.replace(/password[s]?[:\s=]+[^\s]+/gi, 'PASSWORD_REDACTED');
    sanitized = sanitized.replace(/secret[s]?[:\s=]+[^\s]+/gi, 'SECRET_REDACTED');

    // Remove phone numbers and emails
    sanitized = sanitized.replace(/\+?[\d\s\-\(\)]{10,}/g, 'PHONE_REDACTED');
    sanitized = sanitized.replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, 'EMAIL_REDACTED');

    return sanitized;
  }

  /**
   * Sanitize stack trace
   */
  private static sanitizeStackTrace(stackTrace: string): string {
    // Remove file paths that might contain sensitive information
    return stackTrace.replace(/\/[^\s]+/g, 'PATH_REDACTED');
  }

  /**
   * Sanitize context data
   */
  private static sanitizeContext(context?: Record<string, any>): Record<string, any> | undefined {
    if (!context) return undefined;

    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(context)) {
      // Skip sensitive keys
      if (/password|secret|token|key|credential/i.test(key)) {
        sanitized[key] = 'REDACTED';
        continue;
      }

      // Sanitize string values
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeErrorMessage(value);
      } else if (typeof value === 'object' && value !== null) {
        // Recursively sanitize objects (limited depth)
        sanitized[key] = this.sanitizeContext(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Log error to Firestore
   */
  private static async logToFirestore(logEntry: ErrorLogEntry): Promise<void> {
    try {
      const errorRef = doc(db, 'errorLogs', logEntry.id);
      await setDoc(errorRef, logEntry);
    } catch (error) {
      console.error('❌ Failed to log error to Firestore:', error);
    }
  }

  /**
   * Send error to external monitoring service
   */
  private static async sendToMonitoring(logEntry: ErrorLogEntry): Promise<void> {
    try {
      // This would integrate with services like Sentry, LogRocket, etc.
      // For now, we'll just log to console in development
      if (__DEV__) {
        console.log('📊 Would send to monitoring service:', {
          id: logEntry.id,
          severity: logEntry.severity,
          category: logEntry.category,
          message: logEntry.message,
        });
      }
    } catch (error) {
      console.error('❌ Failed to send error to monitoring service:', error);
    }
  }

  /**
   * Handle authentication errors specifically
   */
  static async handleAuthError(error: any, context?: Record<string, any>): Promise<string> {
    return this.handleError(
      error,
      ErrorCategory.AUTHENTICATION,
      ErrorSeverity.HIGH,
      context
    );
  }

  /**
   * Handle validation errors specifically
   */
  static async handleValidationError(error: any, context?: Record<string, any>): Promise<string> {
    return this.handleError(
      error,
      ErrorCategory.VALIDATION,
      ErrorSeverity.LOW,
      context
    );
  }

  /**
   * Handle security errors specifically
   */
  static async handleSecurityError(error: any, context?: Record<string, any>): Promise<string> {
    return this.handleError(
      error,
      ErrorCategory.SECURITY,
      ErrorSeverity.CRITICAL,
      context
    );
  }

  /**
   * Handle network errors specifically
   */
  static async handleNetworkError(error: any, context?: Record<string, any>): Promise<string> {
    return this.handleError(
      error,
      ErrorCategory.NETWORK,
      ErrorSeverity.MEDIUM,
      context
    );
  }

  /**
   * Create a safe error boundary for React components
   */
  static createErrorBoundary(componentName: string) {
    return (error: Error, errorInfo: any) => {
      this.handleError(
        error,
        ErrorCategory.SYSTEM,
        ErrorSeverity.HIGH,
        {
          componentName,
          errorInfo: errorInfo?.componentStack,
        }
      );
    };
  }
}

// Export convenience functions
export const handleError = SecureErrorHandler.handleError.bind(SecureErrorHandler);
export const handleAuthError = SecureErrorHandler.handleAuthError.bind(SecureErrorHandler);
export const handleValidationError = SecureErrorHandler.handleValidationError.bind(SecureErrorHandler);
export const handleSecurityError = SecureErrorHandler.handleSecurityError.bind(SecureErrorHandler);
export const handleNetworkError = SecureErrorHandler.handleNetworkError.bind(SecureErrorHandler);

export default SecureErrorHandler;

import { useCallback } from 'react';
import { useRouter } from 'expo-router';
import { doc, deleteDoc } from 'firebase/firestore';
import { db } from '../services/firebaseSimple';

export interface ChatActionsProps {
  selectedChats: string[];
  isSelectionMode: boolean;
  onChatSelection: (_chatId: string) => void;
  onExitSelectionMode: () => void;
  onError: (_error: string) => void;
}

export interface ChatActions {
  handleChatPress: (_chatId: string) => void;
  handleDeleteChats: () => Promise<void>;
  handleRefresh: (_loadUserChats: () => Promise<void>) => Promise<void>;
}

export const useChatActions = ({
  selectedChats,
  isSelectionMode,
  onChatSelection,
  onExitSelectionMode,
  onError,
}: ChatActionsProps): ChatActions => {
  const router = useRouter();

  // Handle chat press (navigation or selection)
  const handleChatPress = useCallback((_chatId: string) => {
    if (isSelectionMode) {
      onChatSelection(_chatId);
    } else {
      router.push(`/chat/${_chatId}`);
    }
  }, [isSelectionMode, onChatSelection, router]);

  // Delete selected chats
  const handleDeleteChats = useCallback(async () => {
    try {
      for (const chatId of selectedChats) {
        await deleteDoc(doc(db, 'individual_chats', chatId));
      }
      onExitSelectionMode();
    } catch (_error) {
      console.error('❌ Error deleting chats:', _error);
      onError('Failed to delete chats');
    }
  }, [selectedChats, onExitSelectionMode, onError]);

  // Handle refresh
  const handleRefresh = useCallback(async (_loadUserChats: () => Promise<void>) => {
    try {
      await _loadUserChats();
    } catch (_error) {
      console.error("❌ Error refreshing chats:", _error);
      onError('Failed to refresh chats');
    }
  }, [onError]);

  return {
    handleChatPress,
    handleDeleteChats,
    handleRefresh,
  };
};

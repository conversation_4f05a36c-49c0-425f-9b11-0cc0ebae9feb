// Active Call Screen Route
import { useEffect, useState } from 'react';
import { BackHandler, View, _StyleSheet } from 'react-native';
import { _StatusBar } from 'expo-status-bar';
import { RealCallScreen } from '../src/components/CallScreen';
import { useRealCallManager } from '../src/hooks/useRealCallManager';
import { realCallService } from '../src/services/realCallService';
import { auth } from '../src/services/firebaseSimple';
import { _ResponsiveContainer } from '../src/components/ui/ResponsiveContainer';
import { _IRACHAT_COLORS } from '../src/styles/iraChatDesignSystem';
import { _DeviceInfo } from '../src/utils/responsiveUtils';

export default function CallScreenPage() {
  const _currentUser = auth?.currentUser;
  const {
    callState,
    endCall,
    toggleMute,
    toggleVideo,
    toggleSpeaker,
    switchCamera
  } = useRealCallManager();

  const [streams, setStreams] = useState<{
    localStream: any;
    remoteStream: any;
  }>({ localStream: null, remoteStream: null });

  // Get streams from real calling service
  useEffect(() => {
    const updateStreams = () => {
      const localStream = realCallService.getLocalStream();
      const remoteStream = realCallService.getRemoteStream();
      setStreams({ localStream, remoteStream });
    };

    // Update streams initially
    updateStreams();

    // Update streams periodically during call
    const interval = setInterval(updateStreams, 1000);

    return () => clearInterval(interval);
  }, [callState.currentCall]);

  // Prevent back button during call
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // Don't allow back button to close call screen
      return true;
    });

    return () => backHandler.remove();
  }, []);

  if (!callState.currentCall) {
    return <View style={{ flex: 1, backgroundColor: '#000' }} />;
  }

  // This screen is for individual calls only (voice/video)
  // Group calls are handled separately

  return (
    <RealCallScreen
      call={callState.currentCall}
      localStream={streams.localStream}
      remoteStream={streams.remoteStream}
      onEndCall={endCall}
      onToggleMute={toggleMute}
      onToggleVideo={toggleVideo}
      onToggleSpeaker={toggleSpeaker}
      onToggleCamera={switchCamera}
      isMuted={callState.isMuted}
      isVideoEnabled={callState.isVideoEnabled}
      isSpeakerOn={callState.isSpeakerOn}
      callDuration={callState.callDuration}
      connectionQuality={callState.connectionQuality}
    />
  );
}



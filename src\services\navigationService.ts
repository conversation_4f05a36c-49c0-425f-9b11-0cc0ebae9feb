/**
 * 🧭 IRACHAT NAVIGATION SERVICE
 * Centralized navigation system for seamless routing throughout the app
 */

import { router } from 'expo-router';

export interface NavigationParams {
  [key: string]: string | number | boolean | undefined;
}

// 📱 ALL IRACHAT ROUTES - Comprehensive route definitions
export const ROUTES = {
  // 🏠 Main App Routes
  HOME: '/',
  INDEX: '/index',

  // 🔐 Authentication Routes
  AUTH: {
    WELCOME: '/(auth)/welcome',
    REGISTER: '/(auth)/register',
    PHONE_REGISTER: '/(auth)/phone-register',
    LOGIN: '/(auth)/index',
  },

  // 📋 Tab Routes
  TABS: {
    CHATS: '/(tabs)/index',
    GROUPS: '/(tabs)/groups',
    CALLS: '/(tabs)/calls',
    UPDATES: '/(tabs)/updates',
    PROFILE: '/(tabs)/profile',
    SETTINGS: '/(tabs)/settings',
  },

  // 💬 Chat & Messaging Routes
  CHAT: {
    INDIVIDUAL: (chatId: string) => `/chat/${chatId}`,
    GROUP: (groupId: string) => `/group-chat?groupId=${groupId}`,
    NEW_CHAT: '/new-chat',
    CHAT_MANAGEMENT: '/chat-management',
    ENHANCED_GROUP: '/enhanced-group-chat',
    INDIVIDUAL_CHAT: '/individual-chat',
  },

  // 👥 Group Routes
  GROUP: {
    CREATE: '/create-group',
    SETTINGS: (groupId: string) => `/group-settings?groupId=${groupId}`,
    SELECT_MEMBERS: '/select-group-members',
    CHAT: (groupId: string) => `/group-chat?groupId=${groupId}`,
  },

  // 📞 Call Routes
  CALL: {
    VIDEO: (contactId: string, contactName: string) => 
      `/video-call?contactId=${contactId}&contactName=${encodeURIComponent(contactName)}`,
    VOICE: (contactId: string, contactName: string) => 
      `/voice-call?contactId=${contactId}&contactName=${encodeURIComponent(contactName)}`,
    INCOMING: (callId: string) => `/incoming-call?callId=${callId}`,
    INCOMING_REAL: (callId: string) => `/incoming-call-real?callId=${callId}`,
    REAL_CALL: '/real-call',
    SAFE_VIDEO: '/video-call-safe',
  },

  // 👤 Profile & Account Routes
  PROFILE: {
    MAIN: '/(tabs)/profile',
    VIEW: (userId: string) => `/profile/${userId}`,
    EDIT: '/edit-profile',
    ACCOUNT_SETTINGS: '/account-settings',
    PRIVACY_SETTINGS: '/privacy-settings',
    NOTIFICATIONS_SETTINGS: '/notifications-settings',
    THEME_SETTINGS: '/theme-settings',
  },

  // 📱 Social & Updates Routes
  SOCIAL: {
    FEED: '/social-feed',
    CREATE_UPDATE: '/create-update',
    UPDATE_DETAIL: (updateId: string) => `/update/${updateId}`,
    COMPREHENSIVE_UPDATES: '/(tabs)/comprehensive-updates',
  },

  // 🔍 Search & Discovery Routes
  SEARCH: {
    GLOBAL: '/global-search',
    CONTACTS: '/contacts',
    FAST_CONTACTS: '/fast-contacts',
  },

  // 📷 Media & Files Routes
  MEDIA: {
    CAMERA: '/camera',
    GALLERY: '/media-gallery',
    DOWNLOADS: '/downloaded-media',
  },

  // ⚙️ Settings & Preferences Routes
  SETTINGS: {
    MAIN: '/(tabs)/settings',
    ACCOUNT: '/account-settings',
    PRIVACY: '/privacy-settings',
    NOTIFICATIONS: '/notifications-settings',
    THEME: '/theme-settings',
    EXPORT_DATA: '/export-data',
    CHAT_MANAGEMENT: '/chat-management',
  },

  // ℹ️ Help & Support Routes
  HELP: {
    SUPPORT: '/help-support',
    HELP: '/help',
    ABOUT: '/about',
  },

  // 📂 Organization Routes
  ORGANIZATION: {
    ARCHIVES: '/archives',
    PINNED_MESSAGES: '/pinned-messages',
    INVITE_FRIENDS: '/invite-friends',
  },
} as const;

// 🧭 NAVIGATION SERVICE CLASS
class NavigationService {
  /**
   * Navigate to a specific route
   */
  navigate(route: string, params?: NavigationParams) {
    try {
      if (params) {
        const queryString = new URLSearchParams(
          Object.entries(params).reduce((acc, [key, value]) => {
            if (value !== undefined) {
              acc[key] = String(value);
            }
            return acc;
          }, {} as Record<string, string>)
        ).toString();
        
        const separator = route.includes('?') ? '&' : '?';
        router.push(`${route}${separator}${queryString}` as any);
      } else {
        router.push(route as any);
      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }

  /**
   * Replace current route
   */
  replace(route: string, params?: NavigationParams) {
    try {
      if (params) {
        const queryString = new URLSearchParams(
          Object.entries(params).reduce((acc, [key, value]) => {
            if (value !== undefined) {
              acc[key] = String(value);
            }
            return acc;
          }, {} as Record<string, string>)
        ).toString();
        
        const separator = route.includes('?') ? '&' : '?';
        router.replace(`${route}${separator}${queryString}` as any);
      } else {
        router.replace(route as any);
      }
    } catch (error) {
      console.error('Navigation replace error:', error);
    }
  }

  /**
   * Go back to previous screen
   */
  goBack() {
    try {
      if (router.canGoBack()) {
        router.back();
      } else {
        // Fallback to home if can't go back
        this.navigate(ROUTES.TABS.CHATS);
      }
    } catch (error) {
      console.error('Navigation back error:', error);
      this.navigate(ROUTES.TABS.CHATS);
    }
  }

  /**
   * Navigate to home/main screen
   */
  goHome() {
    this.navigate(ROUTES.TABS.CHATS);
  }

  /**
   * Navigate to home screen (alias for goHome)
   */
  navigateToHome() {
    this.goHome();
  }

  // 💬 CHAT NAVIGATION HELPERS
  openChat(chatId: string, isGroup: boolean = false) {
    if (isGroup) {
      this.navigate(ROUTES.CHAT.GROUP(chatId));
    } else {
      this.navigate(ROUTES.CHAT.INDIVIDUAL(chatId));
    }
  }

  openNewChat() {
    this.navigate(ROUTES.CHAT.NEW_CHAT);
  }

  // 👥 GROUP NAVIGATION HELPERS
  createGroup() {
    this.navigate(ROUTES.GROUP.CREATE);
  }

  openGroupSettings(groupId: string) {
    this.navigate(ROUTES.GROUP.SETTINGS(groupId));
  }

  // 📞 CALL NAVIGATION HELPERS
  startVideoCall(contactId: string, contactName: string) {
    this.navigate(ROUTES.CALL.VIDEO(contactId, contactName));
  }

  startVoiceCall(contactId: string, contactName: string) {
    this.navigate(ROUTES.CALL.VOICE(contactId, contactName));
  }

  // 👤 PROFILE NAVIGATION HELPERS
  openProfile() {
    this.navigate(ROUTES.TABS.PROFILE);
  }

  editProfile() {
    this.navigate(ROUTES.PROFILE.EDIT);
  }

  openSettings() {
    this.navigate(ROUTES.SETTINGS.MAIN);
  }

  // 🔍 SEARCH NAVIGATION HELPERS
  openGlobalSearch() {
    this.navigate(ROUTES.SEARCH.GLOBAL);
  }

  openContacts() {
    this.navigate(ROUTES.SEARCH.CONTACTS);
  }

  // 📷 MEDIA NAVIGATION HELPERS
  openCamera() {
    this.navigate(ROUTES.MEDIA.CAMERA);
  }

  openMediaGallery() {
    this.navigate(ROUTES.MEDIA.GALLERY);
  }

  // 📱 SOCIAL NAVIGATION HELPERS
  createUpdate() {
    this.navigate(ROUTES.SOCIAL.CREATE_UPDATE);
  }

  openUpdate(updateId: string) {
    this.navigate(ROUTES.SOCIAL.UPDATE_DETAIL(updateId));
  }

  // ℹ️ HELP NAVIGATION HELPERS
  openHelp() {
    this.navigate(ROUTES.HELP.HELP);
  }

  openSupport() {
    this.navigate(ROUTES.HELP.SUPPORT);
  }

  openAbout() {
    this.navigate(ROUTES.HELP.ABOUT);
  }
}

// Export singleton instance
export const navigationService = new NavigationService();
export default navigationService;

# ESLint ignore patterns for IraChat

# Node modules
node_modules/

# Build outputs
dist/
build/

# Firebase Functions
functions/
functions/**/*
functions/**/*.ts
functions/**/*.js

# Utility scripts in root directory
*.js
!app.config.js
!babel.config.js
!metro.config.js
!postcss.config.js
!tailwind.config.js
!eslint.config.js

# Scripts directory
scripts/

# Test files
**/*.test.js
**/*.test.ts

# Generated files
*.tsbuildinfo

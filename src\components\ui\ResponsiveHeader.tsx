/**
 * Responsive Header Component for IraChat
 * Beautiful animated header with responsive design
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { IRACHAT_COLORS, TYPOGRAPHY, SHADOWS } from '../../styles/iraChatDesignSystem';
import { ResponsiveScale, ResponsiveSpacing, ResponsiveTypography, ComponentSizes } from '../../utils/responsiveUtils';

interface ResponsiveHeaderProps {
  title: string;
  subtitle?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  variant?: 'default' | 'gradient' | 'transparent';
  backgroundColor?: string;
  textColor?: string;
  showBackButton?: boolean;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  statusBarStyle?: 'light' | 'dark' | 'auto';
}

export const ResponsiveHeader: React.FC<ResponsiveHeaderProps> = ({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  onLeftPress,
  onRightPress,
  variant = 'default',
  backgroundColor,
  textColor,
  showBackButton = false,
  style,
  titleStyle,
  statusBarStyle = 'light',
}) => {
  const insets = useSafeAreaInsets();

  const headerHeight = ComponentSizes.headerHeight;
  const iconSize = ResponsiveScale.iconSize(24);
  const _buttonSize = ComponentSizes.buttonHeight.medium;

  const headerStyle: ViewStyle = {
    height: headerHeight,
    paddingTop: insets.top + ResponsiveSpacing.sm,
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingBottom: ResponsiveSpacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...style,
  };

  const getBackgroundStyle = () => {
    switch (variant) {
      case 'gradient':
        return null; // Will use LinearGradient
      case 'transparent':
        return { backgroundColor: 'transparent' };
      default:
        return {
          backgroundColor: backgroundColor || IRACHAT_COLORS.primary,
          ...SHADOWS.md,
        };
    }
  };

  const getTextColor = () => {
    return textColor || (variant === 'transparent' ? IRACHAT_COLORS.text : IRACHAT_COLORS.textOnPrimary);
  };

  const renderLeftSection = () => (
    <View style={styles.leftSection}>
      {(showBackButton || leftIcon || onLeftPress) && (
        <TouchableOpacity
          style={styles.iconButton}
          onPress={onLeftPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons
            name={leftIcon || (showBackButton ? 'arrow-back' : 'menu')}
            size={iconSize}
            color={getTextColor()}
          />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderCenterSection = () => (
    <View style={styles.centerSection}>
      <Text
        style={[
          styles.title,
          { color: getTextColor() },
          titleStyle,
        ]}
        numberOfLines={1}
      >
        {title}
      </Text>
      {subtitle && (
        <Text
          style={[
            styles.subtitle,
            { color: getTextColor(), opacity: 0.8 },
          ]}
          numberOfLines={1}
        >
          {subtitle}
        </Text>
      )}
    </View>
  );

  const renderRightSection = () => (
    <View style={styles.rightSection}>
      {(rightIcon || onRightPress) && (
        <TouchableOpacity
          style={styles.iconButton}
          onPress={onRightPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons
            name={rightIcon || 'ellipsis-vertical'}
            size={iconSize}
            color={getTextColor()}
          />
        </TouchableOpacity>
      )}
    </View>
  );

  const headerContent = (
    <>
      {renderLeftSection()}
      {renderCenterSection()}
      {renderRightSection()}
    </>
  );

  if (variant === 'gradient') {
    return (
      <>
        <StatusBar style={statusBarStyle} />
        <LinearGradient
          colors={IRACHAT_COLORS.primaryGradient as any}
          style={[headerStyle, SHADOWS.md]}
        >
          {headerContent}
        </LinearGradient>
      </>
    );
  }

  return (
    <>
      <StatusBar style={statusBarStyle} />
      <View style={[headerStyle, getBackgroundStyle()]}>
        {headerContent}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  leftSection: {
    width: ComponentSizes.buttonHeight.medium,
    alignItems: 'flex-start',
  },
  centerSection: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.md,
  },
  rightSection: {
    width: ComponentSizes.buttonHeight.medium,
    alignItems: 'flex-end',
  },
  iconButton: {
    width: ComponentSizes.buttonHeight.medium,
    height: ComponentSizes.buttonHeight.medium,
    borderRadius: ComponentSizes.buttonHeight.medium / 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  title: {
    fontSize: ResponsiveTypography.fontSize.lg,
    fontWeight: '600' as const,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: ResponsiveTypography.fontSize.sm,
    fontWeight: '500' as const,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
    marginTop: ResponsiveSpacing.xs / 2,
  },
});

export default ResponsiveHeader;

// 🔥 REAL CALL MANAGEMENT CLOUD FUNCTIONS - COMPLETE WEBRTC BACKEND
// No mockups, no fake data - 100% real WebRTC call management

import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

const db = admin.firestore();

// Real Call Interfaces
interface CallData {
  id: string;
  callerId: string;
  callerName: string;
  callerAvatar?: string;
  receiverId: string;
  receiverName: string;
  receiverAvatar?: string;
  type: 'voice' | 'video';
  status: 'ringing' | 'connecting' | 'connected' | 'ended' | 'missed' | 'declined' | 'failed';
  direction: 'incoming' | 'outgoing';
  startTime: admin.firestore.Timestamp;
  endTime?: admin.firestore.Timestamp;
  duration?: number;
  chatId?: string;
  offer?: any;
  answer?: any;
  iceCandidates?: any[];
  quality?: {
    audioQuality: number;
    videoQuality: number;
    connectionStrength: number;
    packetsLost: number;
    latency: number;
  };
  deviceInfo?: {
    platform: string;
    version: string;
    network: string;
  };
  createdAt: admin.firestore.Timestamp;
  updatedAt: admin.firestore.Timestamp;
}

// ==================== REAL CALL INITIATION ====================

export const initiateCall = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const {
      receiverId,
      receiverName,
      type,
      chatId,
      offer
    } = data;

    const callerId = context.auth.uid;

    console.log('🔥 Initiating real call:', { callerId, receiverId, type });

    // Validate input
    if (!receiverId || !receiverName || !type) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required call data');
    }

    // Get caller info
    const callerDoc = await db.collection('users').doc(callerId).get();
    if (!callerDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Caller not found');
    }

    const callerData = callerDoc.data()!;

    // Check if receiver exists and is available
    const receiverDoc = await db.collection('users').doc(receiverId).get();
    if (!receiverDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Receiver not found');
    }

    const receiverData = receiverDoc.data()!;

    // Check if receiver is online
    const receiverPresence = await db.collection('user_presence').doc(receiverId).get();
    if (!receiverPresence.exists || receiverPresence.data()?.status !== 'online') {
      throw new functions.https.HttpsError('unavailable', 'User is not available for calls');
    }

    // Check for existing active calls
    const existingCallsQuery = await db.collection('calls')
      .where('receiverId', '==', receiverId)
      .where('status', 'in', ['ringing', 'connecting', 'connected'])
      .limit(1)
      .get();

    if (!existingCallsQuery.empty) {
      throw new functions.https.HttpsError('resource-exhausted', 'User is already in a call');
    }

    // Create call document
    const callId = `call_${Date.now()}_${callerId}_${receiverId}`;
    const callData: CallData = {
      id: callId,
      callerId,
      callerName: callerData.displayName || callerData.username || 'Unknown',
      callerAvatar: callerData.photoURL,
      receiverId,
      receiverName,
      receiverAvatar: receiverData.photoURL,
      type,
      status: 'ringing',
      direction: 'outgoing',
      startTime: admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
      chatId,
      offer,
      deviceInfo: {
        platform: data.deviceInfo?.platform || 'unknown',
        version: data.deviceInfo?.version || 'unknown',
        network: data.deviceInfo?.network || 'unknown',
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
      updatedAt: admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
    };

    // Save call to Firestore
    await db.collection('calls').doc(callId).set(callData);

    // Send push notification to receiver
    await sendCallNotification(receiverId, callData);

    // Set call timeout
    setTimeout(async () => {
      try {
        const callDoc = await db.collection('calls').doc(callId).get();
        if (callDoc.exists && callDoc.data()?.status === 'ringing') {
          await db.collection('calls').doc(callId).update({
            status: 'missed',
            endTime: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log('⏰ Call timed out:', callId);
        }
      } catch (error) {
        console.error('❌ Error handling call timeout:', error);
      }
    }, 45000); // 45 seconds timeout

    console.log('✅ Call initiated successfully:', callId);
    return { success: true, callId, call: callData };
  } catch (error) {
    console.error('❌ Error initiating call:', error);
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', 'Failed to initiate call');
  }
});

// ==================== REAL CALL ANSWERING ====================

export const answerCall = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { callId, answer } = data;
    const userId = context.auth.uid;

    console.log('🔥 Answering real call:', { callId, userId });

    if (!callId) {
      throw new functions.https.HttpsError('invalid-argument', 'Call ID is required');
    }

    // Get call document
    const callDoc = await db.collection('calls').doc(callId).get();
    if (!callDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is the receiver
    if (callData.receiverId !== userId) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to answer this call');
    }

    // Check call status
    if (callData.status !== 'ringing') {
      throw new functions.https.HttpsError('failed-precondition', 'Call is not in ringing state');
    }

    // Update call status
    await db.collection('calls').doc(callId).update({
      status: 'connecting',
      answer,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    console.log('✅ Call answered successfully:', callId);
    return { success: true, callId };
  } catch (error) {
    console.error('❌ Error answering call:', error);
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', 'Failed to answer call');
  }
});

// ==================== REAL CALL DECLINING ====================

export const declineCall = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { callId, reason } = data;
    const userId = context.auth.uid;

    console.log('🔥 Declining real call:', { callId, userId, reason });

    if (!callId) {
      throw new functions.https.HttpsError('invalid-argument', 'Call ID is required');
    }

    // Get call document
    const callDoc = await db.collection('calls').doc(callId).get();
    if (!callDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is the receiver or caller
    if (callData.receiverId !== userId && callData.callerId !== userId) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to decline this call');
    }

    // Update call status
    await db.collection('calls').doc(callId).update({
      status: 'declined',
      endTime: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Log call history
    await logCallHistory(callData, 'declined');

    console.log('✅ Call declined successfully:', callId);
    return { success: true, callId };
  } catch (error) {
    console.error('❌ Error declining call:', error);
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', 'Failed to decline call');
  }
});

// ==================== REAL CALL ENDING ====================

export const endCall = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { callId, reason, duration, quality } = data;
    const userId = context.auth.uid;

    console.log('🔥 Ending real call:', { callId, userId, reason, duration });

    if (!callId) {
      throw new functions.https.HttpsError('invalid-argument', 'Call ID is required');
    }

    // Get call document
    const callDoc = await db.collection('calls').doc(callId).get();
    if (!callDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is participant
    if (callData.receiverId !== userId && callData.callerId !== userId) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to end this call');
    }

    // Update call status
    const updateData: any = {
      status: 'ended',
      endTime: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    if (duration) {
      updateData.duration = duration;
    }

    if (quality) {
      updateData.quality = quality;
    }

    await db.collection('calls').doc(callId).update(updateData);

    // Log call history
    await logCallHistory({ ...callData, ...updateData }, 'ended');

    console.log('✅ Call ended successfully:', callId);
    return { success: true, callId };
  } catch (error) {
    console.error('❌ Error ending call:', error);
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', 'Failed to end call');
  }
});

// ==================== REAL ICE CANDIDATE EXCHANGE ====================

export const exchangeIceCandidate = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { callId, candidate } = data;
    const userId = context.auth.uid;

    console.log('🔥 Exchanging ICE candidate:', { callId, userId });

    if (!callId || !candidate) {
      throw new functions.https.HttpsError('invalid-argument', 'Call ID and candidate are required');
    }

    // Get call document
    const callDoc = await db.collection('calls').doc(callId).get();
    if (!callDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is participant
    if (callData.receiverId !== userId && callData.callerId !== userId) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to exchange candidates');
    }

    // Add ICE candidate
    await db.collection('calls').doc(callId).update({
      iceCandidates: admin.firestore.FieldValue.arrayUnion(candidate),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    console.log('✅ ICE candidate exchanged successfully');
    return { success: true };
  } catch (error) {
    console.error('❌ Error exchanging ICE candidate:', error);
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', 'Failed to exchange ICE candidate');
  }
});

// ==================== HELPER FUNCTIONS ====================

async function sendCallNotification(receiverId: string, callData: CallData): Promise<void> {
  try {
    console.log('🔥 Sending real-time call signal to:', receiverId);

    // Use real-time database signaling instead of push notifications
    // Update the receiver's call status in Firestore for real-time signaling
    await db.collection('users').doc(receiverId).update({
      incomingCall: {
        callId: callData.id,
        callerId: callData.callerId,
        callerName: callData.callerName,
        callType: callData.type,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        status: 'incoming'
      },
      lastUpdated: admin.firestore.FieldValue.serverTimestamp()
    });

    console.log('✅ Real-time call signal sent successfully');
  } catch (error) {
    console.error('❌ Error sending call signal:', error);
  }
}

async function logCallHistory(callData: any, finalStatus: string): Promise<void> {
  try {
    console.log('🔥 Logging call history:', callData.id);

    const callLog = {
      id: callData.id,
      callerId: callData.callerId,
      callerName: callData.callerName,
      receiverId: callData.receiverId,
      receiverName: callData.receiverName,
      type: callData.type,
      status: finalStatus,
      startTime: callData.startTime,
      endTime: callData.endTime || admin.firestore.FieldValue.serverTimestamp(),
      duration: callData.duration || 0,
      quality: callData.quality,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    };

    // Log for caller
    await db.collection('users').doc(callData.callerId)
      .collection('call_history').doc(callData.id).set({
        ...callLog,
        direction: 'outgoing',
        contactId: callData.receiverId,
        contactName: callData.receiverName,
      });

    // Log for receiver
    await db.collection('users').doc(callData.receiverId)
      .collection('call_history').doc(callData.id).set({
        ...callLog,
        direction: 'incoming',
        contactId: callData.callerId,
        contactName: callData.callerName,
      });

    console.log('✅ Call history logged');
  } catch (error) {
    console.error('❌ Error logging call history:', error);
  }
}

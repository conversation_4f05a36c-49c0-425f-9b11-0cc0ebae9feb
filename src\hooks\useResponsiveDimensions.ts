/**
 * Responsive Dimensions Hook for IraChat
 * Provides responsive breakpoints and dimensions
 */

import { useState, useEffect } from 'react';
import { Dimensions } from 'react-native';
import { DeviceInfo } from '../utils/responsiveUtils';

interface ResponsiveDimensions {
  width: number;
  height: number;
  isXSmall: boolean;
  isSmall: boolean;
  isMedium: boolean;
  isLarge: boolean;
  isTablet: boolean;
  orientation: 'portrait' | 'landscape';
}

interface ResponsiveSpacing {
  buttonPadding: {
    vertical: number;
    horizontal: number;
  };
  screenPadding: number;
  cardPadding: number;
  sectionSpacing: number;
}

interface ResponsiveFontSizes {
  xs: number;
  sm: number;
  base: number;
  lg: number;
  xl: number;
  '2xl': number;
  '3xl': number;
}

export const useResponsiveDimensions = (): ResponsiveDimensions => {
  const [dimensions, setDimensions] = useState(() => {
    const { width, height } = Dimensions.get('window');
    return { width, height };
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions({ width: window.width, height: window.height });
    });

    return () => subscription?.remove();
  }, []);

  const { width, height } = dimensions;
  const isXSmall = width < 320;
  const isSmall = width < DeviceInfo.breakpoints.small;
  const isMedium = width >= DeviceInfo.breakpoints.small && width < DeviceInfo.breakpoints.large;
  const isLarge = width >= DeviceInfo.breakpoints.large && width < DeviceInfo.breakpoints.tablet;
  const isTablet = width >= DeviceInfo.breakpoints.tablet;
  const orientation = width > height ? 'landscape' : 'portrait';

  return {
    width,
    height,
    isXSmall,
    isSmall,
    isMedium,
    isLarge,
    isTablet,
    orientation,
  };
};

export const useResponsiveSpacing = (): ResponsiveSpacing => {
  const { isSmall, isMedium, isLarge, isTablet } = useResponsiveDimensions();

  if (isTablet) {
    return {
      buttonPadding: { vertical: 16, horizontal: 24 },
      screenPadding: 24,
      cardPadding: 20,
      sectionSpacing: 32,
    };
  }

  if (isLarge) {
    return {
      buttonPadding: { vertical: 14, horizontal: 20 },
      screenPadding: 20,
      cardPadding: 18,
      sectionSpacing: 28,
    };
  }

  if (isSmall) {
    return {
      buttonPadding: { vertical: 8, horizontal: 12 },
      screenPadding: 12,
      cardPadding: 12,
      sectionSpacing: 16,
    };
  }

  if (isMedium) {
    return {
      buttonPadding: { vertical: 12, horizontal: 18 },
      screenPadding: 16,
      cardPadding: 16,
      sectionSpacing: 24,
    };
  }

  // Small devices
  return {
    buttonPadding: { vertical: 10, horizontal: 16 },
    screenPadding: 12,
    cardPadding: 14,
    sectionSpacing: 20,
  };
};

export const useResponsiveFontSizes = (): ResponsiveFontSizes => {
  const { isSmall, isMedium, isLarge, isTablet } = useResponsiveDimensions();

  if (isTablet) {
    return {
      xs: 14,
      sm: 16,
      base: 18,
      lg: 22,
      xl: 26,
      '2xl': 32,
      '3xl': 40,
    };
  }

  if (isLarge) {
    return {
      xs: 13,
      sm: 15,
      base: 17,
      lg: 20,
      xl: 24,
      '2xl': 28,
      '3xl': 36,
    };
  }

  if (isSmall) {
    return {
      xs: 10,
      sm: 12,
      base: 14,
      lg: 16,
      xl: 18,
      '2xl': 20,
      '3xl': 24,
    };
  }

  if (isMedium) {
    return {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 22,
      '2xl': 26,
      '3xl': 32,
    };
  }

  // Small devices
  return {
    xs: 11,
    sm: 13,
    base: 15,
    lg: 17,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
  };
};

export default useResponsiveDimensions;

import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Avatar } from './Avatar';

// Unified Chat Interface
export interface UnifiedChatItem {
  id: string;
  name: string;
  avatar?: string;
  lastMessage: string;
  lastMessageTime: Date;
  lastMessageAt?: Date;
  unreadCount: number;
  isGroup: boolean;
  participants: string[];
  participantNames: { [userId: string]: string };
  participantAvatars: { [userId: string]: string };
  isOnline?: boolean;
  lastSeen?: Date;
  isTyping?: boolean;
  isPinned?: boolean;
  isMuted?: boolean;
  isArchived?: boolean;
  messageCount: number;
  mediaCount: number;
  lastMessageSender?: string;
  lastMessageType?: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice';
  lastMessageBy?: string;
  createdAt: Date;
  updatedAt: Date;
  chatSettings?: {
    notifications: boolean;
    readReceipts: boolean;
    typingIndicators: boolean;
  };
}

interface ModernChatItemProps {
  item: UnifiedChatItem;
  isSelected: boolean;
  isSelectionMode: boolean;
  onPress: (_chatId: string) => void;
  onLongPress: (_chatId: string) => void;
  onDeletePress: (_chatId: string) => void;
  formatTime: (_date: Date) => string;
  getMessagePreview: (_item: UnifiedChatItem) => string;
  getMessageTypeIcon: (_type?: string) => string | null;
}

export const ModernChatItem = React.memo<ModernChatItemProps>(({ 
  item, 
  isSelected, 
  isSelectionMode, 
  onPress, 
  onLongPress, 
  onDeletePress,
  formatTime,
  getMessagePreview,
  getMessageTypeIcon
}) => {
  return (
    <View style={{ position: 'relative' }}>
      <TouchableOpacity
        onPress={() => onPress(item.id)}
        onLongPress={() => onLongPress(item.id)}
        activeOpacity={0.7}
        accessible={true}
        accessibilityRole="button"
        accessibilityLabel={`Chat with ${item.name}${item.unreadCount > 0 ? `, ${item.unreadCount} unread messages` : ''}`}
        accessibilityHint={isSelectionMode ? "Tap to select or deselect this chat" : "Tap to open chat conversation"}
        accessibilityState={{ selected: isSelected }}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 16,
          paddingVertical: 12,
          backgroundColor: isSelected ? 'rgba(135, 206, 235, 0.1)' : 'white',
          borderLeftWidth: isSelected ? 4 : 0,
          borderLeftColor: '#87CEEB',
          borderBottomWidth: 1,
          borderBottomColor: "#f3f4f6",
        }}
      >
        {/* Selection Checkbox */}
        {isSelectionMode && (
          <View style={{ marginRight: 12 }}>
            <View
              style={{
                width: 24,
                height: 24,
                borderRadius: 12,
                borderWidth: 2,
                borderColor: isSelected ? '#87CEEB' : '#d1d5db',
                backgroundColor: isSelected ? '#87CEEB' : 'transparent',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              {isSelected && (
                <Ionicons name="checkmark" size={16} color="white" />
              )}
            </View>
          </View>
        )}

        {/* Delete Button (visible in selection mode) */}
        {isSelectionMode && (
          <TouchableOpacity
            onPress={() => onDeletePress(item.id)}
            style={{
              position: 'absolute',
              right: 16,
              top: '50%',
              transform: [{ translateY: -12 }],
              backgroundColor: '#87CEEB',
              borderRadius: 12,
              padding: 8,
              zIndex: 10,
            }}
          >
            <Ionicons name="trash" size={16} color="white" />
          </TouchableOpacity>
        )}

        {/* Avatar with online indicator */}
        <View style={{ position: 'relative', marginRight: 12 }}>
          <Avatar
            name={item.name}
            imageUrl={item.avatar}
            size="large"
            showOnlineStatus={!item.isGroup}
            isOnline={item.isOnline}
          />

          {/* Group indicator */}
          {item.isGroup && (
            <View style={{
              position: 'absolute',
              top: -4,
              right: -4,
              width: 20,
              height: 20,
              backgroundColor: '#0ea5e9',
              borderRadius: 10,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Ionicons name="people" size={10} color="white" />
            </View>
          )}
        </View>

        {/* Enhanced Chat content */}
        <View style={{ flex: 1, marginRight: 12 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 4 }}>
            <Text
              style={{
                fontSize: 16,
                fontWeight: item.unreadCount > 0 ? '700' : '600',
                color: '#1f2937',
                flex: 1,
              }}
              numberOfLines={1}
            >
              {item.name}
            </Text>

            {/* Pin indicator */}
            {item.isPinned && (
              <Ionicons name="pin" size={14} color="#87CEEB" style={{ marginRight: 8 }} />
            )}

            {/* Mute indicator */}
            {item.isMuted && (
              <Ionicons name="volume-mute" size={14} color="#9ca3af" style={{ marginRight: 8 }} />
            )}

            {/* Time */}
            <Text style={{ fontSize: 12, color: '#9ca3af' }}>
              {formatTime(item.lastMessageTime)}
            </Text>
          </View>

          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            {/* Message type icon */}
            {getMessageTypeIcon(item.lastMessageType) && (
              <Ionicons
                name={getMessageTypeIcon(item.lastMessageType) as keyof typeof Ionicons.glyphMap}
                size={14}
                color="#9ca3af"
                style={{ marginRight: 4 }}
              />
            )}

            {/* Typing indicator */}
            {item.isTyping ? (
              <Text style={{ fontSize: 14, color: '#87CEEB', fontStyle: 'italic', flex: 1 }}>
                typing...
              </Text>
            ) : (
              <Text
                style={{
                  fontSize: 14,
                  color: item.unreadCount > 0 ? '#374151' : '#6b7280',
                  fontWeight: item.unreadCount > 0 ? '600' : 'normal',
                  flex: 1,
                }}
                numberOfLines={1}
              >
                {getMessagePreview(item)}
              </Text>
            )}

            {/* Unread count */}
            {item.unreadCount > 0 && (
              <View
                style={{
                  backgroundColor: '#87CEEB',
                  borderRadius: 12,
                  minWidth: 24,
                  height: 24,
                  alignItems: 'center',
                  justifyContent: 'center',
                  paddingHorizontal: 8,
                }}
              >
                <Text style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}>
                  {item.unreadCount > 99 ? '99+' : item.unreadCount}
                </Text>
              </View>
            )}
          </View>

          {/* Additional info for groups */}
          {item.isGroup && item.participants.length > 2 && (
            <Text style={{ fontSize: 12, color: '#9ca3af', marginTop: 2 }}>
              {item.participants.length} members
            </Text>
          )}
        </View>
      </TouchableOpacity>
    </View>
  );
});

ModernChatItem.displayName = 'ModernChatItem';

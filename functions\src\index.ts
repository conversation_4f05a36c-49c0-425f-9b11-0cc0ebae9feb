// 🔥 FIREBASE CLOUD FUNCTIONS INDEX - REAL DEPLOYMENT READY
// All functions exported for deployment

import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions';

// Initialize Firebase Admin SDK
admin.initializeApp();

// Export all Cloud Functions
export {
  processScheduledMessages,
  updateMessageStatus,
  cleanupOldScheduledMessages,
  updateUserPresence
} from './scheduledMessages';

// Push notification functions removed

export {
  setupRealTimeListeners,
  handleChatUpdate,
  handleMediaUpdate,
  handleSettingsUpdate
} from './realTimeHandlers';

// Translation service removed per user request

export {
  processMediaUpload,
  generateThumbnail,
  compressMedia,
  deleteMedia
} from './mediaProcessing';

export {
  moderateContent,
  scanForSpam,
  checkImageContent,
  handleReportedContent
} from './contentModeration';

export {
  backupUserData,
  exportChatHistory,
  cleanupDeletedData,
  archiveOldChats
} from './dataManagement';

export {
  handleUserSignup,
  handleUserDeletion,
  updateUserAnalytics,
  trackUserActivity,
  updateUsername,
  checkUsernameAvailability
} from './userManagement';

// 🔥 Real Call Management Functions
export {
  initiateCall,
  answerCall,
  declineCall,
  endCall,
  exchangeIceCandidate
} from './callManagement';

// Health check function
export const healthCheck = functions.https.onRequest((req: any, res: any) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    functions: [
      'processScheduledMessages',
      'sendPushNotification',
      'setupRealTimeListeners',
      'translateMessage',
      'processMediaUpload',
      'moderateContent',
      'backupUserData',
      'handleUserSignup'
    ]
  });
});

console.log('🔥 IraChat Cloud Functions initialized successfully');

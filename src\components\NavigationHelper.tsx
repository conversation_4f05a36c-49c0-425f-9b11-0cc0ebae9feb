/**
 * 🧭 NAVIGATION HELPER COMPONENT
 * Universal navigation component for seamless routing throughout IraChat
 */

import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { navigationService } from '../services/navigationService';

interface NavigationHelperProps {
  currentScreen?: string;
  showBackButton?: boolean;
  showHomeButton?: boolean;
  customActions?: {
    icon: string;
    label: string;
    onPress: () => void;
    color?: string;
  }[];
}

export const NavigationHelper: React.FC<NavigationHelperProps> = ({
  _currentScreen,
  showBackButton = true,
  showHomeButton = false,
  customActions = [],
}) => {
  return (
    <View style={styles.container}>
      {/* Back Button */}
      {showBackButton && (
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => navigationService.goBack()}
          accessibilityLabel="Go Back"
        >
          <Ionicons name="arrow-back" size={24} color="#667eea" />
          <Text style={styles.navButtonText}>Back</Text>
        </TouchableOpacity>
      )}

      {/* Home Button */}
      {showHomeButton && (
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => navigationService.goHome()}
          accessibilityLabel="Go Home"
        >
          <Ionicons name="home" size={24} color="#667eea" />
          <Text style={styles.navButtonText}>Home</Text>
        </TouchableOpacity>
      )}

      {/* Custom Actions */}
      {customActions.map((action, index) => (
        <TouchableOpacity
          key={index}
          style={styles.navButton}
          onPress={action.onPress}
          accessibilityLabel={action.label}
        >
          <Ionicons 
            name={action.icon as any} 
            size={24} 
            color={action.color || "#667eea"} 
          />
          <Text style={[styles.navButtonText, { color: action.color || "#667eea" }]}>
            {action.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

// 🚀 QUICK NAVIGATION ACTIONS
export const QuickNavActions = {
  // Chat Actions
  newChat: {
    icon: 'chatbubble-outline',
    label: 'New Chat',
    onPress: () => navigationService.openNewChat(),
  },
  
  createGroup: {
    icon: 'people-outline',
    label: 'Create Group',
    onPress: () => navigationService.createGroup(),
  },

  // Call Actions
  videoCall: (contactId: string, contactName: string) => ({
    icon: 'videocam-outline',
    label: 'Video Call',
    onPress: () => navigationService.startVideoCall(contactId, contactName),
  }),

  voiceCall: (contactId: string, contactName: string) => ({
    icon: 'call-outline',
    label: 'Voice Call',
    onPress: () => navigationService.startVoiceCall(contactId, contactName),
  }),

  // Media Actions
  camera: {
    icon: 'camera-outline',
    label: 'Camera',
    onPress: () => navigationService.openCamera(),
  },

  gallery: {
    icon: 'images-outline',
    label: 'Gallery',
    onPress: () => navigationService.openMediaGallery(),
  },

  // Search Actions
  search: {
    icon: 'search-outline',
    label: 'Search',
    onPress: () => navigationService.openGlobalSearch(),
  },

  contacts: {
    icon: 'people-circle-outline',
    label: 'Contacts',
    onPress: () => navigationService.openContacts(),
  },

  // Settings Actions
  settings: {
    icon: 'settings-outline',
    label: 'Settings',
    onPress: () => navigationService.openSettings(),
  },

  profile: {
    icon: 'person-outline',
    label: 'Profile',
    onPress: () => navigationService.openProfile(),
  },

  // Help Actions
  help: {
    icon: 'help-circle-outline',
    label: 'Help',
    onPress: () => navigationService.openHelp(),
  },

  support: {
    icon: 'headset-outline',
    label: 'Support',
    onPress: () => navigationService.openSupport(),
  },
};

// 📱 FLOATING ACTION BUTTON COMPONENT
interface FloatingActionButtonProps {
  actions: {
    icon: string;
    label: string;
    onPress: () => void;
    color?: string;
    backgroundColor?: string;
  }[];
  mainAction?: {
    icon: string;
    onPress: () => void;
    backgroundColor?: string;
  };
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  actions,
  mainAction,
}) => {
  const [isExpanded, setIsExpanded] = React.useState(false);

  const handleMainPress = () => {
    if (mainAction) {
      mainAction.onPress();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <View style={styles.fabContainer}>
      {/* Expanded Actions */}
      {isExpanded && (
        <View style={styles.fabActions}>
          {actions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.fabAction,
                { backgroundColor: action.backgroundColor || '#FFFFFF' }
              ]}
              onPress={() => {
                action.onPress();
                setIsExpanded(false);
              }}
              accessibilityLabel={action.label}
            >
              <Ionicons 
                name={action.icon as any} 
                size={20} 
                color={action.color || "#667eea"} 
              />
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Main FAB */}
      <TouchableOpacity
        style={[
          styles.fab,
          { backgroundColor: mainAction?.backgroundColor || '#667eea' }
        ]}
        onPress={handleMainPress}
        accessibilityLabel={mainAction ? "Main Action" : "More Actions"}
      >
        <Ionicons 
          name={mainAction?.icon as any || (isExpanded ? 'close' : 'add')} 
          size={24} 
          color="#FFFFFF" 
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 16,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  navButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
    color: '#667eea',
  },
  fabContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    alignItems: 'center',
  },
  fabActions: {
    marginBottom: 16,
    alignItems: 'center',
  },
  fabAction: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  fab: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
  },
});

export default NavigationHelper;

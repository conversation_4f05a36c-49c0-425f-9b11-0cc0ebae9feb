{"version": 3, "file": "contentModeration.js", "sourceRoot": "", "sources": ["../src/contentModeration.ts"], "names": [], "mappings": ";AAAA,6CAA6C;AAC7C,wEAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExE,8DAAgD;AAChD,sDAAwC;AAExC,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;AAEhB,QAAA,eAAe,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;IAC5E,IAAI,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,4BAA4B,CAAC,CAAC;QACxF,CAAC;QAED,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;QAE5C,MAAM,gBAAgB,GAAG;YACvB,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,EAAc;YACrB,MAAM,EAAE,MAAM;SACf,CAAC;QAEF,8EAA8E;QAC9E,MAAM,kBAAkB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC7D,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE,CAAC;YACtC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChC,gBAAgB,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAClC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,0BAA0B,IAAI,EAAE,CAAC,CAAC;gBAC9D,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;gBACjC,MAAM;YACR,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,MAAM,EAAE,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC;YAC5C,OAAO;YACP,IAAI;YACJ,SAAS;YACT,MAAM;YACN,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG;YACxB,MAAM,EAAE,gBAAgB;YACxB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SACxD,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,2BAA2B,CAAC,CAAC;IAChF,CAAC;AACH,CAAC,CAAC,CAAC;AAEU,QAAA,WAAW,GAAG,SAAS,CAAC,SAAS;KAC3C,QAAQ,CAAC,gDAAgD,CAAC;KAC1D,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE;IACpC,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QACpC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;QAE7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC;QAExD,uBAAuB;QACvB,MAAM,cAAc,GAAG;YACrB,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,cAAc;YACd,SAAS;SACV,CAAC;QAEF,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC;QAClE,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QAE5C,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,MAAM,kBAAkB,GAAG,EAAE,CAAC;QAE9B,KAAK,MAAM,SAAS,IAAI,cAAc,EAAE,CAAC;YACvC,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAClC,SAAS,IAAI,GAAG,CAAC;gBACjB,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,MAAM,SAAS,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QAClF,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YACpB,SAAS,IAAI,GAAG,CAAC;YACjB,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5C,CAAC;QAED,gCAAgC;QAChC,IAAI,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YAClC,SAAS,IAAI,GAAG,CAAC;YACjB,kBAAkB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAAC;YAEtD,eAAe;YACf,MAAM,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;gBACxB,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE,gBAAgB;gBAC5B,SAAS;gBACT,kBAAkB;gBAClB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;aACxD,CAAC,CAAC;YAEH,oBAAoB;YACpB,MAAM,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACvC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;AACH,CAAC,CAAC,CAAC;AAEQ,QAAA,iBAAiB,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;IAC9E,IAAI,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,4BAA4B,CAAC,CAAC;QACxF,CAAC;QAED,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;QAEpD,4EAA4E;QAC5E,qDAAqD;QACrD,MAAM,gBAAgB,GAAG;YACvB,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,eAAe;YACtB,QAAQ,EAAE,eAAe;YACzB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,eAAe;YACxB,OAAO,EAAE,eAAe;YACxB,UAAU,EAAE,IAAI;SACjB,CAAC;QAEF,+BAA+B;QAC/B,MAAM,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC;YAC1C,QAAQ;YACR,SAAS;YACT,MAAM;YACN,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG;YACxB,MAAM,EAAE,gBAAgB;YACxB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SACxD,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,4BAA4B,CAAC,CAAC;IACjF,CAAC;AACH,CAAC,CAAC,CAAC;AAEU,QAAA,qBAAqB,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;IAClF,IAAI,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,4BAA4B,CAAC,CAAC;QACxF,CAAC;QAED,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QACxD,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC;QAExD,gBAAgB;QAChB,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC;YAC3D,SAAS;YACT,MAAM;YACN,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG;YAC5B,MAAM;YACN,WAAW;YACX,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SACxD,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACvG,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,GAAG,EAAE,CAAC;QAE1C,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;YACtB,MAAM,WAAW,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;YAEtC,kCAAkC;YAClC,MAAM,UAAU,CAAC,MAAM,CAAC;gBACtB,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;gBACpD,cAAc,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;aAC7D,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,WAAW,GAAG,CAAC,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,WAAW,KAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACxD,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;gBACrB,MAAM,mBAAmB,CAAC,SAAS,EAAE,MAAM,EAAE,kBAAkB,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,MAAM,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;QACvD,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;IACzD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,iCAAiC,CAAC,CAAC;IACtF,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,6DAA6D;AAE7D,KAAK,UAAU,gBAAgB,CAAC,MAAc,EAAE,SAAiB,EAAE,IAAY,EAAE,QAAgB;IAC/F,IAAI,CAAC;QACH,2EAA2E;QAC3E,MAAM,eAAe,GAAG,MAAM,mBAAmB,EAAE,CAAC;QAEpD,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG;YACnB,KAAK,EAAE,0BAA0B;YACjC,IAAI,EAAE,GAAG,IAAI,qBAAqB,MAAM,EAAE;SAC3C,CAAC;QAEF,MAAM,IAAI,GAAG;YACX,IAAI,EAAE,kBAAkB;YACxB,MAAM;YACN,SAAS;YACT,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE;YAC7B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;SACjC,CAAC;QAEF,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC,aAAa,CAAC;YACpC,MAAM,EAAE,eAAe;YACvB,YAAY;YACZ,IAAI;YACJ,OAAO,EAAE;gBACP,QAAQ,EAAE,MAAM;gBAChB,YAAY,EAAE;oBACZ,SAAS,EAAE,YAAY;oBACvB,QAAQ,EAAE,MAAM;iBACjB;aACF;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE;oBACP,GAAG,EAAE;wBACH,KAAK,EAAE,SAAS;wBAChB,KAAK,EAAE,CAAC;qBACT;iBACF;aACF;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACvC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;IACxD,CAAC;AACH,CAAC;AAED,KAAK,UAAU,mBAAmB;IAChC,IAAI,CAAC;QACH,oDAAoD;QACpD,8BAA8B;QAC9B,OAAO,EAAE,CAAC;IACZ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC,CAAC,qCAAqC;QACrD,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED,KAAK,UAAU,mBAAmB,CAAC,SAAiB,EAAE,MAAc,EAAE,MAAc;IAClF,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAAC;QAEtD,MAAM,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEvG,MAAM,UAAU,CAAC,MAAM,CAAC;YACtB,SAAS,EAAE,IAAI;YACf,gBAAgB,EAAE,MAAM;YACxB,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;YACzD,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;QAEH,wBAAwB;QACxB,MAAM,EAAE,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC;YAC5C,SAAS;YACT,MAAM;YACN,MAAM,EAAE,WAAW;YACnB,MAAM;YACN,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SACxD,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC1C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC"}
"use strict";
// 🔥 FIREBASE CLOUD FUNCTIONS INDEX - REAL DEPLOYMENT READY
// All functions exported for deployment
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthCheck = exports.exchangeIceCandidate = exports.endCall = exports.declineCall = exports.answerCall = exports.initiateCall = exports.checkUsernameAvailability = exports.updateUsername = exports.trackUserActivity = exports.updateUserAnalytics = exports.handleUserDeletion = exports.handleUserSignup = exports.archiveOldChats = exports.cleanupDeletedData = exports.exportChatHistory = exports.backupUserData = exports.handleReportedContent = exports.checkImageContent = exports.scanForSpam = exports.moderateContent = exports.deleteMedia = exports.compressMedia = exports.generateThumbnail = exports.processMediaUpload = exports.handleSettingsUpdate = exports.handleMediaUpdate = exports.handleChatUpdate = exports.setupRealTimeListeners = exports.updateUserPresence = exports.cleanupOldScheduledMessages = exports.updateMessageStatus = exports.processScheduledMessages = void 0;
const admin = __importStar(require("firebase-admin"));
const functions = __importStar(require("firebase-functions"));
// Initialize Firebase Admin SDK
admin.initializeApp();
// Export all Cloud Functions
var scheduledMessages_1 = require("./scheduledMessages");
Object.defineProperty(exports, "processScheduledMessages", { enumerable: true, get: function () { return scheduledMessages_1.processScheduledMessages; } });
Object.defineProperty(exports, "updateMessageStatus", { enumerable: true, get: function () { return scheduledMessages_1.updateMessageStatus; } });
Object.defineProperty(exports, "cleanupOldScheduledMessages", { enumerable: true, get: function () { return scheduledMessages_1.cleanupOldScheduledMessages; } });
Object.defineProperty(exports, "updateUserPresence", { enumerable: true, get: function () { return scheduledMessages_1.updateUserPresence; } });
// Push notification functions removed
var realTimeHandlers_1 = require("./realTimeHandlers");
Object.defineProperty(exports, "setupRealTimeListeners", { enumerable: true, get: function () { return realTimeHandlers_1.setupRealTimeListeners; } });
Object.defineProperty(exports, "handleChatUpdate", { enumerable: true, get: function () { return realTimeHandlers_1.handleChatUpdate; } });
Object.defineProperty(exports, "handleMediaUpdate", { enumerable: true, get: function () { return realTimeHandlers_1.handleMediaUpdate; } });
Object.defineProperty(exports, "handleSettingsUpdate", { enumerable: true, get: function () { return realTimeHandlers_1.handleSettingsUpdate; } });
// Translation service removed per user request
var mediaProcessing_1 = require("./mediaProcessing");
Object.defineProperty(exports, "processMediaUpload", { enumerable: true, get: function () { return mediaProcessing_1.processMediaUpload; } });
Object.defineProperty(exports, "generateThumbnail", { enumerable: true, get: function () { return mediaProcessing_1.generateThumbnail; } });
Object.defineProperty(exports, "compressMedia", { enumerable: true, get: function () { return mediaProcessing_1.compressMedia; } });
Object.defineProperty(exports, "deleteMedia", { enumerable: true, get: function () { return mediaProcessing_1.deleteMedia; } });
var contentModeration_1 = require("./contentModeration");
Object.defineProperty(exports, "moderateContent", { enumerable: true, get: function () { return contentModeration_1.moderateContent; } });
Object.defineProperty(exports, "scanForSpam", { enumerable: true, get: function () { return contentModeration_1.scanForSpam; } });
Object.defineProperty(exports, "checkImageContent", { enumerable: true, get: function () { return contentModeration_1.checkImageContent; } });
Object.defineProperty(exports, "handleReportedContent", { enumerable: true, get: function () { return contentModeration_1.handleReportedContent; } });
var dataManagement_1 = require("./dataManagement");
Object.defineProperty(exports, "backupUserData", { enumerable: true, get: function () { return dataManagement_1.backupUserData; } });
Object.defineProperty(exports, "exportChatHistory", { enumerable: true, get: function () { return dataManagement_1.exportChatHistory; } });
Object.defineProperty(exports, "cleanupDeletedData", { enumerable: true, get: function () { return dataManagement_1.cleanupDeletedData; } });
Object.defineProperty(exports, "archiveOldChats", { enumerable: true, get: function () { return dataManagement_1.archiveOldChats; } });
var userManagement_1 = require("./userManagement");
Object.defineProperty(exports, "handleUserSignup", { enumerable: true, get: function () { return userManagement_1.handleUserSignup; } });
Object.defineProperty(exports, "handleUserDeletion", { enumerable: true, get: function () { return userManagement_1.handleUserDeletion; } });
Object.defineProperty(exports, "updateUserAnalytics", { enumerable: true, get: function () { return userManagement_1.updateUserAnalytics; } });
Object.defineProperty(exports, "trackUserActivity", { enumerable: true, get: function () { return userManagement_1.trackUserActivity; } });
Object.defineProperty(exports, "updateUsername", { enumerable: true, get: function () { return userManagement_1.updateUsername; } });
Object.defineProperty(exports, "checkUsernameAvailability", { enumerable: true, get: function () { return userManagement_1.checkUsernameAvailability; } });
// 🔥 Real Call Management Functions
var callManagement_1 = require("./callManagement");
Object.defineProperty(exports, "initiateCall", { enumerable: true, get: function () { return callManagement_1.initiateCall; } });
Object.defineProperty(exports, "answerCall", { enumerable: true, get: function () { return callManagement_1.answerCall; } });
Object.defineProperty(exports, "declineCall", { enumerable: true, get: function () { return callManagement_1.declineCall; } });
Object.defineProperty(exports, "endCall", { enumerable: true, get: function () { return callManagement_1.endCall; } });
Object.defineProperty(exports, "exchangeIceCandidate", { enumerable: true, get: function () { return callManagement_1.exchangeIceCandidate; } });
// Health check function
exports.healthCheck = functions.https.onRequest((req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        functions: [
            'processScheduledMessages',
            'sendPushNotification',
            'setupRealTimeListeners',
            'translateMessage',
            'processMediaUpload',
            'moderateContent',
            'backupUserData',
            'handleUserSignup'
        ]
    });
});
console.log('🔥 IraChat Cloud Functions initialized successfully');
//# sourceMappingURL=index.js.map
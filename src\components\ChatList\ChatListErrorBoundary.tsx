import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ChatListErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface ChatListErrorBoundaryProps {
  children: React.ReactNode;
}

export class ChatListErrorBoundary extends React.Component<
  ChatListErrorBoundaryProps,
  ChatListErrorBoundaryState
> {
  constructor(props: ChatListErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ChatListErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('❌ ChatListScreen Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={{ 
          flex: 1, 
          justifyContent: 'center', 
          alignItems: 'center', 
          padding: 20 
        }}>
          <Ionicons name="warning-outline" size={60} color="#EF4444" />
          <Text style={{ 
            fontSize: 18, 
            fontWeight: '600', 
            color: '#1f2937', 
            marginTop: 16, 
            textAlign: 'center' 
          }}>
            Something went wrong
          </Text>
          <Text style={{ 
            fontSize: 14, 
            color: '#6b7280', 
            marginTop: 8, 
            textAlign: 'center' 
          }}>
            {this.state.error?.message || 'Please restart the app'}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: '#87CEEB',
              paddingHorizontal: 24,
              paddingVertical: 12,
              borderRadius: 25,
              marginTop: 24,
            }}
            onPress={() => this.setState({ hasError: false, error: null })}
          >
            <Text style={{ 
              color: 'white', 
              fontSize: 16, 
              fontWeight: '600' 
            }}>
              Try Again
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    return this.props.children;
  }
}

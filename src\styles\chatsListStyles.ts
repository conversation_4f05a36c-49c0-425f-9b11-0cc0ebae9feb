import { IRACHAT_COLORS } from './iraChatDesignSystem';

export const chatsListStyles = {
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  header: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: IRACHAT_COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.borderLight,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    color: IRACHAT_COLORS.text,
  },
  headerButton: {
    padding: 8,
  },
  searchContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: IRACHAT_COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.borderLight,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: IRACHAT_COLORS.text,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: IRACHAT_COLORS.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    color: IRACHAT_COLORS.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: IRACHAT_COLORS.textSecondary,
    textAlign: 'center' as const,
    lineHeight: 24,
  },
  chatItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: IRACHAT_COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.borderLight,
  },
  chatAvatar: {
    marginRight: 16,
  },
  avatarImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: IRACHAT_COLORS.primary,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  avatarText: {
    fontSize: 20,
    fontWeight: 'bold' as const,
    color: IRACHAT_COLORS.textOnPrimary,
  },
  chatInfo: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: IRACHAT_COLORS.text,
    flex: 1,
  },
  chatTime: {
    fontSize: 12,
    color: IRACHAT_COLORS.textMuted,
  },
  chatFooter: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  lastMessage: {
    fontSize: 14,
    color: IRACHAT_COLORS.textSecondary,
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: IRACHAT_COLORS.primary,
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    paddingHorizontal: 8,
  },
  unreadText: {
    fontSize: 12,
    fontWeight: 'bold' as const,
    color: IRACHAT_COLORS.textOnPrimary,
  },
};

import { useState, useEffect, useCallback } from 'react';
import { UnifiedChatItem } from '../components/ModernChatItem';

export interface ChatSearchState {
  searchQuery: string;
  filteredChats: UnifiedChatItem[];
}

export interface ChatSearchActions {
  handleSearch: (_text: string) => void;
  clearSearch: () => void;
}

export const useChatSearch = (chats: UnifiedChatItem[]): ChatSearchState & ChatSearchActions => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredChats, setFilteredChats] = useState<UnifiedChatItem[]>([]);

  // Handle search input
  const handleSearch = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchQuery("");
  }, []);

  // Filter and sort chats based on search query
  useEffect(() => {
    let filtered = [...chats];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(chat =>
        chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chat.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort by time (most recent first)
    filtered.sort((a, b) => new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime());

    setFilteredChats(filtered);
  }, [chats, searchQuery]);

  return {
    searchQuery,
    filteredChats,
    handleSearch,
    clearSearch,
  };
};

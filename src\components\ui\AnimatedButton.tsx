/**
 * Beautiful Animated Button Component for IraChat
 * Sky blue branding with smooth animations
 */

import React, { useRef } from 'react';
import {
  Animated,
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  GestureResponderEvent,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS, ANIMATIONS } from '../../styles/iraChatDesignSystem';
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing } from '../../utils/responsiveUtils';

interface AnimatedButtonProps {
  title: string;
  onPress: (_event: GestureResponderEvent) => void;
  variant?: 'primary' | 'secondary' | 'ghost' | 'gradient';
  size?: 'small' | 'medium' | 'large';
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
  animated?: boolean;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  icon,
  iconPosition = 'left',
  disabled = false,
  loading = false,
  style,
  textStyle,
  fullWidth = false,
  animated = true,
}) => {
  const scaleAnimation = useRef(new Animated.Value(1)).current;
  const opacityAnimation = useRef(new Animated.Value(1)).current;
  const rotateAnimation = useRef(new Animated.Value(0)).current;

  const handlePressIn = () => {
    if (animated && !disabled) {
      Animated.parallel([
        Animated.spring(scaleAnimation, {
          toValue: 0.95,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnimation, {
          toValue: 0.8,
          duration: ANIMATIONS.fast,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const handlePressOut = () => {
    if (animated && !disabled) {
      Animated.parallel([
        Animated.spring(scaleAnimation, {
          toValue: 1,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnimation, {
          toValue: 1,
          duration: ANIMATIONS.fast,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const handlePress = (event: GestureResponderEvent) => {
    if (!disabled && !loading) {
      onPress(event);
    }
  };

  // Loading animation
  React.useEffect(() => {
    if (loading) {
      Animated.loop(
        Animated.timing(rotateAnimation, {
          toValue: 1,
          duration: ANIMATIONS.slow * 2,
          useNativeDriver: true,
        })
      ).start();
    } else {
      rotateAnimation.setValue(0);
    }
  }, [loading, rotateAnimation]);

  const rotateInterpolate = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: ResponsiveScale.borderRadius(BORDER_RADIUS.lg),
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: iconPosition === 'left' ? 'row' : 'row-reverse',
      minHeight: ResponsiveSpacing.minTouchTarget,
    };

    // Responsive size variations
    switch (size) {
      case 'small':
        baseStyle.paddingVertical = ResponsiveSpacing.sm;
        baseStyle.paddingHorizontal = ResponsiveSpacing.md;
        baseStyle.minHeight = ComponentSizes.buttonHeight.small;
        break;
      case 'large':
        baseStyle.paddingVertical = ResponsiveSpacing.lg;
        baseStyle.paddingHorizontal = ResponsiveSpacing.xl;
        baseStyle.minHeight = ComponentSizes.buttonHeight.large;
        break;
      default:
        baseStyle.paddingVertical = ResponsiveSpacing.md;
        baseStyle.paddingHorizontal = ResponsiveSpacing.lg;
        baseStyle.minHeight = ComponentSizes.buttonHeight.medium;
    }

    // Variant styles
    switch (variant) {
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: IRACHAT_COLORS.surface,
          borderWidth: 1,
          borderColor: IRACHAT_COLORS.primary,
          ...SHADOWS.sm,
        };
      case 'ghost':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
        };
      case 'gradient':
        return baseStyle;
      default:
        return {
          ...baseStyle,
          backgroundColor: IRACHAT_COLORS.primary,
          ...SHADOWS.md,
        };
    }
  };

  const getTextStyle = (): TextStyle => {
    const baseTextStyle: TextStyle = {
      fontFamily: TYPOGRAPHY.fontFamily,
      fontWeight: '500' as const,
      textAlign: 'center',
    };

    // Responsive size variations
    switch (size) {
      case 'small':
        baseTextStyle.fontSize = ResponsiveTypography.fontSize.sm;
        break;
      case 'large':
        baseTextStyle.fontSize = ResponsiveTypography.fontSize.lg;
        break;
      default:
        baseTextStyle.fontSize = ResponsiveTypography.fontSize.base;
    }

    // Variant colors
    switch (variant) {
      case 'secondary':
        baseTextStyle.color = IRACHAT_COLORS.primary;
        break;
      case 'ghost':
        baseTextStyle.color = IRACHAT_COLORS.primary;
        break;
      default:
        baseTextStyle.color = IRACHAT_COLORS.textOnPrimary;
    }

    return baseTextStyle;
  };

  const renderIcon = () => {
    if (!icon) return null;

    const iconSize = ResponsiveScale.iconSize(size === 'small' ? 16 : size === 'large' ? 24 : 20);
    const iconColor = variant === 'secondary' || variant === 'ghost'
      ? IRACHAT_COLORS.primary
      : IRACHAT_COLORS.textOnPrimary;

    if (loading) {
      return (
        <Animated.View
          style={[
            styles.iconContainer,
            iconPosition === 'right' && styles.iconRight,
            { transform: [{ rotate: rotateInterpolate }] },
          ]}
        >
          <Ionicons name="refresh" size={iconSize} color={iconColor} />
        </Animated.View>
      );
    }

    return (
      <Ionicons
        name={icon}
        size={iconSize}
        color={iconColor}
        style={[
          styles.iconContainer,
          iconPosition === 'right' && styles.iconRight,
        ]}
      />
    );
  };

  const buttonContent = (
    <>
      {renderIcon()}
      <Text style={[getTextStyle(), textStyle, disabled && styles.disabledText]}>
        {loading ? 'Loading...' : title}
      </Text>
    </>
  );

  const animatedStyle = {
    transform: [{ scale: scaleAnimation }],
    opacity: opacityAnimation,
  };

  const buttonStyle = [
    getButtonStyle(),
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    style,
  ];

  if (variant === 'gradient') {
    return (
      <TouchableOpacity
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
        disabled={disabled || loading}
        activeOpacity={0.8}
      >
        <Animated.View style={[animatedStyle, fullWidth && styles.fullWidth]}>
          <LinearGradient
            colors={IRACHAT_COLORS.primaryGradient as any}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[buttonStyle, SHADOWS.md]}
          >
            {buttonContent}
          </LinearGradient>
        </Animated.View>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={handlePress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      <Animated.View style={[buttonStyle, animatedStyle]}>
        {buttonContent}
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    marginRight: ResponsiveSpacing.sm,
    paddingHorizontal: SPACING.xs,
  },
  iconRight: {
    marginRight: 0,
    marginLeft: ResponsiveSpacing.sm,
    paddingHorizontal: SPACING.xs,
  },
  fullWidth: {
    width: '100%',
    marginVertical: SPACING.sm,
  },
  disabled: {
    opacity: 0.5,
  },
  disabledText: {
    color: IRACHAT_COLORS.textMuted,
  },
});

export default AnimatedButton;

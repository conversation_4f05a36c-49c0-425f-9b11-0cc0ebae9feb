
const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// ULTRA-STRICT MOBILE-ONLY CONFIGURATION WITH WEBRTC SUPPORT
config.resolver.platforms = ['ios', 'android', 'native'];

// WebRTC specific configuration
config.resolver.alias = {
  ...config.resolver.alias,
  'react-native-webrtc': 'react-native-webrtc',
};

// Ensure WebRTC native modules are properly resolved
config.resolver.sourceExts = [...config.resolver.sourceExts, 'jsx', 'ts', 'tsx'];
config.resolver.assetExts = [...config.resolver.assetExts, 'mp3', 'wav', 'aac', 'm4a'];

// Aggressive bundle optimization
config.transformer.minifierConfig = {
  keep_fnames: false,
  mangle: {
    keep_fnames: false,
    toplevel: true,
  },
  compress: {
    drop_console: true, // Remove console.logs in production
    drop_debugger: true,
    pure_funcs: ['console.log', 'console.info', 'console.debug'],
  },
};

// Strict asset handling - only bundle what's explicitly listed
config.transformer.assetPlugins = ['expo-asset/tools/hashAssetFiles'];

// Exclude development files from bundling
config.resolver.blacklistRE = /.*\.(md|backup|log|tmp)$/;

// Tree shaking optimization
config.transformer.enableBabelRCLookup = false;

module.exports = config;

import { collection, onSnapshot, query, where } from 'firebase/firestore';
import { auth, db } from './firebaseSimple';

export interface Call {
  id: string;
  type: 'voice' | 'video';
  participants: string[];
  status: 'incoming' | 'outgoing' | 'missed' | 'ended';
  timestamp: Date;
  duration?: number;
  callerName?: string;
  callerAvatar?: string;
}

class CallsService {
  private unsubscribe: (() => void) | null = null;

  /**
   * Start listening for calls with proper error handling
   */
  startListening(_onCallsUpdate: (_calls: Call[]) => void, _onError?: (_error: string) => void) {
    try {
      console.log('🔄 Starting calls listener...');

      // Check if auth is available
      if (!auth || !auth.currentUser) {
        console.warn('⚠️ Auth not available, cannot listen for calls');
        if (_onError) {
          _onError('Authentication required for calls');
        }
        return;
      }

      // Check if Firestore is available
      if (!db) {
        console.warn('⚠️ Firestore not available, cannot listen for calls');
        if (_onError) {
          _onError('Database not available');
        }
        return;
      }

      const currentUserId = auth.currentUser.uid;
      
      // Create query to listen for calls involving current user
      const callsQuery = query(
        collection(db, 'calls'),
        where('participants', 'array-contains', currentUserId)
      );

      this.unsubscribe = onSnapshot(
        callsQuery,
        (snapshot) => {
          try {
            const calls: Call[] = [];
            snapshot.forEach((doc) => {
              const data = doc.data();
              calls.push({
                id: doc.id,
                type: data.type || 'voice',
                participants: data.participants || [],
                status: data.status || 'ended',
                timestamp: data.timestamp?.toDate() || new Date(),
                duration: data.duration,
                callerName: data.callerName,
                callerAvatar: data.callerAvatar,
              });
            });

            console.log(`📞 Loaded ${calls.length} calls`);
            // onCallsUpdate(calls);
          } catch (error) {
            console.error('❌ Error processing calls data:', error);
            if (_onError) {
              _onError('Error processing calls data');
            }
          }
        },
        (error) => {
          console.error('❌ Error listening for calls:', error);
          
          // Handle specific Firebase errors
          if (error.code === 'permission-denied') {
            console.error('❌ Permission denied - check Firestore rules');
            if (_onError) {
              _onError('Permission denied - please check your account permissions');
            }
          } else if (error.code === 'unavailable') {
            console.error('❌ Firestore unavailable - network issue');
            if (_onError) {
              _onError('Network unavailable - please check your connection');
            }
          } else {
            console.error('❌ Unknown error:', error.message);
            if (_onError) {
              _onError(`Error: ${error.message}`);
            }
          }
        }
      );

      console.log('✅ Calls listener started successfully');
    } catch (error) {
      console.error('❌ Failed to start calls listener:', error);
      if (_onError) {
        _onError('Failed to start calls listener');
      }
    }
  }

  /**
   * Stop listening for calls
   */
  stopListening() {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
      console.log('🛑 Calls listener stopped');
    }
  }


}

export const callsService = new CallsService();

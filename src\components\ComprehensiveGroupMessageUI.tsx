// 💬 COMPREHENSIVE GROUP MESSAGE UI SYSTEM
// Complete message bubbles, reactions, threading, status indicators, and all message UI components
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Alert,
  SafeAreaView,
  Image,
  TextInput,
  Modal,
  ScrollView,
  Animated,
  Pressable,
} from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Video, ResizeMode } from 'expo-av';
import * as Haptics from 'expo-haptics';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  inputBackground: 'rgba(255, 255, 255, 0.1)',
  myMessageBubble: '#87CEEB',
  otherMessageBubble: '#2A2A2A',
  reactionBackground: 'rgba(135, 206, 235, 0.2)',
};

// Enhanced Message Interface
interface GroupMessage {
  id: string;
  text?: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice' | 'call' | 'location' | 'contact' | 'poll' | 'announcement';
  mediaUrl?: string;
  mediaThumbnail?: string;
  duration?: number;
  fileName?: string;
  fileSize?: number;
  
  // Advanced Features
  mentions?: string[]; // User IDs mentioned
  isAnnouncement?: boolean;
  announcementPriority?: 'low' | 'medium' | 'high';
  
  // Reply System
  replyTo?: {
    messageId: string;
    text: string;
    senderName: string;
    type: string;
    mediaUrl?: string;
  };
  
  // Reactions System
  reactions?: {
    [emoji: string]: {
      users: string[];
      count: number;
    };
  };
  
  // Threading
  threadReplies?: GroupMessage[];
  threadCount?: number;
  
  // Editing & Deletion
  isEdited?: boolean;
  editedAt?: Date;
  isDeleted?: boolean;
  deletedAt?: Date;
  
  // Forwarding
  isForwarded?: boolean;
  forwardedFrom?: string;
  
  // Pinning
  isPinned?: boolean;
  pinnedBy?: string;
  pinnedAt?: Date;
}

interface MessageBubbleProps {
  message: GroupMessage;
  isOwn: boolean;
  showAvatar: boolean;
  showTimestamp: boolean;
  currentUserId: string;
  onReply: (message: GroupMessage) => void;
  onReact: (messageId: string, emoji: string) => void;
  onEdit: (messageId: string) => void;
  onDelete: (messageId: string) => void;
  onForward: (messageId: string) => void;
  onPin: (messageId: string) => void;
  onUserPress: (userId: string) => void;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwn,
  showAvatar,
  showTimestamp,
  currentUserId,
  onReply,
  onReact,
  onEdit,
  onDelete,
  onForward,
  onPin,
  onUserPress,
}) => {
  const [showReactions, setShowReactions] = useState(false);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const reactionAnim = useRef(new Animated.Value(0)).current;

  const handleLongPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setShowContextMenu(true);
    
    Animated.sequence([
      Animated.timing(scaleAnim, { toValue: 0.95, duration: 100, useNativeDriver: true }),
      Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();
  };

  const handleReaction = (emoji: string) => {
    onReact(message.id, emoji);
    setShowReactions(false);
    
    // Animate reaction
    Animated.sequence([
      Animated.timing(reactionAnim, { toValue: 1, duration: 300, useNativeDriver: true }),
      Animated.delay(1500),
      Animated.timing(reactionAnim, { toValue: 0, duration: 300, useNativeDriver: true }),
    ]).start();
  };

  const renderMessageContent = () => {
    switch (message.type) {
      case 'text':
        return (
          <Text style={[styles.messageText, isOwn && styles.ownMessageText]}>
            {message.text}
          </Text>
        );
      
      case 'image':
        return (
          <View style={styles.mediaContainer}>
            <Image source={{ uri: message.mediaUrl }} style={styles.messageImage} />
            {message.text && (
              <Text style={[styles.messageText, styles.mediaCaption, isOwn && styles.ownMessageText]}>
                {message.text}
              </Text>
            )}
          </View>
        );
      
      case 'video':
        return (
          <View style={styles.mediaContainer}>
            <Video
              source={{ uri: message.mediaUrl! }}
              style={styles.messageVideo}
              resizeMode={ResizeMode.COVER}
              shouldPlay={false}
              useNativeControls
            />
            <View style={styles.videoOverlay}>
              <Ionicons name="play-circle" size={40} color="rgba(255,255,255,0.8)" />
              {message.duration && (
                <Text style={styles.videoDuration}>
                  {Math.floor(message.duration / 60)}:{(message.duration % 60).toString().padStart(2, '0')}
                </Text>
              )}
            </View>
            {message.text && (
              <Text style={[styles.messageText, styles.mediaCaption, isOwn && styles.ownMessageText]}>
                {message.text}
              </Text>
            )}
          </View>
        );
      
      case 'voice':
        return (
          <View style={styles.voiceContainer}>
            <TouchableOpacity style={styles.voicePlayButton}>
              <Ionicons name="play" size={20} color={isOwn ? COLORS.background : COLORS.primary} />
            </TouchableOpacity>
            <View style={styles.voiceWaveform}>
              {/* Voice waveform visualization */}
              {Array.from({ length: 20 }).map((_, i) => (
                <View
                  key={i}
                  style={[
                    styles.waveformBar,
                    { height: Math.random() * 20 + 5 },
                    { backgroundColor: isOwn ? COLORS.background : COLORS.primary }
                  ]}
                />
              ))}
            </View>
            <Text style={[styles.voiceDuration, isOwn && styles.ownMessageText]}>
              {message.duration ? `${Math.floor(message.duration / 60)}:${(message.duration % 60).toString().padStart(2, '0')}` : '0:00'}
            </Text>
          </View>
        );
      
      case 'document':
        return (
          <View style={styles.documentContainer}>
            <View style={styles.documentIcon}>
              <Ionicons name="document" size={24} color={isOwn ? COLORS.background : COLORS.primary} />
            </View>
            <View style={styles.documentInfo}>
              <Text style={[styles.documentName, isOwn && styles.ownMessageText]} numberOfLines={1}>
                {message.fileName || 'Document'}
              </Text>
              <Text style={[styles.documentSize, isOwn && styles.ownMessageText]}>
                {message.fileSize ? `${(message.fileSize / 1024 / 1024).toFixed(1)} MB` : 'Unknown size'}
              </Text>
            </View>
            <TouchableOpacity style={styles.downloadButton}>
              <Ionicons name="download" size={20} color={isOwn ? COLORS.background : COLORS.primary} />
            </TouchableOpacity>
          </View>
        );
      
      case 'announcement':
        return (
          <View style={styles.announcementContainer}>
            <View style={styles.announcementHeader}>
              <Ionicons name="megaphone" size={16} color={COLORS.warning} />
              <Text style={styles.announcementLabel}>Announcement</Text>
              {message.announcementPriority && (
                <View style={[styles.priorityBadge, { backgroundColor: 
                  message.announcementPriority === 'high' ? COLORS.error :
                  message.announcementPriority === 'medium' ? COLORS.warning : COLORS.success
                }]}>
                  <Text style={styles.priorityText}>{message.announcementPriority.toUpperCase()}</Text>
                </View>
              )}
            </View>
            <Text style={[styles.messageText, styles.announcementText]}>
              {message.text}
            </Text>
          </View>
        );
      
      default:
        return (
          <Text style={[styles.messageText, isOwn && styles.ownMessageText]}>
            {message.text || 'Unsupported message type'}
          </Text>
        );
    }
  };

  const renderReplyPreview = () => {
    if (!message.replyTo) return null;

    return (
      <View style={styles.replyPreview}>
        <View style={[styles.replyLine, { backgroundColor: isOwn ? COLORS.background : COLORS.primary }]} />
        <View style={styles.replyContent}>
          <Text style={[styles.replySender, isOwn && styles.ownMessageText]}>
            {message.replyTo.senderName}
          </Text>
          <Text style={[styles.replyText, isOwn && styles.ownMessageText]} numberOfLines={1}>
            {message.replyTo.type === 'text' ? message.replyTo.text : `${message.replyTo.type.charAt(0).toUpperCase() + message.replyTo.type.slice(1)}`}
          </Text>
        </View>
      </View>
    );
  };

  const renderReactions = () => {
    if (!message.reactions || Object.keys(message.reactions).length === 0) return null;

    return (
      <View style={styles.reactionsContainer}>
        {Object.entries(message.reactions).map(([emoji, data]) => (
          <TouchableOpacity
            key={emoji}
            style={[
              styles.reactionBubble,
              data.users.includes(currentUserId) && styles.ownReaction
            ]}
            onPress={() => handleReaction(emoji)}
          >
            <Text style={styles.reactionEmoji}>{emoji}</Text>
            <Text style={styles.reactionCount}>{data.count}</Text>
          </TouchableOpacity>
        ))}
        <TouchableOpacity
          style={styles.addReactionButton}
          onPress={() => setShowReactions(true)}
        >
          <Ionicons name="add" size={12} color={COLORS.textMuted} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderMessageStatus = () => {
    if (!isOwn) return null;

    const getStatusIcon = () => {
      switch (message.status) {
        case 'sending': return 'time-outline';
        case 'sent': return 'checkmark';
        case 'delivered': return 'checkmark-done';
        case 'read': return 'checkmark-done';
        default: return 'time-outline';
      }
    };

    const getStatusColor = () => {
      switch (message.status) {
        case 'sending': return COLORS.textMuted;
        case 'sent': return COLORS.textSecondary;
        case 'delivered': return COLORS.textSecondary;
        case 'read': return COLORS.primary;
        default: return COLORS.textMuted;
      }
    };

    return (
      <Ionicons 
        name={getStatusIcon() as any} 
        size={12} 
        color={getStatusColor()} 
        style={styles.statusIcon}
      />
    );
  };

  return (
    <Animated.View style={[styles.messageContainer, { transform: [{ scale: scaleAnim }] }]}>
      <View style={[styles.messageRow, isOwn && styles.ownMessageRow]}>
        {/* Avatar */}
        {showAvatar && !isOwn && (
          <TouchableOpacity onPress={() => onUserPress(message.senderId)}>
            <Image 
              source={{ uri: message.senderAvatar || 'https://via.placeholder.com/32' }} 
              style={styles.messageAvatar} 
            />
          </TouchableOpacity>
        )}

        {/* Message Bubble */}
        <Pressable
          style={[
            styles.messageBubble,
            isOwn ? styles.ownMessageBubble : styles.otherMessageBubble,
            message.isAnnouncement && styles.announcementBubble,
          ]}
          onLongPress={handleLongPress}
          delayLongPress={500}
        >
          {/* Forwarded Indicator */}
          {message.isForwarded && (
            <View style={styles.forwardedIndicator}>
              <Ionicons name="arrow-forward" size={12} color={COLORS.textMuted} />
              <Text style={styles.forwardedText}>Forwarded</Text>
            </View>
          )}

          {/* Sender Name (for group messages) */}
          {!isOwn && showAvatar && (
            <TouchableOpacity onPress={() => onUserPress(message.senderId)}>
              <Text style={styles.senderName}>{message.senderName}</Text>
            </TouchableOpacity>
          )}

          {/* Reply Preview */}
          {renderReplyPreview()}

          {/* Message Content */}
          {renderMessageContent()}

          {/* Message Footer */}
          <View style={styles.messageFooter}>
            {message.isEdited && (
              <Text style={[styles.editedText, isOwn && styles.ownMessageText]}>edited</Text>
            )}
            {showTimestamp && (
              <Text style={[styles.timestamp, isOwn && styles.ownMessageText]}>
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </Text>
            )}
            {renderMessageStatus()}
          </View>
        </Pressable>

        {/* Spacer for alignment */}
        {!isOwn && <View style={styles.messageSpacer} />}
      </View>

      {/* Reactions */}
      {renderReactions()}

      {/* Thread Indicator */}
      {message.threadCount && message.threadCount > 0 && (
        <TouchableOpacity style={[styles.threadIndicator, isOwn && styles.ownThreadIndicator]}>
          <Ionicons name="chatbubbles" size={14} color={COLORS.primary} />
          <Text style={styles.threadCount}>{message.threadCount} replies</Text>
        </TouchableOpacity>
      )}

      {/* Reaction Animation */}
      <Animated.View 
        style={[
          styles.reactionAnimation,
          {
            opacity: reactionAnim,
            transform: [{
              scale: reactionAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.5, 1.5],
              }),
            }],
          },
        ]}
      >
        <Text style={styles.reactionAnimationEmoji}>❤️</Text>
      </Animated.View>

      {/* Quick Reactions Modal */}
      <Modal visible={showReactions} transparent animationType="fade">
        <TouchableOpacity 
          style={styles.reactionModalBackdrop} 
          onPress={() => setShowReactions(false)}
        >
          <View style={styles.reactionModal}>
            {['❤️', '😂', '😮', '😢', '😡', '👍', '👎', '🔥'].map((emoji) => (
              <TouchableOpacity
                key={emoji}
                style={styles.reactionOption}
                onPress={() => handleReaction(emoji)}
              >
                <Text style={styles.reactionOptionEmoji}>{emoji}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Context Menu Modal */}
      <Modal visible={showContextMenu} transparent animationType="fade">
        <TouchableOpacity 
          style={styles.contextMenuBackdrop} 
          onPress={() => setShowContextMenu(false)}
        >
          <View style={styles.contextMenu}>
            <TouchableOpacity style={styles.contextMenuItem} onPress={() => { onReply(message); setShowContextMenu(false); }}>
              <Ionicons name="arrow-undo" size={20} color={COLORS.text} />
              <Text style={styles.contextMenuText}>Reply</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.contextMenuItem} onPress={() => { onForward(message.id); setShowContextMenu(false); }}>
              <Ionicons name="arrow-forward" size={20} color={COLORS.text} />
              <Text style={styles.contextMenuText}>Forward</Text>
            </TouchableOpacity>
            
            {isOwn && (
              <TouchableOpacity style={styles.contextMenuItem} onPress={() => { onEdit(message.id); setShowContextMenu(false); }}>
                <Ionicons name="create" size={20} color={COLORS.text} />
                <Text style={styles.contextMenuText}>Edit</Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity style={styles.contextMenuItem} onPress={() => { onPin(message.id); setShowContextMenu(false); }}>
              <Ionicons name="pin" size={20} color={COLORS.text} />
              <Text style={styles.contextMenuText}>{message.isPinned ? 'Unpin' : 'Pin'}</Text>
            </TouchableOpacity>
            
            {(isOwn || message.senderId === currentUserId) && (
              <TouchableOpacity style={[styles.contextMenuItem, styles.deleteMenuItem]} onPress={() => { onDelete(message.id); setShowContextMenu(false); }}>
                <Ionicons name="trash" size={20} color={COLORS.error} />
                <Text style={[styles.contextMenuText, styles.deleteMenuText]}>Delete</Text>
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    marginVertical: 2,
    paddingHorizontal: 16,
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    maxWidth: '85%',
  },
  ownMessageRow: {
    alignSelf: 'flex-end',
    flexDirection: 'row-reverse',
  },
  messageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1.5,
    borderColor: COLORS.primary,
  },
  messageBubble: {
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 10,
    maxWidth: '100%',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  ownMessageBubble: {
    backgroundColor: COLORS.myMessageBubble,
    borderBottomRightRadius: 4,
  },
  otherMessageBubble: {
    backgroundColor: COLORS.otherMessageBubble,
    borderBottomLeftRadius: 4,
  },
  announcementBubble: {
    borderWidth: 2,
    borderColor: COLORS.warning,
    backgroundColor: COLORS.surface,
  },
  messageSpacer: {
    flex: 1,
  },
  senderName: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: 4,
  },
  messageText: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 20,
  },
  ownMessageText: {
    color: COLORS.background,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 4,
  },
  timestamp: {
    fontSize: 11,
    color: COLORS.textMuted,
  },
  editedText: {
    fontSize: 11,
    color: COLORS.textMuted,
    fontStyle: 'italic',
  },
  statusIcon: {
    marginLeft: 2,
  },

  // Media Styles
  mediaContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  messageImage: {
    width: 200,
    height: 200,
    borderRadius: 12,
  },
  messageVideo: {
    width: 200,
    height: 200,
    borderRadius: 12,
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  videoDuration: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.7)',
    color: COLORS.text,
    fontSize: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  mediaCaption: {
    marginTop: 8,
  },

  // Voice Message Styles
  voiceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    minWidth: 150,
  },
  voicePlayButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceWaveform: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
    flex: 1,
  },
  waveformBar: {
    width: 3,
    backgroundColor: COLORS.primary,
    borderRadius: 1.5,
  },
  voiceDuration: {
    fontSize: 12,
    color: COLORS.textMuted,
  },

  // Document Styles
  documentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    minWidth: 200,
  },
  documentIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2,
  },
  documentSize: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  downloadButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Announcement Styles
  announcementContainer: {
    gap: 8,
  },
  announcementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  announcementLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.warning,
    textTransform: 'uppercase',
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '700',
    color: COLORS.background,
  },
  announcementText: {
    fontSize: 16,
    fontWeight: '500',
  },

  // Reply Styles
  replyPreview: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingLeft: 8,
  },
  replyLine: {
    width: 3,
    borderRadius: 1.5,
    marginRight: 8,
  },
  replyContent: {
    flex: 1,
  },
  replySender: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: 2,
  },
  replyText: {
    fontSize: 13,
    color: COLORS.textMuted,
  },

  // Reactions Styles
  reactionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
    marginLeft: 40,
    gap: 4,
  },
  reactionBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.reactionBackground,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  ownReaction: {
    backgroundColor: COLORS.primary,
  },
  reactionEmoji: {
    fontSize: 14,
  },
  reactionCount: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.text,
  },
  addReactionButton: {
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Thread Styles
  threadIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginLeft: 40,
    gap: 4,
  },
  ownThreadIndicator: {
    alignSelf: 'flex-end',
    marginRight: 40,
    marginLeft: 0,
  },
  threadCount: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '500',
  },

  // Forwarded Styles
  forwardedIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 4,
  },
  forwardedText: {
    fontSize: 11,
    color: COLORS.textMuted,
    fontStyle: 'italic',
  },

  // Animation Styles
  reactionAnimation: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -15,
    marginTop: -15,
  },
  reactionAnimationEmoji: {
    fontSize: 30,
  },

  // Modal Styles
  reactionModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  reactionModal: {
    flexDirection: 'row',
    backgroundColor: COLORS.surface,
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  reactionOption: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  reactionOptionEmoji: {
    fontSize: 24,
  },

  // Context Menu Styles
  contextMenuBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contextMenu: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    paddingVertical: 8,
    minWidth: 200,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  contextMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  contextMenuText: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
  },
  deleteMenuItem: {
    borderTopWidth: 1,
    borderTopColor: COLORS.surfaceLight,
  },
  deleteMenuText: {
    color: COLORS.error,
  },
});

// 🔥 PRODUCTION Firebase Configuration for irachat-production
// Secure Firebase initialization with environment variables

import { FirebaseApp, getApp, getApps, initializeApp } from "firebase/app";
import { Auth, initializeAuth } from "firebase/auth";
import { getFirestore, Firestore } from "firebase/firestore";
import { getStorage, FirebaseStorage } from "firebase/storage";
import { getFunctions, Functions } from "firebase/functions";
import { Platform } from "react-native";

// Import configuration from config file
import { firebaseConfig } from "../config/firebase";

// Validate Firebase configuration
const validateConfig = () => {
  const required = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
  const missing = required.filter(key => !firebaseConfig[key as keyof typeof firebaseConfig]);

  if (missing.length > 0) {
    console.error("❌ Missing Firebase config:", missing);
    throw new Error(`Missing Firebase configuration: ${missing.join(', ')}`);
  }

  return true;
};

// Initialize Firebase App
let firebaseApp: FirebaseApp;
let auth: Auth;
let db: Firestore;
let storage: FirebaseStorage;
let functions: Functions;

try {
  // Validate configuration first
  validateConfig();

  // Initialize Firebase App
  if (getApps().length === 0) {
    firebaseApp = initializeApp(firebaseConfig);
  } else {
    firebaseApp = getApp();
  }

  // Initialize Firebase Auth with AsyncStorage persistence
  console.log("🔧 Initializing Firebase Auth with AsyncStorage...");
  auth = initializeAuth(firebaseApp, {
    // persistence: getReactNativePersistence(AsyncStorage) // TODO: Fix persistence
  });
  console.log("✅ Firebase Auth initialized with AsyncStorage persistence");

  // Initialize Firestore
  db = getFirestore(firebaseApp);

  // Initialize Storage
  storage = getStorage(firebaseApp);

  // Initialize Functions
  functions = getFunctions(firebaseApp);

} catch (error: any) {
  console.error("❌ CRITICAL: Firebase initialization failed:", error);
  console.error("❌ Error details:", error.message);

  // This will help you debug the exact issue
  if (error.message.includes('API key')) {
    console.error("🔑 API Key issue - check your .env file and Firebase console");
  } else if (error.message.includes('project')) {
    console.error("🏗️ Project ID issue - verify project ID in Firebase console");
  } else if (error.message.includes('auth')) {
    console.error("🔐 Auth domain issue - check authDomain in Firebase console");
  }

  throw new Error(`Firebase initialization failed: ${error.message}`);
}

// Export Firebase services
export { firebaseApp as app, auth, db, storage, functions };
// Export firestore alias for compatibility with existing imports
export { db as firestore };

// Auth helper functions
export const getAuthInstance = (): Auth => {
  if (!auth) {
    throw new Error("Firebase Auth not initialized");
  }
  return auth;
};

export const getCurrentUserSafely = () => {
  try {
    return auth?.currentUser || null;
  } catch (error: any) {
    console.warn("⚠️ Error getting current user:", error.message);
    return null;
  }
};

export const isAuthReady = (): boolean => {
  return !!auth;
};

// Auth state management
export const waitForAuth = async (timeoutMs: number = 5000): Promise<Auth> => {
  return new Promise((resolve, reject) => {
    if (auth) {
      resolve(auth);
      return;
    }

    const timeout = setTimeout(() => {
      reject(new Error(`Auth initialization timeout after ${timeoutMs}ms`));
    }, timeoutMs);

    // Check periodically for auth
    const checkAuth = () => {
      if (auth) {
        clearTimeout(timeout);
        resolve(auth);
      } else {
        setTimeout(checkAuth, 100);
      }
    };

    checkAuth();
  });
};

// Platform info
export const getPlatformInfo = () => {
  return {
    platform: Platform.OS,
    authReady: isAuthReady(),
    persistence: Platform.OS === "web" ? "IndexedDB" : "AsyncStorage",
    appName: firebaseApp?.name || "Unknown",
    version: "1.0.0", // App version
  };
};

// Firebase initialization complete

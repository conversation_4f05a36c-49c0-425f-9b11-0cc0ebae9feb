{"cli": {"version": ">= 12.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"gradleCommand": ":app:assembleDebug", "buildType": "apk"}, "ios": {"buildConfiguration": "Debug", "simulator": false}, "env": {"APP_VARIANT": "development"}}, "standalone": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"android": {"buildType": "aab"}}}, "submit": {"production": {}}}
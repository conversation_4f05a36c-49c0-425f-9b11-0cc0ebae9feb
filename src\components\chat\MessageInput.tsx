// 🚀 MESSAGE INPUT COMPONENT
// Handles message composition, voice recording, and media attachments

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

interface MessageInputProps {
  onSendMessage: (_text: string) => void;
  onSendVoiceMessage: (_audioUri: string, _duration: number) => void;
  onSendMedia: (_mediaUri: string, _type: 'image' | 'video') => void;
  isTyping: boolean;
  onTypingStart: () => void;
  onTypingStop: () => void;
  replyingTo?: any;
  onCancelReply: () => void;
}

const COLORS = {
  primary: '#87CEEB',
  background: '#f8f9fa',
  text: '#333',
  textMuted: '#666',
  border: '#e1e5e9',
  white: '#ffffff',
};

export const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  onSendVoiceMessage,
  onSendMedia,
  isTyping,
  onTypingStart,
  onTypingStop,
  replyingTo,
  onCancelReply,
}) => {
  const [messageText, setMessageText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  
  const recordingAnim = useRef(new Animated.Value(1)).current;
  const attachmentAnim = useRef(new Animated.Value(0)).current;

  const handleSend = () => {
    if (messageText.trim()) {
      onSendMessage(messageText.trim());
      setMessageText('');
      onTypingStop();
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleTextChange = (text: string) => {
    setMessageText(text);
    if (text.length > 0 && !isTyping) {
      onTypingStart();
    } else if (text.length === 0 && isTyping) {
      onTypingStop();
    }
  };

  const startRecording = async () => {
    try {
      setIsRecording(true);
      Animated.loop(
        Animated.sequence([
          Animated.timing(recordingAnim, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(recordingAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();
      
      // Voice recording implementation would go here
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } catch (error) {
      console.error('❌ Error starting recording:', error);
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const stopRecording = async () => {
    try {
      setIsRecording(false);
      recordingAnim.stopAnimation();
      recordingAnim.setValue(1);
      
      // Mock voice message for now
      const mockAudioUri = 'mock-audio-uri';
      const mockDuration = 5;
      onSendVoiceMessage(mockAudioUri, mockDuration);
      
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('❌ Error stopping recording:', error);
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const toggleAttachmentMenu = () => {
    const toValue = showAttachmentMenu ? 0 : 1;
    setShowAttachmentMenu(!showAttachmentMenu);
    
    Animated.spring(attachmentAnim, {
      toValue,
      useNativeDriver: true,
    }).start();
  };

  const selectImage = async () => {
    try {
      // Image picker implementation would go here
      const mockImageUri = 'mock-image-uri';
      onSendMedia(mockImageUri, 'image');
      setShowAttachmentMenu(false);
    } catch (error) {
      console.error('❌ Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const selectVideo = async () => {
    try {
      // Video picker implementation would go here
      const mockVideoUri = 'mock-video-uri';
      onSendMedia(mockVideoUri, 'video');
      setShowAttachmentMenu(false);
    } catch (error) {
      console.error('❌ Error selecting video:', error);
      Alert.alert('Error', 'Failed to select video');
    }
  };

  return (
    <View style={styles.container}>
      {replyingTo && (
        <View style={styles.replyContainer}>
          <View style={styles.replyContent}>
            <Text style={styles.replyLabel}>Replying to {replyingTo.senderName}</Text>
            <Text style={styles.replyText} numberOfLines={1}>
              {replyingTo.text}
            </Text>
          </View>
          <TouchableOpacity onPress={onCancelReply} style={styles.cancelReply}>
            <Ionicons name="close" size={20} color={COLORS.textMuted} />
          </TouchableOpacity>
        </View>
      )}
      
      <View style={styles.inputContainer}>
        <TouchableOpacity
          style={styles.attachmentButton}
          onPress={toggleAttachmentMenu}
        >
          <Ionicons name="add" size={24} color={COLORS.primary} />
        </TouchableOpacity>

        <TextInput
          style={styles.textInput}
          placeholder="Type a message..."
          placeholderTextColor={COLORS.textMuted}
          value={messageText}
          onChangeText={handleTextChange}
          multiline
          maxLength={1000}
        />

        {messageText.trim() ? (
          <TouchableOpacity style={styles.sendButton} onPress={handleSend}>
            <Ionicons name="send" size={20} color="white" />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.voiceButton, isRecording && styles.recording]}
            onPressIn={startRecording}
            onPressOut={stopRecording}
          >
            <Animated.View style={{ transform: [{ scale: recordingAnim }] }}>
              <Ionicons 
                name={isRecording ? "stop" : "mic"} 
                size={20} 
                color="white" 
              />
            </Animated.View>
          </TouchableOpacity>
        )}
      </View>

      {showAttachmentMenu && (
        <Animated.View 
          style={[
            styles.attachmentMenu,
            { transform: [{ scale: attachmentAnim }] }
          ]}
        >
          <TouchableOpacity style={styles.attachmentOption} onPress={selectImage}>
            <Ionicons name="image" size={24} color={COLORS.primary} />
            <Text style={styles.attachmentText}>Photo</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.attachmentOption} onPress={selectVideo}>
            <Ionicons name="videocam" size={24} color={COLORS.primary} />
            <Text style={styles.attachmentText}>Video</Text>
          </TouchableOpacity>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  replyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  replyContent: {
    flex: 1,
  },
  replyLabel: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  replyText: {
    fontSize: 14,
    color: COLORS.text,
    marginTop: 2,
  },
  cancelReply: {
    padding: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: COLORS.background,
    borderRadius: 25,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  attachmentButton: {
    padding: 8,
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    maxHeight: 100,
    paddingVertical: 8,
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  voiceButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  recording: {
    backgroundColor: '#ff4444',
  },
  attachmentMenu: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 16,
    backgroundColor: COLORS.background,
    borderRadius: 12,
    marginTop: 8,
  },
  attachmentOption: {
    alignItems: 'center',
    padding: 16,
  },
  attachmentText: {
    fontSize: 12,
    color: COLORS.text,
    marginTop: 4,
  },
});

// 💬 REAL CHAT LIST - Shows actual conversations!
// Fixed: Single export default, proper dependencies, optimized performance

import React, { useEffect, useState, useCallback, useMemo, useRef } from "react";
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
  Image,
  StyleSheet,
  Animated,
  Dimensions,
  Alert,
} from "react-native";
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useSelector } from "react-redux";
import ErrorBoundary from "../../src/components/ErrorBoundary";
import { MainHeader } from "../../src/components/MainHeader";
import { IraChatWallpaper } from "../../src/components/ui/IraChatWallpaper";
import { RootState } from "../../src/redux/store";
import { realTimeMessagingService, RealChat } from "../../src/services/realTimeMessagingService";
import { navigationService } from "../../src/services/navigationService";
import { formatChatTime } from "../../src/utils/dateUtils";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS, ANIMATIONS } from "../../src/styles/iraChatDesignSystem";

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Beautiful Animated Chat Item Component with React.memo
const ChatItem = React.memo(function ChatItem({
  chat,
  currentUserId,
  onPress,
  index
}: {
  chat: RealChat;
  currentUserId: string;
  onPress: (chat: RealChat) => void;
  index: number;
}) {
  const scaleAnimation = useRef(new Animated.Value(1)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;

  // Entrance animation
  useEffect(() => {
    Animated.sequence([
      Animated.delay(index * 100), // Stagger animation
      Animated.parallel([
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: ANIMATIONS.normal,
          useNativeDriver: true,
        }),
        Animated.spring(slideAnimation, {
          toValue: 0,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, [index]);

  // Memoize expensive calculations
  const chatData = useMemo(() => {
    const otherUserId = chat.participants.find(id => id !== currentUserId);
    const otherUserName = otherUserId ? chat.participantNames[otherUserId] : 'Unknown User';
    const otherUserAvatar = otherUserId ? chat.participantAvatars[otherUserId] : '';
    const unreadCount = chat.unreadCount[currentUserId] || 0;
    const formattedTime = formatChatTime(chat.lastMessageTime);

    return {
      otherUserId,
      otherUserName,
      otherUserAvatar,
      unreadCount,
      formattedTime,
    };
  }, [chat, currentUserId]);

  const handlePressIn = () => {
    Animated.spring(scaleAnimation, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnimation, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View
      style={[
        styles.chatItemContainer,
        {
          opacity: fadeAnimation,
          transform: [
            { translateX: slideAnimation },
            { scale: scaleAnimation }
          ],
        },
      ]}
    >
      <TouchableOpacity
        style={styles.chatItem}
        onPress={() => onPress(chat)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <View style={styles.avatarContainer}>
          <Image
            source={{
              uri: chatData.otherUserAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(chatData.otherUserName)}&background=87CEEB&color=fff`
            }}
            style={styles.chatAvatar}
          />
          {chatData.unreadCount > 0 && (
            <View style={styles.onlineIndicator} />
          )}
        </View>

        <View style={styles.chatContent}>
          <View style={styles.chatHeader}>
            <Text style={styles.chatName} numberOfLines={1}>
              {chatData.otherUserName}
            </Text>
            <Text style={styles.chatTime}>{chatData.formattedTime}</Text>
          </View>

          <View style={styles.chatFooter}>
            <Text style={styles.lastMessage} numberOfLines={1}>
              {chat.lastMessage?.content || 'No messages yet'}
            </Text>
            {chatData.unreadCount > 0 && (
              <Animated.View style={styles.unreadBadge}>
                <Text style={styles.unreadText}>
                  {chatData.unreadCount > 99 ? '99+' : chatData.unreadCount}
                </Text>
              </Animated.View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
});

// Main Chat List Screen - SINGLE EXPORT DEFAULT
export default function ChatListScreen() {
  const router = useRouter();
  const [chats, setChats] = useState<RealChat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Enhanced Chat Management State
  const [searchQuery, setSearchQuery] = useState("");
  const [showSearch, setShowSearch] = useState(false);
  const [selectedChats, setSelectedChats] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [filterType, setFilterType] = useState<'all' | 'unread' | 'groups' | 'archived'>('all');
  const [pinnedChats, setPinnedChats] = useState<string[]>([]);
  const [archivedChats, setArchivedChats] = useState<string[]>([]);

  // Get current user from Redux
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  console.log('💬 Chat List loaded for user:', currentUser?.id);

  // Subscribe to user's chats - FIXED DEPENDENCIES
  useEffect(() => {
    if (!currentUser?.id) {
      console.log('❌ No current user, cannot load chats');
      setIsLoading(false);
      return;
    }

    console.log('👂 Subscribing to user chats:', currentUser.id);
    setIsLoading(true);

    const unsubscribe = realTimeMessagingService.subscribeToUserChats(
      currentUser.id,
      (userChats: RealChat[]) => {
        console.log('📨 Received user chats:', userChats.length);
        setChats(userChats);
        setIsLoading(false);
      }
    );

    return () => {
      console.log('🧹 Cleaning up chat subscription');
      unsubscribe();
    };
  }, [currentUser?.id]); // FIXED: Proper dependency

  // Handle refresh - useCallback for performance
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    // Refresh happens automatically via real-time subscription
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  // Navigate to chat - useCallback for performance
  const openChat = useCallback(async (chat: RealChat) => {
    if (!currentUser) return;

    console.log('🔗 Opening chat:', { chatId: chat.id, isGroup: chat.isGroup });

    // Use navigation service for seamless routing
    navigationService.openChat(chat.id, chat.isGroup);
  }, [currentUser]);

  // Start new chat - useCallback for performance
  const startNewChat = useCallback(() => {
    navigationService.openNewChat();
  }, []);

  // Enhanced Chat Management Functions
  const toggleChatSelection = useCallback((chatId: string) => {
    setSelectedChats(prev => {
      const isSelected = prev.includes(chatId);
      if (isSelected) {
        const newSelection = prev.filter(id => id !== chatId);
        if (newSelection.length === 0) {
          setIsSelectionMode(false);
        }
        return newSelection;
      } else {
        return [...prev, chatId];
      }
    });
  }, []);

  const enterSelectionMode = useCallback((chatId: string) => {
    setIsSelectionMode(true);
    setSelectedChats([chatId]);
  }, []);

  const exitSelectionMode = useCallback(() => {
    setIsSelectionMode(false);
    setSelectedChats([]);
  }, []);

  const pinSelectedChats = useCallback(async () => {
    try {
      setPinnedChats(prev => [...new Set([...prev, ...selectedChats])]);
      // Update in Firebase
      // await realTimeMessagingService.pinChats(currentUser?.id, selectedChats);
      exitSelectionMode();
      Alert.alert('Success', 'Chats pinned successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to pin chats');
    }
  }, [selectedChats, exitSelectionMode]);

  const archiveSelectedChats = useCallback(async () => {
    try {
      setArchivedChats(prev => [...new Set([...prev, ...selectedChats])]);
      // Update in Firebase
      // await realTimeMessagingService.archiveChats(currentUser?.id, selectedChats);
      exitSelectionMode();
      Alert.alert('Success', 'Chats archived successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to archive chats');
    }
  }, [selectedChats, exitSelectionMode]);

  const deleteSelectedChats = useCallback(async () => {
    Alert.alert(
      'Delete Chats',
      `Are you sure you want to delete ${selectedChats.length} chat(s)?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Delete from Firebase
              // await realTimeMessagingService.deleteChats(currentUser?.id, selectedChats);
              setChats(prev => prev.filter(chat => !selectedChats.includes(chat.id)));
              exitSelectionMode();
              Alert.alert('Success', 'Chats deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete chats');
            }
          }
        }
      ]
    );
  }, [selectedChats, exitSelectionMode]);

  const markSelectedAsRead = useCallback(async () => {
    try {
      // Mark as read in Firebase
      // await realTimeMessagingService.markChatsAsRead(currentUser?.id, selectedChats);
      setChats(prev =>
        prev.map(chat =>
          selectedChats.includes(chat.id)
            ? { ...chat, unreadCount: { ...chat.unreadCount, [currentUser?.id || '']: 0 } }
            : chat
        )
      );
      exitSelectionMode();
      Alert.alert('Success', 'Chats marked as read');
    } catch (error) {
      Alert.alert('Error', 'Failed to mark chats as read');
    }
  }, [selectedChats, currentUser?.id, exitSelectionMode]);

  // Enhanced Filtering and Search
  const filteredAndSortedChats = useMemo(() => {
    let filtered = chats;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(chat => {
        const otherUserId = chat.participants.find(id => id !== currentUser?.id);
        const chatName = chat.isGroup
          ? chat.groupName || 'Group Chat'
          : otherUserId
            ? chat.participantNames[otherUserId] || 'Unknown'
            : 'Unknown';

        const lastMessageText = typeof chat.lastMessage === 'string'
          ? chat.lastMessage
          : chat.lastMessage && typeof chat.lastMessage === 'object' && 'text' in chat.lastMessage
            ? String((chat.lastMessage as any).text || '')
            : '';

        return chatName.toLowerCase().includes(query) ||
               lastMessageText.toLowerCase().includes(query);
      });
    }

    // Apply type filter
    switch (filterType) {
      case 'unread':
        filtered = filtered.filter(chat =>
          (chat.unreadCount[currentUser?.id || ''] || 0) > 0
        );
        break;
      case 'groups':
        filtered = filtered.filter(chat => chat.isGroup);
        break;
      case 'archived':
        filtered = filtered.filter(chat => archivedChats.includes(chat.id));
        break;
      default:
        // 'all' - exclude archived chats from main view
        filtered = filtered.filter(chat => !archivedChats.includes(chat.id));
        break;
    }

    // Sort: pinned chats first, then by last message time
    return filtered.sort((a, b) => {
      const aIsPinned = pinnedChats.includes(a.id);
      const bIsPinned = pinnedChats.includes(b.id);

      if (aIsPinned && !bIsPinned) return -1;
      if (!aIsPinned && bIsPinned) return 1;

      // Both pinned or both not pinned - sort by time
      return new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime();
    });
  }, [chats, searchQuery, filterType, currentUser?.id, archivedChats, pinnedChats]);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const toggleFilter = useCallback((type: typeof filterType) => {
    setFilterType(current => current === type ? 'all' : type);
  }, []);

  // Render chat item - useCallback for FlatList performance
  const renderChatItem = useCallback(({ item, index }: { item: RealChat; index: number }) => {
    if (!currentUser) return null;

    return (
      <ChatItem
        chat={item}
        currentUserId={currentUser.id}
        onPress={openChat}
        index={index}
      />
    );
  }, [currentUser, openChat]);

  // Key extractor - useCallback for FlatList performance
  const keyExtractor = useCallback((item: RealChat) => item.id, []);

  // Render empty state
  const renderEmptyState = useCallback(() => (
    <View style={styles.emptyState}>
      <Ionicons name="chatbubbles-outline" size={80} color="#9CA3AF" />
      <Text style={styles.emptyTitle}>No conversations yet</Text>
      <Text style={styles.emptySubtitle}>
        Start a conversation with your contacts
      </Text>
      <TouchableOpacity style={styles.startChatButton} onPress={startNewChat}>
        <Ionicons name="add" size={24} color="#FFFFFF" />
        <Text style={styles.startChatText}>Start New Chat</Text>
      </TouchableOpacity>
    </View>
  ), [startNewChat]);

  return (
    <ErrorBoundary>
      <View style={styles.container}>
        <StatusBar style="light" />

        {/* Beautiful animated wallpaper */}
        <IraChatWallpaper variant="chat" animated={true} />

        <MainHeader />

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={IRACHAT_COLORS.primary} />
            <Text style={styles.loadingText}>Loading chats...</Text>
          </View>
        ) : (
          <FlatList
            data={filteredAndSortedChats}
            renderItem={renderChatItem}
            keyExtractor={keyExtractor}
            contentContainerStyle={filteredAndSortedChats.length === 0 ? styles.emptyContainer : styles.chatsList}
            ListEmptyComponent={renderEmptyState}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[IRACHAT_COLORS.primary]}
                tintColor={IRACHAT_COLORS.primary}
              />
            }
            showsVerticalScrollIndicator={false}
            // Performance optimizations
            removeClippedSubviews={true}
            maxToRenderPerBatch={10}
            windowSize={10}
            getItemLayout={(data, index) => ({
              length: 80, // Updated height for new design
              offset: 80 * index,
              index,
            })}
          />
        )}

        {/* Beautiful Floating Action Button */}
        <Animated.View style={styles.fabContainer}>
          <LinearGradient
            colors={IRACHAT_COLORS.primaryGradient as any}
            style={styles.fab}
          >
            <TouchableOpacity style={styles.fabButton} onPress={startNewChat}>
              <Ionicons name="add" size={28} color={IRACHAT_COLORS.textOnPrimary} />
            </TouchableOpacity>
          </LinearGradient>
        </Animated.View>
      </View>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  emptyContainer: {
    flex: 1,
  },
  chatsList: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.sm,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
    backgroundColor: 'transparent',
  },
  emptyTitle: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    color: IRACHAT_COLORS.text,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: TYPOGRAPHY.lineHeight.relaxed * TYPOGRAPHY.fontSize.base,
  },
  startChatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.primary,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.full,
    ...SHADOWS.md,
  },
  startChatText: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontSize: TYPOGRAPHY.fontSize.base,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    marginLeft: SPACING.sm,
  },
  chatItemContainer: {
    marginHorizontal: SPACING.sm,
    marginVertical: SPACING.xs,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.surface,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    height: 80,
    ...SHADOWS.sm,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: SPACING.md,
  },
  chatAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.primaryLight,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: IRACHAT_COLORS.online,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.surface,
  },
  chatContent: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  chatName: {
    fontSize: TYPOGRAPHY.fontSize.base,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    color: IRACHAT_COLORS.text,
    flex: 1,
  },
  chatTime: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
  },
  chatFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    flex: 1,
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    marginRight: SPACING.sm,
  },
  unreadBadge: {
    backgroundColor: IRACHAT_COLORS.primary,
    borderRadius: BORDER_RADIUS.full,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xs,
    ...SHADOWS.sm,
  },
  unreadText: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
  },
  fabContainer: {
    position: 'absolute',
    bottom: SPACING.xl,
    right: SPACING.lg,
  },
  fab: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.lg,
  },
  fabButton: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 32,
  },
});
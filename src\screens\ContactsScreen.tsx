import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useState, useEffect } from "react";
import {
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View,
    ActivityIndicator
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import optimizedContactsService from "../services/optimizedContactsService";
import { navigationService } from "../services/navigationService";
import { Avatar } from "../components/Avatar";

interface Contact {
  id: string;
  name: string;
  phoneNumber: string;
  avatar?: string;
  hasIraChat: boolean;
  isOnline?: boolean;
  lastSeen?: Date;
}

const ContactsScreen = () => {
  const router = useRouter();
  const [searchText, setSearchText] = useState("");
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);

  // Load real contacts on component mount
  useEffect(() => {
    loadContacts();
  }, []);

  const loadContacts = async () => {
    try {
      setLoading(true);
      console.log('📱 Loading real contacts from optimized service...');

      // Get registered IraChat contacts
      const realContacts = await optimizedContactsService.getRegisteredContacts();

      // Format contacts for this screen
      const formattedContacts: Contact[] = realContacts.map(contact => ({
        id: contact.id || contact.userId || Math.random().toString(),
        name: contact.name || 'Unknown',
        phoneNumber: contact.phoneNumber || '',
        avatar: contact.avatar,
        hasIraChat: true, // All contacts from this service are IraChat users
        isOnline: contact.isOnline,
        lastSeen: contact.lastSeen
      }));

      setContacts(formattedContacts);
      setFilteredContacts(formattedContacts);
      console.log('✅ Loaded', formattedContacts.length, 'IraChat contacts');
    } catch (error) {
      console.error('❌ Error loading contacts:', error);
      setContacts([]);
      setFilteredContacts([]);
    } finally {
      setLoading(false);
    }
  };

  const formatLastSeen = (lastSeen: Date | string | undefined): string => {
    try {
      if (!lastSeen) {
        return "Unknown";
      }

      let date: Date;
      if (lastSeen instanceof Date) {
        date = lastSeen;
      } else if (typeof lastSeen === "string") {
        date = new Date(lastSeen);
      } else {
        return "Unknown";
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        return "Unknown";
      }

      const now = new Date();
      const diff = now.getTime() - date.getTime();
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(diff / 3600000);
      const days = Math.floor(diff / 86400000);

      if (minutes < 1) return "just now";
      if (minutes < 60) return `${minutes}m ago`;
      if (hours < 24) return `${hours}h ago`;
      return `${days}d ago`;
    } catch (error) {
      console.error("Error formatting last seen:", error);
      return "Unknown";
    }
  };

  const handleSearch = (text: string) => {
    setSearchText(text);
    if (text.trim() === "") {
      // Show all IraChat contacts
      setFilteredContacts(contacts);
    } else {
      // Search among IraChat contacts
      const filtered = contacts.filter((contact) =>
        contact.name.toLowerCase().includes(text.toLowerCase()) ||
        contact.phoneNumber.includes(text)
      );
      setFilteredContacts(filtered);
    }
  };

  const openChat = (contact: Contact) => {
    console.log("🚀 Opening chat with contact:", contact.name);

    // Use navigation service for consistent routing
    navigationService.openChat(contact.id, false);
  };

  const renderContact = (contact: any, index: number) => {
    return (
      <TouchableOpacity
        key={contact.id}
        onPress={() => openChat(contact)}
        className="flex-row items-center px-4 py-3 border-b border-gray-100"
        activeOpacity={0.7}
        style={{ zIndex: 1000 - index }} // Use index for layering
      >
        <View style={{ position: 'relative' }}>
          <Avatar
                name={contact.name}
                imageUrl={contact.avatar}
                size="medium"
                showOnlineStatus={true}
                isOnline={contact.isOnline}
              />
        </View>

        <View className="flex-1 ml-3">
          <Text className="text-gray-900 font-semibold text-base">
            {contact.name}
          </Text>
          <View className="flex-row items-center mt-1">
            {contact.isOnline ? (
              <Text className="text-green-600 text-sm font-medium">Online</Text>
            ) : (
              <Text className="text-gray-500 text-sm">
                Last seen {formatLastSeen(contact.lastSeen)}
              </Text>
            )}
          </View>
        </View>

        <View className="items-center">
          <View className="bg-sky-100 rounded-full p-2">
            <Ionicons name="chatbubble" size={16} color="#0ea5e9" />
          </View>
          <Text className="text-xs text-sky-600 mt-1 font-medium">IraChat</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center px-4 py-3 bg-sky-500 shadow-sm">
        <TouchableOpacity onPress={() => router.back()} className="mr-3">
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>

        <View className="flex-1">
          <Text className="text-white font-semibold text-lg">
            Select Contact
          </Text>
          <Text className="text-white/80 text-sm">
            {filteredContacts.length} IraChat users
          </Text>
        </View>
      </View>

      {/* Search Bar */}
      <View className="px-4 py-3 bg-gray-50 border-b border-gray-200">
        <View className="flex-row items-center bg-white rounded-full px-4 py-2 border border-gray-200">
          <Ionicons name="search" size={20} color="#6b7280" />
          <TextInput
            value={searchText}
            onChangeText={handleSearch}
            placeholder="Search contacts..."
            className="flex-1 ml-3 text-base"
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={() => handleSearch("")}>
              <Ionicons name="close-circle" size={20} color="#6b7280" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Contacts List */}
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {loading ? (
          <View className="flex-1 items-center justify-center px-4 py-20">
            <ActivityIndicator size="large" color="#0ea5e9" />
            <Text className="text-gray-500 text-base mt-4">
              Loading contacts...
            </Text>
          </View>
        ) : filteredContacts.length > 0 ? (
          <>
            <View className="px-4 py-3 bg-gray-50">
              <Text className="text-gray-600 text-sm font-medium">
                CONTACTS ON IRACHAT ({filteredContacts.length})
              </Text>
            </View>

            {filteredContacts.map((contact, index) =>
              renderContact(contact, index),
            )}
          </>
        ) : (
          <View className="flex-1 items-center justify-center px-4">
            <Ionicons name="people-outline" size={64} color="#d1d5db" />
            <Text className="text-gray-500 text-lg font-medium mt-4">
              {searchText ? 'No contacts found' : 'No IraChat contacts'}
            </Text>
            <Text className="text-gray-400 text-sm mt-2 text-center">
              {searchText ? 'Try a different search term' : 'Invite friends to join IraChat'}
            </Text>
            <Text className="text-gray-400 text-center mt-2">
              Try searching with a different name
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Invite Friends Button */}
      <View className="px-4 py-3 bg-gray-50 border-t border-gray-200">
        <TouchableOpacity className="flex-row items-center justify-center bg-sky-500 rounded-full py-3">
          <Ionicons name="person-add" size={20} color="white" />
          <Text className="text-white font-semibold ml-2">
            Invite Friends to IraChat
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default ContactsScreen;

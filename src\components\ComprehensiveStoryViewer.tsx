// 🎬 COMPREHENSIVE STORY VIEWER
// Full-screen story viewing with advanced features
// Progress indicators, reactions, replies, and analytics

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Modal,
  StatusBar,
  Animated,
  Image,
  TextInput,
  Alert,
  SafeAreaView,
} from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import { Video, ResizeMode } from 'expo-av';
import { LinearGradient } from 'expo-linear-gradient';
import { Story, Reaction } from '../types/Update';
import { comprehensiveUpdatesService } from '../services/comprehensiveUpdatesService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface ComprehensiveStoryViewerProps {
  visible: boolean;
  stories: Story[];
  initialIndex: number;
  currentUserId: string;
  onClose: () => void;
  onStoryChange?: (index: number) => void;
}

export const ComprehensiveStoryViewer: React.FC<ComprehensiveStoryViewerProps> = ({
  visible,
  stories,
  initialIndex,
  currentUserId,
  onClose,
  onStoryChange,
}) => {
  // ==================== STATE MANAGEMENT ====================
  
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [progress, setProgress] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [showReactions, setShowReactions] = useState(false);
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [replyText, setReplyText] = useState('');
  const [reactions, setReactions] = useState<Reaction[]>([]);

  // Animation refs
  const progressAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const reactionAnim = useRef(new Animated.Value(0)).current;
  const panRef = useRef<any>(null);

  // Timer refs
  const progressTimer = useRef<NodeJS.Timeout | null>(null);
  const storyDuration = 5000; // 5 seconds per story

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (visible && stories.length > 0) {
      setCurrentIndex(initialIndex);
      startStoryProgress();
      trackStoryView();
    }

    return () => {
      stopStoryProgress();
    };
  }, [visible, initialIndex]);

  useEffect(() => {
    if (visible) {
      startStoryProgress();
    }
  }, [currentIndex, isPaused]);

  // ==================== STORY PROGRESS MANAGEMENT ====================

  const startStoryProgress = () => {
    stopStoryProgress();
    
    if (isPaused) return;

    progressAnim.setValue(0);
    setProgress(0);

    const startTime = Date.now();
    progressTimer.current = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min(elapsed / storyDuration, 1);
      
      setProgress(newProgress);
      progressAnim.setValue(newProgress);

      if (newProgress >= 1) {
        nextStory();
      }
    }, 50);
  };

  const stopStoryProgress = () => {
    if (progressTimer.current) {
      clearInterval(progressTimer.current);
      progressTimer.current = null;
    }
  };

  const pauseStory = () => {
    setIsPaused(true);
    stopStoryProgress();
  };

  const resumeStory = () => {
    setIsPaused(false);
    startStoryProgress();
  };

  // ==================== NAVIGATION ====================

  const nextStory = () => {
    if (currentIndex < stories.length - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      onStoryChange?.(newIndex);
    } else {
      onClose();
    }
  };

  const previousStory = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      onStoryChange?.(newIndex);
    }
  };

  // ==================== INTERACTIONS ====================

  const handleDoubleTap = useCallback(() => {
    // Double tap to like story
    handleLike();
    
    // Animate heart
    Animated.sequence([
      Animated.timing(scaleAnim, { toValue: 1.5, duration: 200, useNativeDriver: true }),
      Animated.timing(scaleAnim, { toValue: 1, duration: 200, useNativeDriver: true }),
    ]).start();
  }, []);

  const handleLike = async () => {
    const currentStory = stories[currentIndex];
    if (!currentStory) return;

    try {
      const result = await comprehensiveUpdatesService.toggleLike(
        currentStory.id,
        currentUserId,
        'Current User', // This should come from user context
        undefined
      );

      if (result.success) {
        // Update local story state
        // This would typically update the parent component's state
      }
    } catch (error) {
      console.error('❌ Error liking story:', error);
    }
  };

  const handleReaction = async (emoji: string) => {
    const currentStory = stories[currentIndex];
    if (!currentStory) return;

    try {
      // Add reaction with animation
      const newReaction: Reaction = {
        id: `reaction_${Date.now()}`,
        userId: currentUserId,
        userName: 'Current User',
        emoji,
        timestamp: new Date(),
      };

      setReactions(prev => [...prev, newReaction]);

      // Animate reaction
      Animated.sequence([
        Animated.timing(reactionAnim, { toValue: 1, duration: 300, useNativeDriver: true }),
        Animated.delay(2000),
        Animated.timing(reactionAnim, { toValue: 0, duration: 300, useNativeDriver: true }),
      ]).start(() => {
        setReactions(prev => prev.filter(r => r.id !== newReaction.id));
      });

      setShowReactions(false);
    } catch (error) {
      console.error('❌ Error adding reaction:', error);
    }
  };

  const handleReply = async () => {
    if (!replyText.trim()) return;

    const currentStory = stories[currentIndex];
    if (!currentStory) return;

    try {
      // Send private reply to story owner
      // This would typically use a messaging service
      Alert.alert('Reply Sent', 'Your reply has been sent privately');
      setReplyText('');
      setShowReplyInput(false);
    } catch (error) {
      console.error('❌ Error sending reply:', error);
      Alert.alert('Error', 'Failed to send reply');
    }
  };

  const trackStoryView = async () => {
    const currentStory = stories[currentIndex];
    if (!currentStory) return;

    try {
      await comprehensiveUpdatesService.viewUpdate(
        currentStory.id,
        currentUserId,
        'Current User',
        undefined
      );
    } catch (error) {
      console.error('❌ Error tracking story view:', error);
    }
  };

  // ==================== GESTURE HANDLERS ====================

  const onPanGestureEvent = Animated.event(
    [{ nativeEvent: { translationY: reactionAnim } }],
    { useNativeDriver: true }
  );

  const onPanHandlerStateChange = (event: any) => {
    if (event.nativeEvent.state === State.END) {
      const { translationY, velocityY } = event.nativeEvent;
      
      if (translationY > 100 || velocityY > 500) {
        // Swipe down to close
        onClose();
      } else {
        // Snap back
        Animated.spring(reactionAnim, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  // ==================== RENDER METHODS ====================

  const renderProgressBars = () => (
    <View style={styles.progressContainer}>
      {stories.map((_, index) => (
        <View key={index} style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground} />
          <Animated.View
            style={[
              styles.progressBarFill,
              {
                width: index === currentIndex 
                  ? progressAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0%', '100%'],
                    })
                  : index < currentIndex ? '100%' : '0%'
              }
            ]}
          />
        </View>
      ))}
    </View>
  );

  const renderStoryHeader = () => {
    const currentStory = stories[currentIndex];
    if (!currentStory) return null;

    return (
      <View style={styles.storyHeader}>
        <View style={styles.userInfo}>
          <Image 
            source={{ uri: currentStory.userAvatar || 'https://via.placeholder.com/40' }} 
            style={styles.userAvatar} 
          />
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{currentStory.userName}</Text>
            <Text style={styles.storyTime}>
              {Math.floor((Date.now() - currentStory.timestamp.getTime()) / (1000 * 60 * 60))}h
            </Text>
          </View>
        </View>
        
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color="white" />
        </TouchableOpacity>
      </View>
    );
  };

  const renderStoryContent = () => {
    const currentStory = stories[currentIndex];
    if (!currentStory) return null;

    const media = currentStory.media[0];
    if (!media) return null;

    return (
      <View style={styles.storyContent}>
        {media.type === 'video' ? (
          <Video
            source={{ uri: media.url }}
            style={styles.storyMedia}
            resizeMode={ResizeMode.COVER}
            shouldPlay={!isPaused}
            isLooping={false}
            onPlaybackStatusUpdate={(status: any) => {
              if (status.didJustFinish) {
                nextStory();
              }
            }}
          />
        ) : (
          <Image source={{ uri: media.url }} style={styles.storyMedia} />
        )}

        {/* Story Caption */}
        {currentStory.caption && (
          <View style={styles.captionContainer}>
            <Text style={styles.captionText}>{currentStory.caption}</Text>
          </View>
        )}

        {/* Touch Areas for Navigation */}
        <TouchableOpacity 
          style={styles.leftTouchArea} 
          onPress={previousStory}
          onLongPress={pauseStory}
          onPressOut={resumeStory}
        />
        <TouchableOpacity 
          style={styles.rightTouchArea} 
          onPress={nextStory}
          onLongPress={pauseStory}
          onPressOut={resumeStory}
        />
      </View>
    );
  };

  const renderStoryActions = () => (
    <View style={styles.storyActions}>
      <TouchableOpacity 
        style={styles.actionButton}
        onPress={() => setShowReactions(true)}
      >
        <Ionicons name="heart-outline" size={24} color="white" />
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.actionButton}
        onPress={() => setShowReplyInput(true)}
      >
        <Ionicons name="chatbubble-outline" size={24} color="white" />
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.actionButton}>
        <Ionicons name="share-outline" size={24} color="white" />
      </TouchableOpacity>
    </View>
  );

  const renderReactionPicker = () => (
    <Modal visible={showReactions} transparent animationType="slide">
      <View style={styles.reactionModal}>
        <TouchableOpacity 
          style={styles.reactionBackdrop} 
          onPress={() => setShowReactions(false)} 
        />
        <View style={styles.reactionPicker}>
          {['❤️', '😂', '😮', '😢', '😡', '👍', '👎', '🔥'].map((emoji) => (
            <TouchableOpacity
              key={emoji}
              style={styles.reactionButton}
              onPress={() => handleReaction(emoji)}
            >
              <Text style={styles.reactionEmoji}>{emoji}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );

  const renderReplyInput = () => (
    <Modal visible={showReplyInput} transparent animationType="slide">
      <View style={styles.replyModal}>
        <TouchableOpacity 
          style={styles.replyBackdrop} 
          onPress={() => setShowReplyInput(false)} 
        />
        <View style={styles.replyContainer}>
          <TextInput
            style={styles.replyInput}
            placeholder="Send a message..."
            placeholderTextColor="#999"
            value={replyText}
            onChangeText={setReplyText}
            multiline
            autoFocus
          />
          <TouchableOpacity style={styles.sendButton} onPress={handleReply}>
            <Ionicons name="send" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const renderFloatingReactions = () => (
    <View style={styles.floatingReactions}>
      {reactions.map((reaction) => (
        <Animated.View
          key={reaction.id}
          style={[
            styles.floatingReaction,
            {
              opacity: reactionAnim,
              transform: [
                {
                  translateY: reactionAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, -100],
                  }),
                },
              ],
            },
          ]}
        >
          <Text style={styles.floatingReactionEmoji}>{reaction.emoji}</Text>
        </Animated.View>
      ))}
    </View>
  );

  if (!visible || stories.length === 0) return null;

  return (
    <Modal visible={visible} animationType="fade" statusBarTranslucent>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <PanGestureHandler
        ref={panRef}
        onGestureEvent={onPanGestureEvent}
        onHandlerStateChange={onPanHandlerStateChange}
      >
        <Animated.View style={styles.container}>
          <LinearGradient
            colors={['rgba(0,0,0,0.3)', 'transparent', 'rgba(0,0,0,0.3)']}
            style={styles.gradient}
          >
            {renderProgressBars()}
            {renderStoryHeader()}
            {renderStoryContent()}
            {renderStoryActions()}
            {renderFloatingReactions()}
          </LinearGradient>
        </Animated.View>
      </PanGestureHandler>
      
      {renderReactionPicker()}
      {renderReplyInput()}
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  gradient: {
    flex: 1,
  },
  progressContainer: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    paddingTop: 50,
    paddingBottom: 8,
  },
  progressBarContainer: {
    flex: 1,
    height: 2,
    backgroundColor: 'rgba(255,255,255,0.3)',
    marginHorizontal: 2,
    borderRadius: 1,
    overflow: 'hidden',
  },
  progressBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255,255,255,0.3)',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: 'white',
  },
  storyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  storyTime: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 12,
  },
  closeButton: {
    padding: 8,
  },
  storyContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  storyMedia: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    position: 'absolute',
  },
  captionContainer: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    right: 16,
  },
  captionText: {
    color: 'white',
    fontSize: 16,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  leftTouchArea: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: SCREEN_WIDTH * 0.3,
    height: '100%',
  },
  rightTouchArea: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: SCREEN_WIDTH * 0.7,
    height: '100%',
  },
  storyActions: {
    position: 'absolute',
    bottom: 50,
    right: 16,
    alignItems: 'center',
  },
  actionButton: {
    padding: 12,
    marginVertical: 8,
  },
  floatingReactions: {
    position: 'absolute',
    bottom: 200,
    left: 20,
  },
  floatingReaction: {
    marginVertical: 4,
  },
  floatingReactionEmoji: {
    fontSize: 24,
  },
  reactionModal: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  reactionBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  reactionPicker: {
    flexDirection: 'row',
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingVertical: 20,
    paddingHorizontal: 16,
    justifyContent: 'space-around',
  },
  reactionButton: {
    padding: 8,
  },
  reactionEmoji: {
    fontSize: 32,
  },
  replyModal: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  replyBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  replyContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(0,0,0,0.9)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'flex-end',
  },
  replyInput: {
    flex: 1,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    color: 'white',
    maxHeight: 100,
    marginRight: 8,
  },
  sendButton: {
    backgroundColor: '#667eea',
    borderRadius: 20,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

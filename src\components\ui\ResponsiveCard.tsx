/**
 * Responsive Card Component for IraChat
 * Beautiful animated cards with responsive design
 */

import React, { useRef, useEffect } from 'react';
import {
  Animated,
  TouchableOpacity,
  ViewStyle,
  GestureResponderEvent,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IRACHAT_COLORS, SHADOWS, ANIMATIONS } from '../../styles/iraChatDesignSystem';
import { ResponsiveScale, ResponsiveSpacing, ComponentSizes } from '../../utils/responsiveUtils';

interface ResponsiveCardProps {
  children: React.ReactNode;
  onPress?: (_event: GestureResponderEvent) => void;
  variant?: 'default' | 'elevated' | 'outlined' | 'gradient';
  style?: ViewStyle;
  animated?: boolean;
  disabled?: boolean;
  index?: number;
}

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  onPress,
  variant = 'default',
  style,
  animated = true,
  disabled = false,
  index = 0,
}) => {
  const scaleAnimation = useRef(new Animated.Value(1)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    if (animated) {
      // Entrance animation with stagger
      Animated.sequence([
        Animated.delay(index * 100),
        Animated.parallel([
          Animated.timing(fadeAnimation, {
            toValue: 1,
            duration: ANIMATIONS.normal,
            useNativeDriver: true,
          }),
          Animated.spring(slideAnimation, {
            toValue: 0,
            tension: 50,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    } else {
      fadeAnimation.setValue(1);
      slideAnimation.setValue(0);
    }
  }, [animated, index, fadeAnimation, slideAnimation]);

  const handlePressIn = () => {
    if (animated && !disabled && onPress) {
      Animated.spring(scaleAnimation, {
        toValue: 0.95,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animated && !disabled && onPress) {
      Animated.spring(scaleAnimation, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    }
  };

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: ResponsiveScale.borderRadius(16),
      padding: ResponsiveSpacing.cardSpacing,
      marginHorizontal: ResponsiveSpacing.screenPadding,
      marginVertical: ResponsiveSpacing.md,
    };

    switch (variant) {
      case 'elevated':
        return {
          ...baseStyle,
          backgroundColor: IRACHAT_COLORS.surfaceElevated,
          ...SHADOWS.lg,
        };
      case 'outlined':
        return {
          ...baseStyle,
          backgroundColor: IRACHAT_COLORS.surface,
          borderWidth: 1,
          borderColor: IRACHAT_COLORS.border,
          ...SHADOWS.sm,
        };
      case 'gradient':
        return baseStyle;
      default:
        return {
          ...baseStyle,
          backgroundColor: IRACHAT_COLORS.surface,
          ...SHADOWS.md,
        };
    }
  };

  const animatedStyle = {
    opacity: fadeAnimation,
    transform: [
      { translateY: slideAnimation },
      { scale: scaleAnimation }
    ],
  };

  const cardStyle = [getCardStyle(), style];

  if (variant === 'gradient') {
    return (
      <Animated.View style={[animatedStyle, { marginHorizontal: ResponsiveSpacing.screenPadding, marginVertical: ResponsiveSpacing.md }]}>
        <LinearGradient
          colors={IRACHAT_COLORS.skyGradient as any}
          style={cardStyle}
        >
          {onPress ? (
            <TouchableOpacity
              onPress={onPress}
              onPressIn={handlePressIn}
              onPressOut={handlePressOut}
              disabled={disabled}
              activeOpacity={1}
              style={{ borderRadius: ResponsiveScale.borderRadius(16) }}
            >
              {children}
            </TouchableOpacity>
          ) : (
            children
          )}
        </LinearGradient>
      </Animated.View>
    );
  }

  if (onPress) {
    return (
      <Animated.View style={animatedStyle}>
        <TouchableOpacity
          style={cardStyle}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled}
          activeOpacity={1}
        >
          {children}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[cardStyle, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

// Responsive List Card for chat items, contacts, etc.
interface ResponsiveListCardProps {
  children: React.ReactNode;
  onPress?: (_event: GestureResponderEvent) => void;
  style?: ViewStyle;
  animated?: boolean;
  index?: number;
  showDivider?: boolean;
}

export const ResponsiveListCard: React.FC<ResponsiveListCardProps> = ({
  children,
  onPress,
  style,
  animated = true,
  index = 0,
  showDivider = true,
}) => {
  const scaleAnimation = useRef(new Animated.Value(1)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    if (animated) {
      Animated.sequence([
        Animated.delay(index * 50),
        Animated.parallel([
          Animated.timing(fadeAnimation, {
            toValue: 1,
            duration: ANIMATIONS.fast,
            useNativeDriver: true,
          }),
          Animated.spring(slideAnimation, {
            toValue: 0,
            tension: 80,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    } else {
      fadeAnimation.setValue(1);
      slideAnimation.setValue(0);
    }
  }, [animated, index, fadeAnimation, slideAnimation]);

  const handlePressIn = () => {
    if (animated && onPress) {
      Animated.spring(scaleAnimation, {
        toValue: 0.98,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animated && onPress) {
      Animated.spring(scaleAnimation, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    }
  };

  const cardStyle: ViewStyle = {
    backgroundColor: IRACHAT_COLORS.surface,
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.md,
    minHeight: ComponentSizes.buttonHeight.large,
    justifyContent: 'center',
    ...(showDivider && {
      borderBottomWidth: 1,
      borderBottomColor: IRACHAT_COLORS.borderLight,
    }),
    ...style,
  };

  const animatedStyle = {
    opacity: fadeAnimation,
    transform: [
      { translateX: slideAnimation },
      { scale: scaleAnimation }
    ],
  };

  if (onPress) {
    return (
      <Animated.View style={animatedStyle}>
        <TouchableOpacity
          style={cardStyle}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={1}
        >
          {children}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[cardStyle, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

export default ResponsiveCard;
